"""
모델 설정 관리자
"""
import yaml
import os
from typing import Dict, Any, Optional
from dataclasses import dataclass

from src.models.ai_model_interfaces import ModelConfig, ModelType, ModelFactory


@dataclass
class AnalysisConfig:
    """분석 설정"""
    similarity_threshold: float = 0.6
    pass_threshold: float = 0.6
    grade_thresholds: Dict[str, float] = None
    score_weights: Dict[str, float] = None
    
    def __post_init__(self):
        if self.grade_thresholds is None:
            self.grade_thresholds = {'A': 0.9, 'B': 0.8, 'C': 0.7, 'D': 0.6, 'F': 0.0}
        if self.score_weights is None:
            self.score_weights = {
                'semantic_similarity': 0.25,
                'topic_relevance': 0.25,
                'keyword_overlap': 0.15,
                'answer_completeness': 0.15,
                'answer_accuracy': 0.10,
                'answer_helpfulness': 0.10
            }


class ModelConfigManager:
    """모델 설정 관리자"""
    
    def __init__(self, config_path: str = "config/model_config.yaml"):
        """
        설정 관리자 초기화
        
        Args:
            config_path: 설정 파일 경로
        """
        self.config_path = config_path
        self.config_data = None
        self.current_profile = None
        self.load_config()
    
    def load_config(self) -> None:
        """설정 파일 로드"""
        try:
            if not os.path.exists(self.config_path):
                print(f"⚠️ 설정 파일이 없습니다: {self.config_path}")
                self._create_default_config()
            
            with open(self.config_path, 'r', encoding='utf-8') as f:
                self.config_data = yaml.safe_load(f)
            
            self.current_profile = self.config_data.get('default_profile', 'korean_ecommerce')
            print(f"✅ 설정 로드 완료: {self.config_path} (프로필: {self.current_profile})")
            
        except Exception as e:
            print(f"❌ 설정 로드 실패: {e}")
            self._create_default_config()
    
    def _create_default_config(self) -> None:
        """기본 설정 생성"""
        self.config_data = {
            'default_profile': 'korean_ecommerce',
            'profiles': {
                'korean_ecommerce': {
                    'embedding_model': {
                        'provider': 'huggingface',
                        'model_name': 'snunlp/KR-SBERT-V40K-klueNLI-augSTS',
                        'model_type': 'embedding'
                    },
                    'llm_model': {
                        'provider': 'openai',
                        'model_name': 'gpt-3.5-turbo',
                        'model_type': 'llm',
                        'max_tokens': 200,
                        'temperature': 0.1
                    }
                }
            }
        }
        self.current_profile = 'korean_ecommerce'
    
    def get_profile_names(self) -> list:
        """사용 가능한 프로필 목록"""
        if not self.config_data or 'profiles' not in self.config_data:
            return ['korean_ecommerce']
        return list(self.config_data['profiles'].keys())
    
    def set_profile(self, profile_name: str) -> bool:
        """프로필 변경"""
        if profile_name not in self.get_profile_names():
            print(f"❌ 존재하지 않는 프로필: {profile_name}")
            return False
        
        self.current_profile = profile_name
        print(f"✅ 프로필 변경: {profile_name}")
        return True
    
    def get_embedding_config(self, profile_name: str = None) -> ModelConfig:
        """임베딩 모델 설정 가져오기"""
        profile = profile_name or self.current_profile
        
        try:
            profile_data = self.config_data['profiles'][profile]
            embedding_data = profile_data['embedding_model']
            
            return ModelConfig(
                model_name=embedding_data['model_name'],
                model_type=ModelType.EMBEDDING,
                provider=embedding_data['provider'],
                model_path=embedding_data.get('model_path'),
                additional_params=embedding_data.get('additional_params', {})
            )
        except Exception as e:
            print(f"❌ 임베딩 설정 로드 실패: {e}")
            # 기본 설정 반환
            return ModelConfig(
                model_name="snunlp/KR-SBERT-V40K-klueNLI-augSTS",
                model_type=ModelType.EMBEDDING,
                provider="huggingface"
            )
    
    def get_llm_config(self, profile_name: str = None) -> ModelConfig:
        """LLM 모델 설정 가져오기"""
        profile = profile_name or self.current_profile
        
        try:
            profile_data = self.config_data['profiles'][profile]
            llm_data = profile_data['llm_model']
            
            # API 키 가져오기
            api_key = None
            if llm_data['provider'] == 'openai':
                from config.config import OPENAI_API_KEY
                api_key = OPENAI_API_KEY
            elif llm_data['provider'] == 'anthropic':
                # Anthropic API 키가 있다면 여기서 가져오기
                api_key = os.getenv('ANTHROPIC_API_KEY')
            
            return ModelConfig(
                model_name=llm_data['model_name'],
                model_type=ModelType.LLM,
                provider=llm_data['provider'],
                api_key=api_key,
                max_tokens=llm_data.get('max_tokens'),
                temperature=llm_data.get('temperature'),
                additional_params=llm_data.get('additional_params', {})
            )
        except Exception as e:
            print(f"❌ LLM 설정 로드 실패: {e}")
            # 기본 설정 반환
            from config.config import OPENAI_API_KEY
            return ModelConfig(
                model_name="gpt-3.5-turbo",
                model_type=ModelType.LLM,
                provider="openai",
                api_key=OPENAI_API_KEY,
                max_tokens=200,
                temperature=0.1
            )
    
    def get_analysis_config(self, profile_name: str = None) -> AnalysisConfig:
        """분석 설정 가져오기"""
        try:
            analysis_data = self.config_data.get('analysis_settings', {})
            qa_data = analysis_data.get('qa_pair_analysis', {})
            
            return AnalysisConfig(
                similarity_threshold=qa_data.get('similarity_threshold', 0.6),
                pass_threshold=qa_data.get('pass_threshold', 0.6),
                grade_thresholds=qa_data.get('grade_thresholds', {}),
                score_weights=qa_data.get('score_weights', {})
            )
        except Exception as e:
            print(f"❌ 분석 설정 로드 실패: {e}")
            return AnalysisConfig()
    
    def create_embedding_model(self, profile_name: str = None):
        """임베딩 모델 생성"""
        config = self.get_embedding_config(profile_name)
        return ModelFactory.create_embedding_model(config)
    
    def create_llm_model(self, profile_name: str = None):
        """LLM 모델 생성"""
        config = self.get_llm_config(profile_name)
        return ModelFactory.create_llm_model(config)
    
    def get_batch_settings(self) -> Dict[str, Any]:
        """배치 처리 설정"""
        try:
            return self.config_data.get('analysis_settings', {}).get('batch_processing', {
                'max_workers': 4,
                'batch_size': 50,
                'timeout_seconds': 30,
                'enable_parallel': True
            })
        except:
            return {
                'max_workers': 4,
                'batch_size': 50,
                'timeout_seconds': 30,
                'enable_parallel': True
            }
    
    def get_output_settings(self) -> Dict[str, Any]:
        """출력 설정"""
        try:
            return self.config_data.get('output_settings', {
                'json_format': {
                    'ensure_ascii': False,
                    'indent': 2,
                    'sort_keys': False
                },
                'export_options': {
                    'include_metadata': True,
                    'include_statistics': True,
                    'include_raw_data': True
                }
            })
        except:
            return {
                'json_format': {
                    'ensure_ascii': False,
                    'indent': 2,
                    'sort_keys': False
                },
                'export_options': {
                    'include_metadata': True,
                    'include_statistics': True,
                    'include_raw_data': True
                }
            }
    
    def print_current_config(self) -> None:
        """현재 설정 출력"""
        print(f"\n📋 현재 모델 설정 (프로필: {self.current_profile})")
        print("=" * 60)
        
        # 임베딩 모델 정보
        embedding_config = self.get_embedding_config()
        print(f"🔤 임베딩 모델:")
        print(f"  • 제공자: {embedding_config.provider}")
        print(f"  • 모델명: {embedding_config.model_name}")
        
        # LLM 모델 정보
        llm_config = self.get_llm_config()
        print(f"\n🤖 LLM 모델:")
        print(f"  • 제공자: {llm_config.provider}")
        print(f"  • 모델명: {llm_config.model_name}")
        print(f"  • 최대 토큰: {llm_config.max_tokens}")
        print(f"  • 온도: {llm_config.temperature}")
        
        # 분석 설정
        analysis_config = self.get_analysis_config()
        print(f"\n📊 분석 설정:")
        print(f"  • 유사도 임계값: {analysis_config.similarity_threshold}")
        print(f"  • 통과 임계값: {analysis_config.pass_threshold}")
        
        print("=" * 60)
    
    def list_available_profiles(self) -> None:
        """사용 가능한 프로필 목록 출력"""
        print(f"\n📋 사용 가능한 프로필:")
        print("-" * 40)
        
        for profile_name in self.get_profile_names():
            marker = "✅" if profile_name == self.current_profile else "  "
            print(f"{marker} {profile_name}")
            
            try:
                profile_data = self.config_data['profiles'][profile_name]
                embedding = profile_data['embedding_model']
                llm = profile_data['llm_model']
                print(f"     임베딩: {embedding['provider']}/{embedding['model_name']}")
                print(f"     LLM: {llm['provider']}/{llm['model_name']}")
            except:
                print(f"     (설정 오류)")
            print()


# 전역 설정 관리자 인스턴스
config_manager = ModelConfigManager()


def get_config_manager() -> ModelConfigManager:
    """설정 관리자 인스턴스 가져오기"""
    return config_manager


def switch_profile(profile_name: str) -> bool:
    """프로필 전환"""
    return config_manager.set_profile(profile_name)


def get_current_profile() -> str:
    """현재 프로필 이름"""
    return config_manager.current_profile


if __name__ == "__main__":
    # 테스트
    manager = ModelConfigManager()
    manager.print_current_config()
    manager.list_available_profiles()
