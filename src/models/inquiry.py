"""
문의 데이터 모델
"""
from datetime import datetime
from typing import Dict, Optional
from pydantic import BaseModel, Field


class Inquiry(BaseModel):
    """문의 데이터 모델"""
    id: str
    post_id: str
    user_id: Optional[str] = None
    content: str
    subject: Optional[str] = None
    created_at: datetime = Field(default_factory=datetime.now)
    is_answered: bool = False
    answer_content: Optional[str] = None
    answered_at: Optional[datetime] = None
    metadata: Optional[Dict] = None
    
    @classmethod
    def from_dict(cls, data: Dict) -> 'Inquiry':
        """딕셔너리에서 문의 객체 생성"""
        # ID 필드 처리 (Supabase는 'id'를 사용하지만 외부 시스템은 다른 필드명 사용 가능)
        if 'id' not in data and 'post_id' in data:
            data['id'] = data['post_id']
        
        # 날짜 필드 처리
        for date_field in ['created_at', 'answered_at']:
            if date_field in data and isinstance(data[date_field], str):
                try:
                    data[date_field] = datetime.fromisoformat(data[date_field].replace('Z', '+00:00'))
                except (ValueError, TypeError):
                    data[date_field] = None
        
        return cls(**data)
    
    def to_dict(self) -> Dict:
        """딕셔너리로 변환"""
        return self.model_dump(
            exclude_none=True,
            exclude_defaults=False
        )
