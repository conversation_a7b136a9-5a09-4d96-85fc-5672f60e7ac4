"""
문의 분류 관련 데이터 모델
"""
from datetime import datetime
from enum import Enum
from typing import Dict, List, Optional
from pydantic import BaseModel, Field


class InquiryCategory(str, Enum):
    """문의 카테고리 열거형"""
    RETURN_REQUEST = "반품 요청"
    EXCHANGE_REQUEST = "교환 요청"
    REFUND_INQUIRY = "환불 문의"
    SHIPPING_ISSUE = "배송 지연/오류"
    PRODUCT_INFO = "상품 정보 문의"
    ORDER_CHANGE = "주문 변경/취소"
    ACCOUNT_RELATED = "회원/계정 관련"
    OTHER = "기타/불명확"


class InquiryTiming(str, Enum):
    """문의 타이밍"""
    BEFORE_ORDER = "주문 전"
    DURING_ORDER = "주문 중"
    AFTER_ORDER = "주문 후"
    UNKNOWN = "알 수 없음"


class InquiryTone(str, Enum):
    """문의 어조"""
    POSITIVE = "긍정적"
    NEUTRAL = "중립"
    NEGATIVE = "부정적"
    URGENT = "긴급"


class HandlingDifficulty(str, Enum):
    """처리 난이도"""
    EASY = "쉬움"
    MEDIUM = "보통"
    HARD = "어려움"
    VERY_HARD = "매우 어려움"


class ClassificationResult(BaseModel):
    """분류 결과 모델"""
    post_id: str
    brand: Optional[str] = None
    original_content: str
    category: InquiryCategory
    confidence: float = Field(ge=0.0, le=1.0)
    is_buyer: bool = True
    inquiry_timing: Optional[InquiryTiming] = None
    tone: Optional[InquiryTone] = None
    handling_difficulty: Optional[HandlingDifficulty] = None
    classified_at: datetime = Field(default_factory=datetime.now)
    
    def to_dict(self) -> Dict:
        """딕셔너리로 변환"""
        return self.model_dump(
            exclude_none=True,
            exclude_defaults=False
        )
    
    def to_supabase_record(self) -> Dict:
        """Supabase 레코드로 변환"""
        data = self.to_dict()
        # Enum 값을 문자열로 변환
        for field in ['category', 'inquiry_timing', 'tone', 'handling_difficulty']:
            if field in data and data[field] is not None:
                data[field] = str(data[field].value)
        return data