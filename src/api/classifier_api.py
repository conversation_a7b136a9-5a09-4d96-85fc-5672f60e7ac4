"""
문의 분류 API 서비스
"""
from fastapi import FastAPI, HTTPException, Depends, BackgroundTasks
from typing import List, Optional
import asyncio

from src.models.classification import ClassificationResult
from src.models.inquiry import Inquiry
from src.services.async_inquiry_service import AsyncInquiryService
from src.services.inquiry_classifier_service import InquiryClassifierService
from config.config import SUPABASE_URL, SUPABASE_KEY, OPENAI_API_KEY


app = FastAPI(title="이커머스 문의 분류 API")

# 서비스 인스턴스
classifier_service = InquiryClassifierService(
    embedding_model_path="models/ko_ecommerce_classifier.bin",
    llm_api_key=OPENAI_API_KEY
)


async def get_inquiry_service():
    """의존성 주입용 문의 서비스 가져오기"""
    return AsyncInquiryService(SUPABASE_URL, SUPABASE_KEY)


@app.post("/classify/single", response_model=ClassificationResult)
async def classify_single_inquiry(
    inquiry_id: str,
    inquiry_service: AsyncInquiryService = Depends(get_inquiry_service)
):
    """단일 문의 분류 API"""
    # 문의 데이터 조회
    inquiry_data = await inquiry_service.get_inquiry_by_id(inquiry_id)
    if not inquiry_data:
        raise HTTPException(status_code=404, detail="문의를 찾을 수 없습니다")
    
    # 문의 객체 생성 및 분류
    inquiry = Inquiry.from_dict(inquiry_data)
    result = await classifier_service.classify_inquiry(inquiry)
    
    return result


@app.post("/classify/bulk", response_model=List[ClassificationResult])
async def classify_bulk_inquiries(
    background_tasks: BackgroundTasks,
    limit: int = 100,
    save_results: bool = False,
    output_format: str = "json",
    inquiry_service: AsyncInquiryService = Depends(get_inquiry_service)
):
    """대량 문의 분류 API"""
    # 문의 데이터 조회
    inquiries_data = await inquiry_service.get_all_inquiries(limit=limit)
    if not inquiries_data:
        return []
    
    # 문의 객체 생성
    inquiries = [Inquiry.from_dict(item) for item in inquiries_data]
    
    # 분류 실행
    results = await classifier_service.bulk_classify(inquiries)
    
    # 결과 저장 (백그라운드 작업)
    if save_results:
        background_tasks.add_task(
            classifier_service.save_results,
            results,
            output_format,
            f"output/classification_results_{int(datetime.now().timestamp())}.{output_format}",
            inquiry_service if output_format == "supabase" else None
        )
    
    return results


@app.get("/categories")
async def get_categories():
    """지원되는 카테고리 목록 조회"""
    return {
        "categories": [
            {"id": cat.name, "name": cat.value}
            for cat in InquiryCategory
        ]
    }