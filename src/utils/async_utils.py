"""
비동기 유틸리티 함수
"""
import asyncio
import functools
import time
from typing import Any, Callable, List, TypeVar

T = TypeVar('T')


def async_timed():
    """
    비동기 함수 실행 시간 측정 데코레이터
    """
    def wrapper(func):
        @functools.wraps(func)
        async def wrapped(*args, **kwargs):
            start = time.time()
            try:
                return await func(*args, **kwargs)
            finally:
                end = time.time()
                total = end - start
                print(f"{func.__name__} 완료: {total:.4f}초")
        return wrapped
    return wrapper


async def batch_process(
    items: List[T],
    process_func: Callable[[T], Any],
    batch_size: int = 10
) -> List[Any]:
    """
    항목 목록을 배치로 비동기 처리
    
    Args:
        items: 처리할 항목 목록
        process_func: 처리 함수 (비동기)
        batch_size: 배치 크기
        
    Returns:
        처리 결과 목록
    """
    all_results = []
    
    for i in range(0, len(items), batch_size):
        batch = items[i:i+batch_size]
        tasks = [process_func(item) for item in batch]
        results = await asyncio.gather(*tasks)
        all_results.extend(results)
    
    return all_results


