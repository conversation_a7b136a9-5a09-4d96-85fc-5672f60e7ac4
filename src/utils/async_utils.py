"""
비동기 유틸리티 함수
"""
import asyncio
import functools
import time
from typing import Any, Callable, List, TypeVar

T = TypeVar('T')


def async_timed():
    """
    비동기 함수 실행 시간 측정 데코레이터
    """
    def wrapper(func):
        @functools.wraps(func)
        async def wrapped(*args, **kwargs):
            start = time.time()
            try:
                return await func(*args, **kwargs)
            finally:
                end = time.time()
                total = end - start
                print(f"{func.__name__} 완료: {total:.4f}초")
        return wrapped
    return wrapper


async def batch_process(
    items: List[T],
    process_func: Callable[[T], Any],
    batch_size: int = 10
) -> List[Any]:
    """
    항목 목록을 배치로 비동기 처리
    
    Args:
        items: 처리할 항목 목록
        process_func: 처리 함수 (비동기)
        batch_size: 배치 크기
        
    Returns:
        처리 결과 목록
    """
    all_results = []
    
    for i in range(0, len(items), batch_size):
        batch = items[i:i+batch_size]
        tasks = [process_func(item) for item in batch]
        results = await asyncio.gather(*tasks)
        all_results.extend(results)
    
    return all_results


def run_async_in_thread(func):
    """
    비동기 함수를 동기적으로 실행하는 데코레이터

    Args:
        func: 비동기 함수

    Returns:
        동기 함수로 래핑된 함수
    """
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        try:
            # 기존 이벤트 루프가 있는지 확인
            loop = asyncio.get_event_loop()
            if loop.is_running():
                # 이미 실행 중인 루프가 있으면 새 스레드에서 실행
                import concurrent.futures
                import threading

                def run_in_thread():
                    new_loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(new_loop)
                    try:
                        return new_loop.run_until_complete(func(*args, **kwargs))
                    finally:
                        new_loop.close()

                with concurrent.futures.ThreadPoolExecutor() as executor:
                    future = executor.submit(run_in_thread)
                    return future.result()
            else:
                # 실행 중인 루프가 없으면 직접 실행
                return loop.run_until_complete(func(*args, **kwargs))
        except RuntimeError:
            # 루프가 없으면 새로 생성
            return asyncio.run(func(*args, **kwargs))

    return wrapper


async def run_with_timeout(coro, timeout_seconds: float = 30.0):
    """
    타임아웃과 함께 코루틴 실행

    Args:
        coro: 실행할 코루틴
        timeout_seconds: 타임아웃 시간 (초)

    Returns:
        실행 결과

    Raises:
        asyncio.TimeoutError: 타임아웃 발생 시
    """
    try:
        return await asyncio.wait_for(coro, timeout=timeout_seconds)
    except asyncio.TimeoutError:
        print(f"작업이 {timeout_seconds}초 내에 완료되지 않았습니다.")
        raise


async def retry_async(
    func: Callable,
    max_retries: int = 3,
    delay: float = 1.0,
    backoff_factor: float = 2.0
):
    """
    비동기 함수 재시도 실행

    Args:
        func: 실행할 비동기 함수
        max_retries: 최대 재시도 횟수
        delay: 초기 지연 시간 (초)
        backoff_factor: 지연 시간 증가 배수

    Returns:
        함수 실행 결과

    Raises:
        마지막 시도에서 발생한 예외
    """
    last_exception = None
    current_delay = delay

    for attempt in range(max_retries + 1):
        try:
            return await func()
        except Exception as e:
            last_exception = e
            if attempt < max_retries:
                print(f"시도 {attempt + 1} 실패: {e}, {current_delay}초 후 재시도...")
                await asyncio.sleep(current_delay)
                current_delay *= backoff_factor
            else:
                print(f"모든 재시도 실패. 마지막 오류: {e}")
                raise last_exception
