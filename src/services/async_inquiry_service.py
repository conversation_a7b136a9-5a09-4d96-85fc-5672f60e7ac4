"""
비동기 문의 서비스 - Supabase에서 문의 데이터 비동기 조회
"""
import asyncio
from typing import Dict, List, Optional
from supabase import create_client, Client

from src.utils.async_utils import async_timed


class AsyncInquiryService:
    """비동기 문의 서비스"""
    
    def __init__(self, supabase_url: str, supabase_key: str):
        """
        비동기 문의 서비스 초기화
        
        Args:
            supabase_url: Supabase URL
            supabase_key: Supabase API 키
        """
        self.supabase_client = create_client(supabase_url, supabase_key)
        self.tables = {
            'product': 'product_inquiries',
            'personal': 'personal_inquiries',
            'shipping': 'shipping_inquiry',
            'results': 'classification_results',
            'logs': 'classification_logs'
        }
    
    @async_timed()
    async def get_inquiry_by_id(self, inquiry_id: str, table: str = 'product') -> Optional[Dict]:
        """
        ID로 문의 조회
        
        Args:
            inquiry_id: 문의 ID
            table: 테이블 이름 (기본값: product)
            
        Returns:
            문의 데이터 또는 None
        """
        table_name = self.tables.get(table, table)
        
        # Supabase 쿼리 실행
        response = self.supabase_client.table(table_name) \
            .select('*') \
            .eq('id', inquiry_id) \
            .execute()
        
        data = response.data
        return data[0] if data else None
    
    @async_timed()
    async def get_all_inquiries(
        self, 
        table: str = 'product',
        limit: int = 100,
        offset: int = 0,
        only_unanswered: bool = False
    ) -> List[Dict]:
        """
        모든 문의 조회
        
        Args:
            table: 테이블 이름 (기본값: product)
            limit: 최대 조회 수
            offset: 오프셋
            only_unanswered: 미답변 문의만 조회 여부
            
        Returns:
            문의 데이터 목록
        """
        table_name = self.tables.get(table, table)
        
        # 기본 쿼리 구성
        query = self.supabase_client.table(table_name) \
            .select('*') \
            .order('created_at', desc=True) \
            .limit(limit) \
            .offset(offset)
        
        # 미답변 필터 적용
        if only_unanswered:
            query = query.eq('is_answered', False)
        
        # 쿼리 실행
        response = query.execute()
        
        return response.data
    
    @async_timed()
    async def save_classification_result(self, result: Dict) -> Dict:
        """
        분류 결과 저장
        
        Args:
            result: 저장할 분류 결과
            
        Returns:
            저장된 결과
        """
        # 결과 테이블에 저장
        response = self.supabase_client.table(self.tables['results']) \
            .upsert(result, on_conflict='post_id') \
            .execute()
        
        return response.data[0] if response.data else {}
    
    @async_timed()
    async def save_classification_log(self, log_data: Dict) -> Dict:
        """
        분류 로그 저장
        
        Args:
            log_data: 저장할 로그 데이터
            
        Returns:
            저장된 로그
        """
        # 로그 테이블에 저장
        response = self.supabase_client.table(self.tables['logs']) \
            .insert(log_data) \
            .execute()
        
        return response.data[0] if response.data else {}
