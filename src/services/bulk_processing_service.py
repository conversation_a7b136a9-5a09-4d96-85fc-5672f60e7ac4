"""
대규모 데이터를 비동기적으로 처리하는 모듈
"""
import asyncio
import time

from config.config import SUPABASE_KEY, SUPABASE_URL
from src.models.inquiry import Inquiry
from src.services.batch_service import BatchProcessConfig, BatchService
from src.utils.async_utils import run_async_in_thread


async def process_inquiry(inquiry: Inquiry) -> dict:
    """
    개별 문의 처리 로직
    
    Args:
        inquiry: 처리할 문의 객체
        
    Returns:
        처리 결과
    """
    # 실제 비즈니스 로직 구현 (예: 감정 분석, 자동 분류 등)
    # 여기서는 간단한 예시만 구현
    
    # 문의 내용 분석 시뮬레이션 (실제로는 AI 모델 등으로 처리)
    await asyncio.sleep(0.05)  # 처리 시간 시뮬레이션
    
    # 결과 생성
    is_urgent = '긴급' in inquiry.subject or '빠른' in inquiry.content
    category = '교환' if '교환' in inquiry.subject else (
        '환불' if '환불' in inquiry.content else '일반문의'
    )
    
    return {
        'id': inquiry.id,
        'category': category,
        'is_urgent': is_urgent,
        'requires_attention': is_urgent or not inquiry.is_answered
    }


@run_async_in_thread
async def main() -> int:
    """
    메인 실행 함수
    """
    print("대규모 데이터 병렬 처리 시작...")
    start_time = time.time()
    
    # 설정 및 오류 체크
    if not SUPABASE_URL or not SUPABASE_KEY:
        print("오류: Supabase 연결 정보가 설정되지 않았습니다.")
        return 1
    
    # 배치 처리 설정
    config = BatchProcessConfig(
        batch_size=30,  # 배치당 최대 항목 수
        max_concurrent_batches=5,  # 최대 동시 배치 처리 수
        timeout_seconds=60.0  # API 요청 타임아웃
    )
    
    # 배치 서비스 초기화
    batch_service = BatchService(SUPABASE_URL, SUPABASE_KEY, config)
    
    try:
        # 테이블에서 모든 데이터 가져오기
        table_name = "personal_inquiries"
        print(f"{table_name} 테이블에서 데이터 조회 중...")
        
        inquiries_data = await batch_service.fetch_all_data(table_name)
        inquiry_count = len(inquiries_data)
        
        print(f"총 {inquiry_count}개 문의 데이터 조회 완료")
        
        if not inquiries_data:
            print("처리할 데이터가 없습니다.")
            return 0
        
        # 대규모 병렬 처리 실행
        print("문의 데이터 병렬 처리 중...")
        result = await batch_service.process_bulk_inquiries(
            inquiries_data, 
            process_inquiry
        )
        
        # 결과 출력
        success_rate = result.success_rate
        print("\n[처리 결과]")
        print(f"- 총 처리 항목: {result.total_items}개")
        print(f"- 성공: {result.successful_items}개")
        print(f"- 실패: {result.failed_items}개")
        print(f"- 성공률: {success_rate:.1f}%")
        
        # 오류 샘플 출력 (있을 경우)
        if result.errors:
            print("\n[오류 샘플]")
            for error in result.errors[:3]:  # 처음 3개만 출력
                print(f"- ID: {error['item'].get('id')}, "
                      f"오류: {error['error']}")
            
            if len(result.errors) > 3:
                print(f"... 외 {len(result.errors) - 3}개 오류")
        
        elapsed_time = time.time() - start_time
        print(f"\n총 처리 시간: {elapsed_time:.2f}초")
        print(f"항목당 평균 처리 시간: {(elapsed_time / inquiry_count) * 1000:.2f}ms")
        
    except Exception as e:
        print(f"처리 중 오류 발생: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0


def run_bulk_processing() -> int:
    """
    외부에서 호출할 수 있는 실행 함수
    
    Returns:
        종료 코드
    """
    result = main()
    if callable(result):
        # 실행 가능한 결과값인 경우 (코루틴)
        return 0
    return result 