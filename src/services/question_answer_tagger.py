"""
질문-답변 분류 및 태그 생성 전문 시스템
"""
import asyncio
import json
import re
from typing import Dict, List, Set, Tuple, Optional
from dataclasses import dataclass, asdict
from collections import Counter

from src.services.optimized_inquiry_analyzer import OptimizedInquiryAnalyzer
from src.services.embedding_model_service import EmbeddingModelService


@dataclass
class QuestionTag:
    """질문 태그"""
    tag_name: str
    confidence: float
    category: str  # 주제/감정/긴급도/기타
    description: str


@dataclass
class AnswerTag:
    """답변 태그"""
    tag_name: str
    confidence: float
    category: str  # 해결방식/품질/만족도/기타
    description: str


@dataclass
class TaggedInquiry:
    """태그가 적용된 문의"""
    inquiry_id: str
    
    # 원본 데이터
    question_text: str
    answer_text: str
    
    # 질문 태그
    question_tags: List[QuestionTag]
    question_category: str
    question_sentiment: str
    
    # 답변 태그
    answer_tags: List[AnswerTag]
    answer_quality: str
    answer_effectiveness: str
    
    # 매칭 분석
    tag_match_score: float  # 질문-답변 태그 매칭 점수
    resolution_prediction: str  # 해결가능성 예측
    
    def to_dict(self) -> Dict:
        """딕셔너리 변환"""
        return asdict(self)


class QuestionAnswerTagger:
    """질문-답변 분류 및 태그 생성 시스템"""
    
    def __init__(self, supabase_url: str, supabase_key: str):
        """
        태거 시스템 초기화
        
        Args:
            supabase_url: Supabase URL
            supabase_key: Supabase API 키
        """
        self.analyzer = OptimizedInquiryAnalyzer(supabase_url, supabase_key)
        self.embedding_service = None
        
        # 질문 태그 체계
        self.question_tag_patterns = {
            # 주제별 태그
            "배송관련": {
                "keywords": ["배송", "택배", "발송", "도착", "언제", "늦어", "빨리"],
                "category": "주제",
                "description": "배송 관련 문의"
            },
            "상품문의": {
                "keywords": ["상품", "제품", "품질", "색상", "사이즈", "재질", "성분"],
                "category": "주제", 
                "description": "상품 정보 관련 문의"
            },
            "결제문의": {
                "keywords": ["결제", "카드", "계좌", "입금", "환불", "취소", "할인"],
                "category": "주제",
                "description": "결제 관련 문의"
            },
            "교환반품": {
                "keywords": ["교환", "반품", "불량", "파손", "잘못", "다른"],
                "category": "주제",
                "description": "교환/반품 관련 문의"
            },
            
            # 감정별 태그
            "긴급요청": {
                "keywords": ["급해", "빨리", "긴급", "당장", "오늘", "지금", "!!!"],
                "category": "감정",
                "description": "긴급한 처리가 필요한 문의"
            },
            "불만표현": {
                "keywords": ["화나", "짜증", "불만", "실망", "최악", "엉망", "어이없"],
                "category": "감정",
                "description": "불만이나 화가 표현된 문의"
            },
            "감사표현": {
                "keywords": ["감사", "고마워", "좋아", "만족", "훌륭", "완벽"],
                "category": "감정",
                "description": "감사나 만족이 표현된 문의"
            },
            
            # 복잡도별 태그
            "단순문의": {
                "keywords": ["언제", "어디", "얼마", "몇", "어떻게"],
                "category": "복잡도",
                "description": "간단한 정보 요청"
            },
            "복합문의": {
                "keywords": ["그리고", "또한", "추가로", "동시에", "여러"],
                "category": "복잡도", 
                "description": "여러 사항이 포함된 복합 문의"
            }
        }
        
        # 답변 태그 체계
        self.answer_tag_patterns = {
            # 해결방식별 태그
            "즉시해결": {
                "keywords": ["완료", "처리", "해결", "확인", "바로", "즉시"],
                "category": "해결방식",
                "description": "즉시 해결된 답변"
            },
            "안내제공": {
                "keywords": ["안내", "방법", "절차", "단계", "참고", "확인"],
                "category": "해결방식",
                "description": "해결 방법을 안내한 답변"
            },
            "추가확인": {
                "keywords": ["확인", "검토", "문의", "연락", "담당자", "부서"],
                "category": "해결방식",
                "description": "추가 확인이 필요한 답변"
            },
            "거절응답": {
                "keywords": ["어려운", "불가능", "죄송", "안됩니다", "할 수 없"],
                "category": "해결방식",
                "description": "요청을 거절한 답변"
            },
            
            # 품질별 태그
            "상세답변": {
                "keywords": ["자세히", "구체적", "단계별", "상세", "설명"],
                "category": "품질",
                "description": "상세하고 구체적인 답변"
            },
            "친절답변": {
                "keywords": ["안녕하세요", "감사합니다", "죄송합니다", "도움", "친절"],
                "category": "품질",
                "description": "친절하고 정중한 답변"
            },
            "전문답변": {
                "keywords": ["정책", "규정", "시스템", "전문", "기술"],
                "category": "품질",
                "description": "전문적인 지식이 포함된 답변"
            }
        }
    
    def _init_embedding_service(self):
        """임베딩 서비스 초기화"""
        if self.embedding_service is None:
            try:
                self.embedding_service = EmbeddingModelService()
                print("✅ 임베딩 서비스 초기화 완료")
            except Exception as e:
                print(f"⚠️ 임베딩 서비스 초기화 실패: {e}")
    
    def _extract_question_tags(self, question_text: str) -> List[QuestionTag]:
        """질문에서 태그 추출"""
        tags = []
        text_lower = question_text.lower()
        
        for tag_name, pattern in self.question_tag_patterns.items():
            # 키워드 매칭
            matches = sum(1 for keyword in pattern["keywords"] if keyword in text_lower)
            if matches > 0:
                confidence = min(1.0, matches / len(pattern["keywords"]) * 2)  # 최대 1.0
                
                tags.append(QuestionTag(
                    tag_name=tag_name,
                    confidence=confidence,
                    category=pattern["category"],
                    description=pattern["description"]
                ))
        
        # 길이 기반 태그
        if len(question_text) > 200:
            tags.append(QuestionTag(
                tag_name="장문문의",
                confidence=0.8,
                category="복잡도",
                description="긴 문의 내용"
            ))
        
        # 특수문자 기반 태그
        if question_text.count('!') >= 2:
            tags.append(QuestionTag(
                tag_name="강조표현",
                confidence=0.9,
                category="감정",
                description="강조 표현이 포함된 문의"
            ))
        
        return sorted(tags, key=lambda x: x.confidence, reverse=True)
    
    def _extract_answer_tags(self, answer_text: str) -> List[AnswerTag]:
        """답변에서 태그 추출"""
        if not answer_text or answer_text.strip() == "nan":
            return []
        
        tags = []
        text_lower = answer_text.lower()
        
        for tag_name, pattern in self.answer_tag_patterns.items():
            # 키워드 매칭
            matches = sum(1 for keyword in pattern["keywords"] if keyword in text_lower)
            if matches > 0:
                confidence = min(1.0, matches / len(pattern["keywords"]) * 2)
                
                tags.append(AnswerTag(
                    tag_name=tag_name,
                    confidence=confidence,
                    category=pattern["category"],
                    description=pattern["description"]
                ))
        
        # 길이 기반 품질 태그
        if len(answer_text) > 300:
            tags.append(AnswerTag(
                tag_name="상세답변",
                confidence=0.7,
                category="품질",
                description="상세한 답변"
            ))
        elif len(answer_text) < 50:
            tags.append(AnswerTag(
                tag_name="간단답변",
                confidence=0.6,
                category="품질",
                description="간단한 답변"
            ))
        
        # 구조화 정도 태그
        if answer_text.count('\n') > 2 or '1.' in answer_text or '•' in answer_text:
            tags.append(AnswerTag(
                tag_name="구조화답변",
                confidence=0.8,
                category="품질",
                description="구조화된 답변"
            ))
        
        return sorted(tags, key=lambda x: x.confidence, reverse=True)
    
    def _calculate_tag_match_score(
        self, 
        question_tags: List[QuestionTag], 
        answer_tags: List[AnswerTag]
    ) -> float:
        """질문-답변 태그 매칭 점수 계산"""
        if not question_tags or not answer_tags:
            return 0.0
        
        # 주제 매칭 점수
        question_topics = {tag.tag_name for tag in question_tags if tag.category == "주제"}
        answer_solutions = {tag.tag_name for tag in answer_tags if tag.category == "해결방식"}
        
        topic_match_score = 0.0
        
        # 특정 주제-해결방식 매칭 규칙
        topic_solution_matches = {
            "배송관련": ["즉시해결", "안내제공", "추가확인"],
            "상품문의": ["안내제공", "상세답변"],
            "결제문의": ["즉시해결", "추가확인"],
            "교환반품": ["즉시해결", "안내제공"]
        }
        
        for topic in question_topics:
            if topic in topic_solution_matches:
                expected_solutions = topic_solution_matches[topic]
                if any(sol in answer_solutions for sol in expected_solutions):
                    topic_match_score += 0.3
        
        # 감정-품질 매칭 점수
        emotion_quality_score = 0.0
        question_emotions = {tag.tag_name for tag in question_tags if tag.category == "감정"}
        answer_qualities = {tag.tag_name for tag in answer_tags if tag.category == "품질"}
        
        if "불만표현" in question_emotions and "친절답변" in answer_qualities:
            emotion_quality_score += 0.4
        if "긴급요청" in question_emotions and "즉시해결" in answer_solutions:
            emotion_quality_score += 0.3
        
        return min(1.0, topic_match_score + emotion_quality_score)
    
    def _predict_resolution(
        self, 
        question_tags: List[QuestionTag], 
        answer_tags: List[AnswerTag],
        match_score: float
    ) -> str:
        """해결가능성 예측"""
        if not answer_tags:
            return "미답변"
        
        # 해결 지표 태그 확인
        resolution_indicators = {"즉시해결", "안내제공", "상세답변"}
        negative_indicators = {"거절응답", "추가확인"}
        
        answer_tag_names = {tag.tag_name for tag in answer_tags}
        
        if any(tag in answer_tag_names for tag in resolution_indicators):
            if match_score > 0.6:
                return "해결가능성높음"
            else:
                return "해결가능성보통"
        elif any(tag in answer_tag_names for tag in negative_indicators):
            return "해결가능성낮음"
        else:
            return "해결가능성보통"
    
    async def tag_inquiries_batch(self, limit: int = 50) -> List[TaggedInquiry]:
        """배치 문의 태깅"""
        print(f"🏷️ {limit}건의 문의 태깅 시작...")
        
        # 기본 분석 실행
        analysis_results = await self.analyzer.analyze_inquiries_batch(limit=limit)
        
        # 태깅 적용
        tagged_inquiries = []
        
        for result in analysis_results:
            try:
                # 질문 태그 추출
                question_text = f"{result.subject} {result.question_content}".strip()
                question_tags = self._extract_question_tags(question_text)
                
                # 답변 태그 추출
                answer_tags = self._extract_answer_tags(result.answer_content)
                
                # 매칭 점수 계산
                match_score = self._calculate_tag_match_score(question_tags, answer_tags)
                
                # 해결가능성 예측
                resolution_prediction = self._predict_resolution(question_tags, answer_tags, match_score)
                
                tagged_inquiry = TaggedInquiry(
                    inquiry_id=result.inquiry_id,
                    question_text=question_text,
                    answer_text=result.answer_content,
                    question_tags=question_tags,
                    question_category=result.question_analysis.category,
                    question_sentiment=result.question_analysis.sentiment,
                    answer_tags=answer_tags,
                    answer_quality=result.answer_analysis.quality_score if result.answer_analysis else 0.0,
                    answer_effectiveness=result.answer_analysis.response_type if result.answer_analysis else "미답변",
                    tag_match_score=match_score,
                    resolution_prediction=resolution_prediction
                )
                
                tagged_inquiries.append(tagged_inquiry)
                
            except Exception as e:
                print(f"⚠️ 문의 {result.inquiry_id} 태깅 실패: {e}")
                continue
        
        print(f"✅ {len(tagged_inquiries)}건 태깅 완료")
        return tagged_inquiries
    
    def generate_tag_statistics(self, tagged_inquiries: List[TaggedInquiry]) -> Dict:
        """태그 통계 생성"""
        stats = {
            "question_tag_frequency": {},
            "answer_tag_frequency": {},
            "tag_match_distribution": {},
            "resolution_prediction_distribution": {},
            "category_tag_correlation": {},
            "top_tag_combinations": []
        }
        
        # 질문 태그 빈도
        question_tag_counter = Counter()
        for inquiry in tagged_inquiries:
            for tag in inquiry.question_tags:
                question_tag_counter[tag.tag_name] += 1
        stats["question_tag_frequency"] = dict(question_tag_counter.most_common(10))
        
        # 답변 태그 빈도
        answer_tag_counter = Counter()
        for inquiry in tagged_inquiries:
            for tag in inquiry.answer_tags:
                answer_tag_counter[tag.tag_name] += 1
        stats["answer_tag_frequency"] = dict(answer_tag_counter.most_common(10))
        
        # 매칭 점수 분포
        match_scores = [inq.tag_match_score for inq in tagged_inquiries]
        stats["tag_match_distribution"] = {
            "high_match": sum(1 for score in match_scores if score > 0.7),
            "medium_match": sum(1 for score in match_scores if 0.3 <= score <= 0.7),
            "low_match": sum(1 for score in match_scores if score < 0.3),
            "average_score": sum(match_scores) / len(match_scores) if match_scores else 0
        }
        
        # 해결가능성 분포
        resolution_counter = Counter(inq.resolution_prediction for inq in tagged_inquiries)
        stats["resolution_prediction_distribution"] = dict(resolution_counter)
        
        return stats
    
    def export_tagged_results(
        self, 
        tagged_inquiries: List[TaggedInquiry],
        filename: str = "tagged_inquiries.json"
    ) -> Dict:
        """태깅 결과 JSON 내보내기"""
        stats = self.generate_tag_statistics(tagged_inquiries)
        
        output = {
            "metadata": {
                "total_inquiries": len(tagged_inquiries),
                "analysis_timestamp": __import__('time').strftime("%Y-%m-%d %H:%M:%S"),
                "tag_system_version": "1.0"
            },
            "statistics": stats,
            "tagged_inquiries": [inq.to_dict() for inq in tagged_inquiries]
        }
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(output, f, ensure_ascii=False, indent=2)
            print(f"📄 태깅 결과를 {filename}에 저장했습니다.")
        except Exception as e:
            print(f"⚠️ 파일 저장 실패: {e}")
        
        return output


async def run_question_answer_tagging():
    """질문-답변 태깅 실행"""
    from config.config import SUPABASE_URL, SUPABASE_KEY
    
    print("🏷️ 질문-답변 태깅 시스템 시작...")
    
    tagger = QuestionAnswerTagger(SUPABASE_URL, SUPABASE_KEY)
    
    try:
        # 태깅 실행
        tagged_inquiries = await tagger.tag_inquiries_batch(limit=25)
        
        # 결과 내보내기
        results = tagger.export_tagged_results(tagged_inquiries)
        
        # 요약 출력
        print("\n" + "="*80)
        print("🏷️ 질문-답변 태깅 결과 요약")
        print("="*80)
        
        stats = results["statistics"]
        
        print(f"📊 기본 통계:")
        print(f"  • 총 분석: {results['metadata']['total_inquiries']}건")
        print(f"  • 평균 매칭점수: {stats['tag_match_distribution']['average_score']:.3f}")
        
        print(f"\n🔖 상위 질문 태그:")
        for tag, count in list(stats["question_tag_frequency"].items())[:5]:
            print(f"  • {tag}: {count}회")
        
        print(f"\n💬 상위 답변 태그:")
        for tag, count in list(stats["answer_tag_frequency"].items())[:5]:
            print(f"  • {tag}: {count}회")
        
        print(f"\n🎯 해결가능성 분포:")
        for prediction, count in stats["resolution_prediction_distribution"].items():
            print(f"  • {prediction}: {count}건")
        
        print(f"\n✅ 상세 결과는 tagged_inquiries.json 파일에서 확인하세요!")
        
        return results
        
    except Exception as e:
        print(f"❌ 태깅 중 오류: {e}")
        import traceback
        traceback.print_exc()
        return None


if __name__ == "__main__":
    asyncio.run(run_question_answer_tagging())
