"""
대규모 데이터 비동기 배치 처리 서비스
"""
import asyncio
from typing import Any, Callable, Dict, Generic, List, Optional, TypeVar

import httpx
from pydantic import BaseModel, Field, validator

from src.models.inquiry import Inquiry
from src.utils.async_utils import async_timed, batch_process

T = TypeVar('T', bound=BaseModel)


class BatchProcessConfig(BaseModel):
    """배치 처리 설정"""
    batch_size: int = Field(default=25, ge=1, le=100)
    max_concurrent_batches: int = Field(default=4, ge=1, le=10)
    retry_count: int = Field(default=3, ge=0, le=10)
    timeout_seconds: float = Field(default=30.0, ge=1.0, le=300.0)
    
    @validator('batch_size')
    def validate_batch_size(cls, v):
        """배치 크기 검증"""
        if v > 50:
            print(f"경고: 큰 배치 크기({v})는 성능 문제를 일으킬 수 있습니다.")
        return v


class BatchProcessResult(BaseModel):
    """배치 처리 결과"""
    total_items: int = 0
    successful_items: int = 0
    failed_items: int = 0
    errors: List[Dict[str, Any]] = Field(default_factory=list)
    
    @property
    def success_rate(self) -> float:
        """성공률 계산"""
        if self.total_items == 0:
            return 0.0
        return self.successful_items / self.total_items * 100.0


class BatchService(Generic[T]):
    """
    대규모 데이터 배치 처리 서비스
    
    데이터를 효율적으로 처리하기 위한 배치 처리 및 동시성 제어 제공
    """
    
    def __init__(
        self, 
        url: str, 
        api_key: str, 
        config: Optional[BatchProcessConfig] = None
    ):
        self.url = url
        self.api_key = api_key
        self.config = config or BatchProcessConfig()
        self.headers = {
            "apikey": api_key,
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json",
            "Prefer": "return=representation"
        }
    
    async def fetch_all_data(self, table_name: str) -> List[Dict[str, Any]]:
        """
        테이블의 모든 데이터를 조회합니다.
        
        Args:
            table_name: 테이블 이름
            
        Returns:
            조회된 데이터 리스트
        """
        endpoint = f"{self.url}/rest/v1/{table_name}?select=*"
        
        async with httpx.AsyncClient(timeout=self.config.timeout_seconds) as client:
            response = await client.get(endpoint, headers=self.headers)
            response.raise_for_status()
            return response.json()
    
    @async_timed()
    async def process_all_items(
        self,
        items: List[Dict[str, Any]],
        process_func: Callable[[Dict[str, Any]], Any]
    ) -> BatchProcessResult:
        """
        모든 항목을 병렬로 처리합니다.
        
        Args:
            items: 처리할 항목 목록
            process_func: 처리 함수
            
        Returns:
            처리 결과
        """
        if not items:
            return BatchProcessResult(total_items=0)
        
        # 배치 분할
        batch_size = self.config.batch_size
        batches = [
            items[i:i + batch_size] 
            for i in range(0, len(items), batch_size)
        ]
        
        result = BatchProcessResult(total_items=len(items))
        semaphore = asyncio.Semaphore(self.config.max_concurrent_batches)
        
        async def process_batch(batch):
            """한 배치를 비동기로 처리"""
            async with semaphore:
                tasks = [process_func(item) for item in batch]
                batch_results = await asyncio.gather(*tasks, return_exceptions=True)
                
                successful = 0
                failed = 0
                errors = []
                
                for i, res in enumerate(batch_results):
                    if isinstance(res, Exception):
                        failed += 1
                        errors.append({
                            "item": batch[i],
                            "error": str(res)
                        })
                    else:
                        successful += 1
                
                return successful, failed, errors
        
        # 모든 배치 병렬 처리
        batch_tasks = [process_batch(batch) for batch in batches]
        batch_results = await asyncio.gather(*batch_tasks)
        
        # 결과 집계
        for successful, failed, errors in batch_results:
            result.successful_items += successful
            result.failed_items += failed
            result.errors.extend(errors)
        
        return result
    
    @async_timed()
    async def process_bulk_inquiries(
        self, 
        inquiries: List[Dict[str, Any]],
        process_func: Callable[[Inquiry], Any]
    ) -> BatchProcessResult:
        """
        대량의 문의 데이터를 처리합니다.
        
        Args:
            inquiries: 처리할 문의 데이터
            process_func: 각 문의를 처리할 함수
            
        Returns:
            처리 결과
        """
        async def process_inquiry(inquiry_data):
            try:
                # 데이터 변환 및 처리
                inquiry = Inquiry.from_dict(inquiry_data)
                return await process_func(inquiry)
            except Exception as e:
                # 개별 항목 오류 처리
                print(f"문의 처리 오류 (ID:{inquiry_data.get('id')}): {e}")
                raise
        
        # 모든 문의 병렬 처리
        return await self.process_all_items(inquiries, process_inquiry) 