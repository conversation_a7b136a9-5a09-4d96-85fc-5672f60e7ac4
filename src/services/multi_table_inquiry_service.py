"""
여러 테이블 통합 문의 처리 서비스
"""
import asyncio
from typing import Dict, List, Optional, Union
from datetime import datetime

from src.models.inquiry import Inquiry
from src.services.async_inquiry_service import AsyncInquiryService
from src.utils.async_utils import async_timed


class MultiTableInquiryService:
    """여러 테이블에서 문의 데이터를 통합 처리하는 서비스"""
    
    def __init__(self, supabase_url: str, supabase_key: str):
        """
        통합 문의 서비스 초기화
        
        Args:
            supabase_url: Supabase URL
            supabase_key: Supabase API 키
        """
        self.async_service = AsyncInquiryService(supabase_url, supabase_key)
        
        # 처리할 테이블 목록
        self.inquiry_tables = [
            'product_inquiries',    # 상품 문의
            'personal_inquiries',   # 개인 문의
            'shipping_inquiry'      # 배송 문의
        ]
        
        # 테이블별 매핑 정보
        self.table_mappings = {
            'product_inquiries': {
                'type': '상품 문의',
                'key_fields': ['product_id', 'brand', 'category']
            },
            'personal_inquiries': {
                'type': '개인 문의', 
                'key_fields': ['customer', 'phone', 'email']
            },
            'shipping_inquiry': {
                'type': '배송 문의',
                'key_fields': ['order_id', 'tracking_number', 'delivery_status']
            }
        }
    
    @async_timed()
    async def get_all_inquiries_from_all_tables(
        self, 
        limit_per_table: int = 50,
        only_unanswered: bool = False
    ) -> Dict[str, List[Dict]]:
        """
        모든 테이블에서 문의 데이터 조회
        
        Args:
            limit_per_table: 테이블당 최대 조회 수
            only_unanswered: 미답변 문의만 조회 여부
            
        Returns:
            테이블별 문의 데이터 딕셔너리
        """
        results = {}
        
        # 각 테이블에서 병렬로 데이터 조회
        tasks = []
        for table in self.inquiry_tables:
            task = self._get_inquiries_from_table(
                table, limit_per_table, only_unanswered
            )
            tasks.append(task)
        
        # 병렬 실행
        table_results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 결과 정리
        for i, table in enumerate(self.inquiry_tables):
            result = table_results[i]
            if isinstance(result, Exception):
                print(f"테이블 {table} 조회 중 오류: {result}")
                results[table] = []
            else:
                results[table] = result
                
        return results
    
    async def _get_inquiries_from_table(
        self, 
        table: str, 
        limit: int, 
        only_unanswered: bool
    ) -> List[Dict]:
        """
        특정 테이블에서 문의 데이터 조회
        
        Args:
            table: 테이블 이름
            limit: 최대 조회 수
            only_unanswered: 미답변 문의만 조회 여부
            
        Returns:
            문의 데이터 목록
        """
        try:
            # 테이블 이름을 키로 변환 (async_inquiry_service의 tables 딕셔너리 사용)
            table_key = self._get_table_key(table)
            
            inquiries = await self.async_service.get_all_inquiries(
                table=table_key,
                limit=limit,
                only_unanswered=only_unanswered
            )
            
            # 테이블 정보 추가
            for inquiry in inquiries:
                inquiry['source_table'] = table
                inquiry['inquiry_type'] = self.table_mappings[table]['type']
                
            return inquiries
            
        except Exception as e:
            print(f"테이블 {table} 조회 중 오류: {e}")
            return []
    
    def _get_table_key(self, table_name: str) -> str:
        """테이블 이름을 async_service의 키로 변환"""
        table_key_mapping = {
            'product_inquiries': 'product',
            'personal_inquiries': 'personal', 
            'shipping_inquiry': 'shipping'
        }
        return table_key_mapping.get(table_name, table_name)
    
    @async_timed()
    async def get_unified_inquiry_list(
        self, 
        limit_per_table: int = 50,
        only_unanswered: bool = False
    ) -> List[Dict]:
        """
        모든 테이블의 문의를 통합된 리스트로 반환
        
        Args:
            limit_per_table: 테이블당 최대 조회 수
            only_unanswered: 미답변 문의만 조회 여부
            
        Returns:
            통합된 문의 데이터 리스트 (시간순 정렬)
        """
        # 모든 테이블에서 데이터 조회
        table_results = await self.get_all_inquiries_from_all_tables(
            limit_per_table, only_unanswered
        )
        
        # 모든 문의를 하나의 리스트로 통합
        all_inquiries = []
        for table, inquiries in table_results.items():
            all_inquiries.extend(inquiries)
        
        # 생성 시간순으로 정렬 (최신순)
        all_inquiries.sort(
            key=lambda x: x.get('created_at', ''), 
            reverse=True
        )
        
        return all_inquiries
    
    @async_timed()
    async def get_inquiry_statistics(self) -> Dict[str, Union[int, Dict]]:
        """
        전체 문의 통계 정보 조회
        
        Returns:
            통계 정보 딕셔너리
        """
        # 모든 테이블에서 데이터 조회 (제한 없이)
        table_results = await self.get_all_inquiries_from_all_tables(
            limit_per_table=1000
        )
        
        stats = {
            'total_inquiries': 0,
            'answered_inquiries': 0,
            'unanswered_inquiries': 0,
            'by_table': {},
            'by_type': {}
        }
        
        for table, inquiries in table_results.items():
            table_stats = {
                'total': len(inquiries),
                'answered': len([i for i in inquiries if i.get('answered_at')]),
                'unanswered': len([i for i in inquiries if not i.get('answered_at')])
            }
            
            stats['by_table'][table] = table_stats
            stats['total_inquiries'] += table_stats['total']
            stats['answered_inquiries'] += table_stats['answered']
            stats['unanswered_inquiries'] += table_stats['unanswered']
            
            # 타입별 통계
            inquiry_type = self.table_mappings[table]['type']
            stats['by_type'][inquiry_type] = table_stats
        
        # 답변률 계산
        if stats['total_inquiries'] > 0:
            stats['answer_rate'] = (
                stats['answered_inquiries'] / stats['total_inquiries'] * 100
            )
        else:
            stats['answer_rate'] = 0
            
        return stats
    
    async def convert_to_inquiry_objects(
        self, 
        inquiry_data_list: List[Dict]
    ) -> List[Inquiry]:
        """
        딕셔너리 형태의 문의 데이터를 Inquiry 객체로 변환
        
        Args:
            inquiry_data_list: 문의 데이터 딕셔너리 리스트
            
        Returns:
            Inquiry 객체 리스트
        """
        inquiry_objects = []
        
        for data in inquiry_data_list:
            try:
                # 필수 필드 확인 및 기본값 설정
                if 'id' not in data:
                    data['id'] = data.get('post_id', f"temp_{len(inquiry_objects)}")
                
                if 'post_id' not in data:
                    data['post_id'] = data['id']
                
                if 'content' not in data:
                    data['content'] = data.get('message', data.get('text', ''))
                
                # Inquiry 객체 생성
                inquiry = Inquiry.from_dict(data)
                inquiry_objects.append(inquiry)
                
            except Exception as e:
                print(f"문의 객체 변환 중 오류: {e}, 데이터: {data}")
                continue
                
        return inquiry_objects
    
    def print_unified_summary(self, inquiries: List[Dict]) -> None:
        """
        통합 문의 목록 요약 출력
        
        Args:
            inquiries: 통합된 문의 데이터 리스트
        """
        print(f"\n[통합 문의 목록] - 총 {len(inquiries)}건")
        print("=" * 100)
        
        if not inquiries:
            print("데이터가 없습니다.")
            return
        
        # 헤더 출력
        print(f"{'타입':<12} {'ID':<15} {'제목/내용':<30} {'작성일':<15} {'답변여부':<8}")
        print("-" * 100)
        
        # 각 문의 출력
        for inquiry in inquiries[:20]:  # 최대 20개만 출력
            try:
                inquiry_type = inquiry.get('inquiry_type', '알 수 없음') or '알 수 없음'
                inquiry_id = str(inquiry.get('id', '') or '')[:14]

                # 제목 또는 내용 (길이 제한)
                title = inquiry.get('subject') or inquiry.get('content') or '내용 없음'
                if len(title) > 29:
                    title = title[:26] + '...'

                created_at = inquiry.get('created_at') or ''
                if isinstance(created_at, str) and len(created_at) > 14:
                    created_at = created_at[:14]
                elif not isinstance(created_at, str):
                    created_at = str(created_at)[:14] if created_at else ''

                status = "✅" if inquiry.get('answered_at') else "❌"

                print(f"{inquiry_type:<12} {inquiry_id:<15} {title:<30} {created_at:<15} {status:<8}")

            except Exception as e:
                print(f"문의 출력 중 오류: {e}, 데이터: {inquiry}")
                continue
        
        if len(inquiries) > 20:
            print(f"... 외 {len(inquiries) - 20}건")
            
        print("=" * 100)
