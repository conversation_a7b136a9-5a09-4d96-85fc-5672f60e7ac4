"""
최적화된 문의 분석 서비스 - <PERSON><PERSON><PERSON> 출력, 성능 개선, 질문-답변 분류 및 태그 생성
"""
import asyncio
import json
import time
from typing import Dict, List, Optional, Tuple, Set
from dataclasses import dataclass, asdict
from concurrent.futures import ThreadPoolExecutor
import re

from src.services.async_inquiry_service import AsyncInquiryService
from src.services.embedding_model_service import EmbeddingModelService
from src.services.llm_service import LLMService
from src.utils.async_utils import async_timed


@dataclass
class QuestionAnalysis:
    """질문 분석 결과"""
    category: str
    confidence: float
    tags: List[str]
    keywords: List[str]
    sentiment: str  # 긍정/중립/부정/긴급
    urgency: str   # 낮음/보통/높음/긴급


@dataclass
class AnswerAnalysis:
    """답변 분석 결과"""
    quality_score: float  # 0-1 답변 품질 점수
    response_type: str    # 해결완료/부분해결/안내/거절/기타
    tags: List[str]
    keywords: List[str]
    satisfaction_indicator: str  # 만족/보통/불만족/불명


@dataclass
class InquiryAnalysisResult:
    """종합 문의 분석 결과"""
    inquiry_id: str
    created_at: str
    
    # 원본 데이터
    subject: str
    question_content: str
    answer_content: str
    
    # 질문 분석
    question_analysis: QuestionAnalysis
    
    # 답변 분석 (답변이 있는 경우)
    answer_analysis: Optional[AnswerAnalysis]
    
    # 종합 분석
    resolution_status: str  # 해결됨/미해결/부분해결
    processing_time_ms: float
    
    def to_dict(self) -> Dict:
        """딕셔너리로 변환 (JSON 직렬화 가능)"""
        result = asdict(self)

        # JSON 직렬화를 위한 타입 변환
        def convert_value(value):
            if hasattr(value, 'item'):  # numpy types
                return value.item()
            elif isinstance(value, (list, tuple)):
                return [convert_value(v) for v in value]
            elif isinstance(value, dict):
                return {k: convert_value(v) for k, v in value.items()}
            elif hasattr(value, '__dict__'):  # dataclass objects
                return convert_value(asdict(value))
            else:
                return value

        # 모든 값 변환 및 None 값 제거
        return {k: convert_value(v) for k, v in result.items() if v is not None}


class OptimizedInquiryAnalyzer:
    """최적화된 문의 분석기"""
    
    def __init__(self, supabase_url: str, supabase_key: str):
        """
        분석기 초기화
        
        Args:
            supabase_url: Supabase URL
            supabase_key: Supabase API 키
        """
        self.async_service = AsyncInquiryService(supabase_url, supabase_key)
        self.embedding_service = None
        self.llm_service = None
        
        # 성능 최적화를 위한 캐시
        self._category_cache = {}
        self._tag_cache = {}
        
        # 한국어 이커머스 특화 설정
        self.question_categories = [
            "반품요청", "교환요청", "환불문의", "배송문의", 
            "상품문의", "주문취소", "계정문의", "쿠폰문의", 
            "사이트이용", "기타"
        ]
        
        self.answer_types = [
            "해결완료", "부분해결", "안내제공", "거절응답", "기타"
        ]
        
        # 태그 생성을 위한 키워드 패턴
        self.tag_patterns = {
            "긴급": ["급해", "빨리", "긴급", "당장", "오늘", "지금"],
            "불만": ["화나", "짜증", "불만", "실망", "최악", "엉망"],
            "칭찬": ["좋아", "만족", "감사", "훌륭", "최고", "완벽"],
            "배송": ["배송", "택배", "발송", "도착", "받았", "언제"],
            "결제": ["결제", "카드", "계좌", "입금", "환불", "취소"],
            "상품": ["제품", "상품", "품질", "색상", "사이즈", "불량"],
            "서비스": ["직원", "상담", "응답", "친절", "불친절", "서비스"]
        }
    
    def _init_ai_services(self):
        """AI 서비스 지연 초기화 (성능 최적화)"""
        if self.embedding_service is None:
            try:
                self.embedding_service = EmbeddingModelService()
                print("✅ 임베딩 모델 로드 완료")
            except Exception as e:
                print(f"⚠️ 임베딩 모델 로드 실패: {e}")
        
        if self.llm_service is None:
            try:
                from config.config import OPENAI_API_KEY
                if OPENAI_API_KEY:
                    self.llm_service = LLMService()
                    print("✅ LLM 서비스 초기화 완료")
            except Exception as e:
                print(f"⚠️ LLM 서비스 초기화 실패: {e}")
    
    def _extract_tags(self, text: str) -> List[str]:
        """텍스트에서 태그 추출 (성능 최적화된 버전)"""
        if not text:
            return []
        
        # 캐시 확인
        cache_key = hash(text[:100])  # 처음 100자로 캐시 키 생성
        if cache_key in self._tag_cache:
            return self._tag_cache[cache_key]
        
        text_lower = text.lower()
        tags = []
        
        for tag, keywords in self.tag_patterns.items():
            if any(keyword in text_lower for keyword in keywords):
                tags.append(tag)
        
        # 추가 패턴 기반 태그
        if re.search(r'[!]{2,}|[?]{2,}', text):
            tags.append("강조")
        
        if re.search(r'\d+원|\d+만원|\d+천원', text):
            tags.append("금액언급")
        
        if re.search(r'\d{4}-\d{2}-\d{2}|\d{2}/\d{2}', text):
            tags.append("날짜언급")
        
        # 캐시 저장
        self._tag_cache[cache_key] = tags
        return tags
    
    def _extract_keywords(self, text: str, max_keywords: int = 5) -> List[str]:
        """키워드 추출 (간단한 빈도 기반)"""
        if not text:
            return []
        
        # 한국어 불용어
        stopwords = {'은', '는', '이', '가', '을', '를', '에', '의', '와', '과', '도', '만', '부터', '까지', '로', '으로'}
        
        # 단어 추출 (한글, 영문, 숫자만)
        words = re.findall(r'[가-힣a-zA-Z0-9]+', text)
        words = [w for w in words if len(w) > 1 and w not in stopwords]
        
        # 빈도 계산
        word_freq = {}
        for word in words:
            word_freq[word] = word_freq.get(word, 0) + 1
        
        # 빈도순 정렬
        sorted_words = sorted(word_freq.items(), key=lambda x: x[1], reverse=True)
        return [word for word, freq in sorted_words[:max_keywords]]
    
    def _analyze_question(self, subject: str, content: str) -> QuestionAnalysis:
        """질문 분석 (최적화된 버전)"""
        full_question = f"{subject} {content}".strip()
        
        # AI 분류 (캐시 활용)
        cache_key = hash(full_question[:200])
        if cache_key in self._category_cache:
            category, confidence = self._category_cache[cache_key]
        else:
            if self.embedding_service:
                category, confidence = self.embedding_service.classify_text(full_question)
                self._category_cache[cache_key] = (category, confidence)
            else:
                category, confidence = "기타", 0.5
        
        # 태그 및 키워드 추출
        tags = self._extract_tags(full_question)
        keywords = self._extract_keywords(full_question)
        
        # 감정 분석 (간단한 규칙 기반)
        sentiment = "중립"
        if any(word in full_question.lower() for word in ["급해", "빨리", "긴급"]):
            sentiment = "긴급"
        elif any(word in full_question.lower() for word in ["화나", "짜증", "불만"]):
            sentiment = "부정"
        elif any(word in full_question.lower() for word in ["감사", "좋아", "만족"]):
            sentiment = "긍정"
        
        # 긴급도 분석
        urgency = "보통"
        if sentiment == "긴급" or "오늘" in full_question or "지금" in full_question:
            urgency = "긴급"
        elif any(word in full_question.lower() for word in ["빨리", "급해"]):
            urgency = "높음"
        
        return QuestionAnalysis(
            category=category,
            confidence=confidence,
            tags=tags,
            keywords=keywords,
            sentiment=sentiment,
            urgency=urgency
        )
    
    def _analyze_answer(self, answer: str) -> Optional[AnswerAnalysis]:
        """답변 분석"""
        if not answer or answer.strip() == "nan":
            return None
        
        # 답변 품질 점수 (길이, 정중함, 구체성 기반)
        quality_score = min(1.0, len(answer) / 200)  # 기본 길이 점수
        
        if any(word in answer for word in ["안녕하세요", "감사합니다", "죄송합니다"]):
            quality_score += 0.2  # 정중함 보너스
        
        if any(word in answer for word in ["주문번호", "운송장", "연락드리겠습니다"]):
            quality_score += 0.2  # 구체성 보너스
        
        quality_score = min(1.0, quality_score)
        
        # 응답 타입 분석
        response_type = "기타"
        if any(word in answer for word in ["완료", "처리", "해결"]):
            response_type = "해결완료"
        elif any(word in answer for word in ["확인", "검토", "안내"]):
            response_type = "안내제공"
        elif any(word in answer for word in ["어려운", "불가능", "죄송"]):
            response_type = "거절응답"
        
        # 태그 및 키워드
        tags = self._extract_tags(answer)
        keywords = self._extract_keywords(answer)
        
        # 만족도 지표 (답변 내용 기반 추정)
        satisfaction_indicator = "보통"
        if quality_score > 0.8:
            satisfaction_indicator = "만족"
        elif quality_score < 0.4:
            satisfaction_indicator = "불만족"
        
        return AnswerAnalysis(
            quality_score=quality_score,
            response_type=response_type,
            tags=tags,
            keywords=keywords,
            satisfaction_indicator=satisfaction_indicator
        )
    
    @async_timed()
    async def analyze_inquiries_batch(
        self, 
        limit: int = 50,
        use_parallel: bool = True
    ) -> List[InquiryAnalysisResult]:
        """
        배치 문의 분석 (성능 최적화)
        
        Args:
            limit: 분석할 문의 수
            use_parallel: 병렬 처리 사용 여부
            
        Returns:
            분석 결과 리스트
        """
        start_time = time.time()
        
        # AI 서비스 초기화
        self._init_ai_services()
        
        # 데이터 조회 (최적화된 쿼리)
        print(f"📊 {limit}건의 문의 데이터 조회 중...")
        raw_inquiries = await self.async_service.get_all_inquiries(
            table='personal',
            limit=limit,
            only_unanswered=False
        )
        
        # 의미있는 데이터만 필터링
        valid_inquiries = []
        for inquiry in raw_inquiries:
            content = inquiry.get('content', '') or ''
            if len(content.strip()) > 10:  # 최소 길이 조건
                valid_inquiries.append(inquiry)
        
        print(f"✅ 유효한 문의 {len(valid_inquiries)}건 필터링 완료")
        
        # 분석 실행
        results = []
        
        if use_parallel and len(valid_inquiries) > 10:
            # 병렬 처리 (대용량 데이터용)
            print("🚀 병렬 처리 모드로 분석 중...")
            
            def analyze_single(inquiry_data):
                return self._analyze_single_inquiry(inquiry_data, start_time)
            
            with ThreadPoolExecutor(max_workers=4) as executor:
                futures = [executor.submit(analyze_single, inq) for inq in valid_inquiries]
                for future in futures:
                    try:
                        result = future.result(timeout=30)
                        if result:
                            results.append(result)
                    except Exception as e:
                        print(f"⚠️ 개별 분석 실패: {e}")
        else:
            # 순차 처리 (소용량 데이터용)
            print("📝 순차 처리 모드로 분석 중...")
            for inquiry in valid_inquiries:
                try:
                    result = self._analyze_single_inquiry(inquiry, start_time)
                    if result:
                        results.append(result)
                except Exception as e:
                    print(f"⚠️ 개별 분석 실패: {e}")
        
        total_time = time.time() - start_time
        print(f"✅ 총 {len(results)}건 분석 완료 (소요시간: {total_time:.2f}초)")
        
        return results
    
    def _analyze_single_inquiry(self, inquiry: Dict, start_time: float) -> Optional[InquiryAnalysisResult]:
        """개별 문의 분석"""
        try:
            processing_start = time.time()
            
            # 데이터 추출
            inquiry_id = inquiry.get('id', '')
            subject = inquiry.get('subject', '') or ''
            content = inquiry.get('content', '') or ''
            answer = inquiry.get('answer', '') or ''
            created_at = inquiry.get('created_at', '') or ''
            
            # 질문 분석
            question_analysis = self._analyze_question(subject, content)
            
            # 답변 분석
            answer_analysis = self._analyze_answer(answer) if answer and answer != 'nan' else None
            
            # 해결 상태 판단
            resolution_status = "미해결"
            if answer_analysis:
                if answer_analysis.response_type == "해결완료":
                    resolution_status = "해결됨"
                elif answer_analysis.response_type in ["안내제공", "부분해결"]:
                    resolution_status = "부분해결"
            
            processing_time = (time.time() - processing_start) * 1000  # ms
            
            return InquiryAnalysisResult(
                inquiry_id=inquiry_id,
                created_at=created_at,
                subject=subject,
                question_content=content,
                answer_content=answer if answer != 'nan' else '',
                question_analysis=question_analysis,
                answer_analysis=answer_analysis,
                resolution_status=resolution_status,
                processing_time_ms=processing_time
            )
            
        except Exception as e:
            print(f"⚠️ 문의 {inquiry.get('id', 'unknown')} 분석 실패: {e}")
            return None
    
    def export_to_json(
        self, 
        results: List[InquiryAnalysisResult], 
        filename: str = None
    ) -> Dict:
        """
        분석 결과를 JSON으로 내보내기
        
        Args:
            results: 분석 결과 리스트
            filename: 저장할 파일명 (None이면 파일 저장 안함)
            
        Returns:
            JSON 구조화된 결과
        """
        # 통계 계산
        total_count = len(results)
        answered_count = sum(1 for r in results if r.answer_analysis is not None)
        avg_processing_time = sum(r.processing_time_ms for r in results) / total_count if total_count > 0 else 0
        
        # 카테고리별 통계
        category_stats = {}
        for result in results:
            category = result.question_analysis.category
            if category not in category_stats:
                category_stats[category] = 0
            category_stats[category] += 1
        
        # 태그 통계
        all_tags = []
        for result in results:
            all_tags.extend(result.question_analysis.tags)
            if result.answer_analysis:
                all_tags.extend(result.answer_analysis.tags)
        
        tag_stats = {}
        for tag in all_tags:
            tag_stats[tag] = tag_stats.get(tag, 0) + 1
        
        # JSON 구조 생성
        json_output = {
            "metadata": {
                "analysis_timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
                "total_inquiries": total_count,
                "answered_inquiries": answered_count,
                "answer_rate": (answered_count / total_count * 100) if total_count > 0 else 0,
                "avg_processing_time_ms": round(avg_processing_time, 2)
            },
            "statistics": {
                "category_distribution": category_stats,
                "tag_frequency": dict(sorted(tag_stats.items(), key=lambda x: x[1], reverse=True)[:10]),
                "resolution_status": {
                    "resolved": sum(1 for r in results if r.resolution_status == "해결됨"),
                    "partially_resolved": sum(1 for r in results if r.resolution_status == "부분해결"),
                    "unresolved": sum(1 for r in results if r.resolution_status == "미해결")
                }
            },
            "inquiries": [result.to_dict() for result in results]
        }
        
        # 파일 저장
        if filename:
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(json_output, f, ensure_ascii=False, indent=2)
                print(f"📄 결과를 {filename}에 저장했습니다.")
            except Exception as e:
                print(f"⚠️ 파일 저장 실패: {e}")
        
        return json_output


async def run_optimized_analysis():
    """최적화된 분석 실행"""
    from config.config import SUPABASE_URL, SUPABASE_KEY
    
    print("🚀 최적화된 문의 분석 시작...")
    
    analyzer = OptimizedInquiryAnalyzer(SUPABASE_URL, SUPABASE_KEY)
    
    try:
        # 분석 실행
        results = await analyzer.analyze_inquiries_batch(
            limit=30,  # 테스트용으로 30건
            use_parallel=True
        )
        
        # JSON 출력
        json_output = analyzer.export_to_json(
            results, 
            filename="inquiry_analysis_results.json"
        )
        
        # 요약 출력
        print("\n" + "="*80)
        print("📊 최적화된 분석 결과 요약")
        print("="*80)
        print(f"총 분석: {json_output['metadata']['total_inquiries']}건")
        print(f"답변률: {json_output['metadata']['answer_rate']:.1f}%")
        print(f"평균 처리시간: {json_output['metadata']['avg_processing_time_ms']:.2f}ms")
        
        print(f"\n🏷️ 상위 카테고리:")
        for category, count in list(json_output['statistics']['category_distribution'].items())[:5]:
            print(f"  • {category}: {count}건")
        
        print(f"\n🔖 상위 태그:")
        for tag, count in list(json_output['statistics']['tag_frequency'].items())[:5]:
            print(f"  • {tag}: {count}회")
        
        print(f"\n✅ JSON 파일로 상세 결과 저장 완료!")
        
        return json_output
        
    except Exception as e:
        print(f"❌ 분석 중 오류: {e}")
        import traceback
        traceback.print_exc()
        return None


if __name__ == "__main__":
    asyncio.run(run_optimized_analysis())
