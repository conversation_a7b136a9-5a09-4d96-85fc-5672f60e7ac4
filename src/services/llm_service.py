"""
LLM 백업 서비스 - OpenAI API 연동
"""
import asyncio
import json
import logging
from typing import List, Optional, Tuple
from openai import Async<PERSON>penAI

from config.config import OPENAI_API_KEY
from src.models.classification import InquiryCategory


class LLMService:
    """OpenAI API를 사용한 LLM 백업 서비스"""

    def __init__(self, api_key: Optional[str] = None):
        """
        LLM 서비스 초기화

        Args:
            api_key: OpenAI API 키 (없으면 환경변수에서 가져옴)
        """
        self.api_key = api_key or OPENAI_API_KEY
        if not self.api_key:
            raise ValueError("OpenAI API 키가 설정되지 않았습니다.")

        self.client = AsyncOpenAI(api_key=self.api_key)
        self.model = "gpt-3.5-turbo"
        self.max_tokens = 150
        self.temperature = 0.1

        # 카테고리 목록
        self.categories = [cat.value for cat in InquiryCategory]

        # 분류 프롬프트 템플릿
        self.classification_prompt = self._create_classification_prompt()

    def _create_classification_prompt(self) -> str:
        """분류용 프롬프트 템플릿 생성"""
        categories_str = "\n".join([f"- {cat}" for cat in self.categories])

        return f"""
다음 이커머스 고객 문의를 아래 카테고리 중 하나로 분류해주세요:

{categories_str}

문의 내용: {{content}}

응답 형식:
카테고리: [선택된 카테고리]
신뢰도: [0.0-1.0 사이의 숫자]
이유: [분류 이유 간단히]

응답:"""

    async def classify_inquiry(self, content: str) -> Tuple[str, float]:
        """
        문의 내용을 분류

        Args:
            content: 분류할 문의 내용

        Returns:
            (카테고리, 신뢰도) 튜플
        """
        try:
            prompt = self.classification_prompt.format(content=content)

            response = await self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {
                        "role": "system",
                        "content": "당신은 이커머스 고객 문의를 분류하는 전문가입니다."
                    },
                    {"role": "user", "content": prompt}
                ],
                max_tokens=self.max_tokens,
                temperature=self.temperature
            )

            result = response.choices[0].message.content.strip()
            return self._parse_classification_result(result)

        except Exception as e:
            logging.error(f"LLM 분류 중 오류 발생: {e}")
            return "기타/불명확", 0.5

    def _parse_classification_result(self, result: str) -> Tuple[str, float]:
        """
        LLM 응답 결과 파싱

        Args:
            result: LLM 응답 텍스트

        Returns:
            (카테고리, 신뢰도) 튜플
        """
        try:
            lines = result.split('\n')
            category = "기타/불명확"
            confidence = 0.5

            for line in lines:
                line = line.strip()
                if line.startswith('카테고리:'):
                    category_text = line.replace('카테고리:', '').strip()
                    # 유효한 카테고리인지 확인
                    if category_text in self.categories:
                        category = category_text
                elif line.startswith('신뢰도:'):
                    try:
                        confidence_text = line.replace('신뢰도:', '').strip()
                        confidence = float(confidence_text)
                        # 0-1 범위로 제한
                        confidence = max(0.0, min(1.0, confidence))
                    except ValueError:
                        confidence = 0.5

            return category, confidence

        except Exception as e:
            logging.error(f"LLM 결과 파싱 중 오류: {e}")
            return "기타/불명확", 0.5

    async def bulk_classify(
        self, contents: List[str]
    ) -> List[Tuple[str, float]]:
        """
        여러 문의를 일괄 분류

        Args:
            contents: 분류할 문의 내용 목록

        Returns:
            (카테고리, 신뢰도) 튜플 목록
        """
        tasks = [self.classify_inquiry(content) for content in contents]
        return await asyncio.gather(*tasks, return_exceptions=True)

    async def generate_response(
        self, inquiry_content: str, category: str
    ) -> str:
        """
        문의에 대한 응답 생성

        Args:
            inquiry_content: 문의 내용
            category: 분류된 카테고리

        Returns:
            생성된 응답
        """
        try:
            prompt = f"""
다음은 '{category}' 카테고리의 고객 문의입니다. 친절하고 도움이 되는 응답을 생성해주세요:

문의: {inquiry_content}

응답:"""

            response = await self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": "당신은 친절한 고객서비스 담당자입니다."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=300,
                temperature=0.3
            )

            return response.choices[0].message.content.strip()

        except Exception as e:
            logging.error(f"응답 생성 중 오류 발생: {e}")
            return ("죄송합니다. 현재 시스템 문제로 응답을 생성할 수 없습니다. "
                    "잠시 후 다시 시도해주세요.")