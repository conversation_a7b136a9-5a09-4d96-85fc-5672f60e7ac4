"""
LLM 백업 서비스 - OpenAI API 연동
"""
import asyncio
import json
import logging
from typing import List, Optional, Tuple
from openai import AsyncOpenAI

from config.config import OPENAI_API_KEY
from src.models.classification import InquiryCategory


class LLMService:
    """OpenAI API를 사용한 LLM 백업 서비스"""

    def __init__(self, api_key: Optional[str] = None):
        """
        LLM 서비스 초기화

        Args:
            api_key: OpenAI API 키 (없으면 환경변수에서 가져옴)
        """
        self.api_key = api_key or OPENAI_API_KEY
        if not self.api_key:
            raise ValueError("OpenAI API 키가 설정되지 않았습니다.")

        self.client = AsyncOpenAI(api_key=self.api_key)
        self.model = "gpt-3.5-turbo"
        self.max_tokens = 150
        self.temperature = 0.1

        # 카테고리 목록
        self.categories = [cat.value for cat in InquiryCategory]

        # 분류 프롬프트 템플릿
        self.classification_prompt = self._create_classification_prompt()

    def _create_classification_prompt(self) -> str:
        """한국어 이커머스 최적화 분류용 프롬프트 템플릿 생성"""
        categories_str = "\n".join([f"- {cat}" for cat in self.categories])

        return f"""당신은 한국 이커머스 고객문의 분류 전문 AI입니다.
다음 고객 문의를 정확히 분석하여 JSON 형식으로 분류해주세요.

분류 카테고리:
{categories_str}

추가 분석 항목:
- sentiment: 긍정/중립/부정/긴급
- stage: 주문전/주문중/배송중/구매후/기타
- priority: 낮음/보통/높음/긴급

고객 문의: "{{content}}"

다음 JSON 형식으로 정확히 응답하세요:
{{
    "category": "[위 카테고리 중 하나]",
    "confidence": [0.0-1.0 사이 숫자],
    "sentiment": "[긍정/중립/부정/긴급 중 하나]",
    "stage": "[주문전/주문중/배송중/구매후/기타 중 하나]",
    "priority": "[낮음/보통/높음/긴급 중 하나]",
    "reason": "[분류 이유 한 문장]"
}}

JSON:"""

    async def classify_inquiry(self, content: str) -> Tuple[str, float]:
        """
        문의 내용을 분류

        Args:
            content: 분류할 문의 내용

        Returns:
            (카테고리, 신뢰도) 튜플
        """
        try:
            prompt = self.classification_prompt.format(content=content)

            response = await self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {
                        "role": "system",
                        "content": "당신은 이커머스 고객 문의를 분류하는 전문가입니다."
                    },
                    {"role": "user", "content": prompt}
                ],
                max_tokens=self.max_tokens,
                temperature=self.temperature
            )

            result = response.choices[0].message.content.strip()
            return self._parse_classification_result(result)

        except Exception as e:
            logging.error(f"LLM 분류 중 오류 발생: {e}")
            return "기타/불명확", 0.5

    def _parse_classification_result(self, result: str) -> Tuple[str, float]:
        """
        LLM JSON 응답 결과 파싱

        Args:
            result: LLM 응답 텍스트 (JSON 형식)

        Returns:
            (카테고리, 신뢰도) 튜플
        """
        try:
            # JSON 파싱 시도

            # JSON 부분만 추출 (앞뒤 텍스트 제거)
            json_start = result.find('{')
            json_end = result.rfind('}') + 1

            if json_start != -1 and json_end > json_start:
                json_str = result[json_start:json_end]
                parsed = json.loads(json_str)

                category = parsed.get('category', '기타/불명확')
                confidence = float(parsed.get('confidence', 0.5))

                # 유효성 검증
                if category not in self.categories:
                    category = "기타/불명확"

                confidence = max(0.0, min(1.0, confidence))

                # 추가 정보 로깅
                sentiment = parsed.get('sentiment', '중립')
                stage = parsed.get('stage', '기타')
                priority = parsed.get('priority', '보통')
                reason = parsed.get('reason', '')

                logging.info(
                    f"LLM 분류 결과 - 카테고리: {category}, "
                    f"감정: {sentiment}, 단계: {stage}, "
                    f"우선순위: {priority}, 이유: {reason}"
                )

                return category, confidence
            else:
                # JSON 형식이 아닌 경우 기존 방식으로 파싱
                return self._parse_legacy_format(result)

        except json.JSONDecodeError:
            # JSON 파싱 실패 시 기존 방식으로 파싱
            return self._parse_legacy_format(result)
        except Exception as e:
            logging.error(f"LLM 결과 파싱 중 오류: {e}")
            return "기타/불명확", 0.5

    def _parse_legacy_format(self, result: str) -> Tuple[str, float]:
        """
        기존 형식의 LLM 응답 파싱 (백업용)

        Args:
            result: LLM 응답 텍스트

        Returns:
            (카테고리, 신뢰도) 튜플
        """
        try:
            lines = result.split('\n')
            category = "기타/불명확"
            confidence = 0.5

            for line in lines:
                line = line.strip()
                if line.startswith('카테고리:'):
                    category_text = line.replace('카테고리:', '').strip()
                    if category_text in self.categories:
                        category = category_text
                elif line.startswith('신뢰도:'):
                    try:
                        confidence_text = line.replace('신뢰도:', '').strip()
                        confidence = float(confidence_text)
                        confidence = max(0.0, min(1.0, confidence))
                    except ValueError:
                        confidence = 0.5

            return category, confidence

        except Exception as e:
            logging.error(f"레거시 형식 파싱 중 오류: {e}")
            return "기타/불명확", 0.5

    async def bulk_classify(
        self, contents: List[str]
    ) -> List[Tuple[str, float]]:
        """
        여러 문의를 일괄 분류

        Args:
            contents: 분류할 문의 내용 목록

        Returns:
            (카테고리, 신뢰도) 튜플 목록
        """
        tasks = [self.classify_inquiry(content) for content in contents]
        return await asyncio.gather(*tasks, return_exceptions=True)

    async def generate_response(
        self, inquiry_content: str, category: str
    ) -> str:
        """
        문의에 대한 응답 생성

        Args:
            inquiry_content: 문의 내용
            category: 분류된 카테고리

        Returns:
            생성된 응답
        """
        try:
            prompt = f"""
다음은 '{category}' 카테고리의 고객 문의입니다. 친절하고 도움이 되는 응답을 생성해주세요:

문의: {inquiry_content}

응답:"""

            response = await self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": "당신은 친절한 고객서비스 담당자입니다."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=300,
                temperature=0.3
            )

            return response.choices[0].message.content.strip()

        except Exception as e:
            logging.error(f"응답 생성 중 오류 발생: {e}")
            return ("죄송합니다. 현재 시스템 문제로 응답을 생성할 수 없습니다. "
                    "잠시 후 다시 시도해주세요.")