"""
종합적인 문의 분석 서비스 - subject, content, answer를 모두 활용
"""
import asyncio
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass

from src.services.async_inquiry_service import AsyncInquiryService
from src.services.embedding_model_service import EmbeddingModelService
from src.services.llm_service import LLMService
from src.utils.async_utils import async_timed


@dataclass
class ComprehensiveInquiry:
    """종합적인 문의 데이터 클래스"""
    id: str
    subject: str = ""
    content: str = ""  # 실제 사용자 질문
    answer: str = ""   # 셀러 답변
    created_at: str = ""
    answered_at: str = ""
    
    @property
    def full_question(self) -> str:
        """제목과 내용을 결합한 완전한 질문"""
        parts = []
        if self.subject and self.subject.strip():
            parts.append(self.subject.strip())
        if self.content and self.content.strip():
            parts.append(self.content.strip())
        return " | ".join(parts) if parts else ""
    
    @property
    def has_answer(self) -> bool:
        """답변이 있는지 확인"""
        return bool(self.answer and self.answer.strip())
    
    @property
    def is_meaningful(self) -> bool:
        """의미있는 문의인지 확인 (내용이 충분한지)"""
        full_text = self.full_question
        return len(full_text.strip()) > 10


class ComprehensiveInquiryService:
    """subject, content, answer를 종합 분석하는 서비스"""
    
    def __init__(self, supabase_url: str, supabase_key: str):
        """
        종합 문의 서비스 초기화
        
        Args:
            supabase_url: Supabase URL
            supabase_key: Supabase API 키
        """
        self.async_service = AsyncInquiryService(supabase_url, supabase_key)
        self.embedding_service = None
        self.llm_service = None
        
        # 이커머스 특화 카테고리
        self.categories = [
            "반품 요청", "교환 요청", "환불 문의", "배송 지연/오류",
            "상품 정보 문의", "주문 변경/취소", "회원/계정 관련", 
            "쿠폰/적립금", "사이트 이용", "기타/불명확"
        ]
    
    def _init_ai_services(self):
        """AI 서비스 지연 초기화"""
        if self.embedding_service is None:
            try:
                self.embedding_service = EmbeddingModelService()
                print("✅ 한국어 임베딩 모델 로드 완료")
            except Exception as e:
                print(f"⚠️ 임베딩 모델 로드 실패: {e}")
        
        if self.llm_service is None:
            try:
                from config.config import OPENAI_API_KEY
                if OPENAI_API_KEY:
                    self.llm_service = LLMService()
                    print("✅ LLM 서비스 초기화 완료")
            except Exception as e:
                print(f"⚠️ LLM 서비스 초기화 실패: {e}")
    
    @async_timed()
    async def get_comprehensive_inquiries(
        self, 
        limit: int = 50,
        only_unanswered: bool = False,
        only_meaningful: bool = True
    ) -> List[ComprehensiveInquiry]:
        """
        종합적인 문의 데이터 조회
        
        Args:
            limit: 최대 조회 수
            only_unanswered: 미답변 문의만 조회
            only_meaningful: 의미있는 문의만 조회
            
        Returns:
            ComprehensiveInquiry 객체 리스트
        """
        # 원본 데이터 조회
        raw_inquiries = await self.async_service.get_all_inquiries(
            table='personal',
            limit=limit * 2,  # 필터링을 고려해 더 많이 조회
            only_unanswered=only_unanswered
        )
        
        # ComprehensiveInquiry 객체로 변환
        comprehensive_inquiries = []
        for raw in raw_inquiries:
            inquiry = ComprehensiveInquiry(
                id=raw.get('id', ''),
                subject=raw.get('subject', '') or '',
                content=raw.get('content', '') or '',
                answer=raw.get('answer', '') or '',
                created_at=raw.get('created_at', '') or '',
                answered_at=raw.get('answered_at', '') or ''
            )
            
            # 필터링 조건 적용
            if only_meaningful and not inquiry.is_meaningful:
                continue
            
            comprehensive_inquiries.append(inquiry)
            
            # 원하는 수만큼 수집되면 중단
            if len(comprehensive_inquiries) >= limit:
                break
        
        return comprehensive_inquiries
    
    @async_timed()
    async def analyze_inquiry_patterns(
        self, 
        inquiries: List[ComprehensiveInquiry]
    ) -> Dict:
        """
        문의 패턴 분석
        
        Args:
            inquiries: 분석할 문의 리스트
            
        Returns:
            분석 결과 딕셔너리
        """
        if not inquiries:
            return {}
        
        analysis = {
            'total_count': len(inquiries),
            'answered_count': sum(1 for inq in inquiries if inq.has_answer),
            'meaningful_count': sum(1 for inq in inquiries if inq.is_meaningful),
            'avg_question_length': 0,
            'avg_answer_length': 0,
            'common_keywords': [],
            'answer_rate': 0
        }
        
        # 길이 통계
        question_lengths = [len(inq.full_question) for inq in inquiries if inq.full_question]
        answer_lengths = [len(inq.answer) for inq in inquiries if inq.answer]
        
        if question_lengths:
            analysis['avg_question_length'] = sum(question_lengths) / len(question_lengths)
        if answer_lengths:
            analysis['avg_answer_length'] = sum(answer_lengths) / len(answer_lengths)
        
        # 답변률
        if analysis['total_count'] > 0:
            analysis['answer_rate'] = analysis['answered_count'] / analysis['total_count'] * 100
        
        # 키워드 분석 (간단한 버전)
        all_text = ' '.join([inq.full_question for inq in inquiries if inq.full_question])
        keywords = ['반품', '교환', '환불', '배송', '주문', '취소', '상품', '문의', '불량', '지연']
        keyword_counts = {kw: all_text.count(kw) for kw in keywords}
        analysis['common_keywords'] = sorted(keyword_counts.items(), key=lambda x: x[1], reverse=True)[:5]
        
        return analysis
    
    @async_timed()
    async def classify_inquiries_with_ai(
        self, 
        inquiries: List[ComprehensiveInquiry],
        use_llm_fallback: bool = True
    ) -> List[Dict]:
        """
        AI를 사용한 문의 분류
        
        Args:
            inquiries: 분류할 문의 리스트
            use_llm_fallback: LLM 백업 사용 여부
            
        Returns:
            분류 결과 리스트
        """
        self._init_ai_services()
        
        if not self.embedding_service:
            print("❌ AI 분류 서비스를 사용할 수 없습니다.")
            return []
        
        results = []
        
        for inquiry in inquiries:
            if not inquiry.is_meaningful:
                continue
            
            try:
                # 1차: 임베딩 기반 분류
                question_text = inquiry.full_question
                category, confidence = self.embedding_service.classify_text(question_text)
                
                classification_result = {
                    'inquiry_id': inquiry.id,
                    'question': question_text[:100] + '...' if len(question_text) > 100 else question_text,
                    'answer': inquiry.answer[:100] + '...' if len(inquiry.answer) > 100 else inquiry.answer,
                    'category': category,
                    'confidence': confidence,
                    'method': 'embedding',
                    'has_answer': inquiry.has_answer
                }
                
                # 2차: 신뢰도가 낮으면 LLM 백업 (비용 절약을 위해 선택적)
                if (confidence < 0.8 and use_llm_fallback and 
                    self.llm_service and len(results) < 3):  # 처음 3개만 LLM 사용
                    
                    try:
                        llm_category, llm_confidence = await self.llm_service.classify_inquiry(question_text)
                        if llm_confidence > confidence:
                            classification_result.update({
                                'category': llm_category,
                                'confidence': llm_confidence,
                                'method': 'llm_fallback'
                            })
                    except Exception as e:
                        print(f"LLM 분류 실패: {e}")
                
                results.append(classification_result)
                
            except Exception as e:
                print(f"문의 분류 중 오류: {e}")
                continue
        
        return results
    
    def print_comprehensive_analysis(
        self, 
        inquiries: List[ComprehensiveInquiry],
        classifications: List[Dict] = None,
        analysis: Dict = None
    ):
        """
        종합 분석 결과 출력
        
        Args:
            inquiries: 문의 리스트
            classifications: 분류 결과
            analysis: 패턴 분석 결과
        """
        print("\n" + "="*80)
        print("📊 PERSONAL_INQUIRIES 종합 분석 결과")
        print("="*80)
        
        # 기본 통계
        if analysis:
            print(f"\n📈 기본 통계:")
            print(f"  • 총 문의: {analysis['total_count']}건")
            print(f"  • 답변 완료: {analysis['answered_count']}건")
            print(f"  • 답변률: {analysis['answer_rate']:.1f}%")
            print(f"  • 평균 질문 길이: {analysis['avg_question_length']:.0f}자")
            print(f"  • 평균 답변 길이: {analysis['avg_answer_length']:.0f}자")
            
            print(f"\n🔍 주요 키워드:")
            for keyword, count in analysis['common_keywords']:
                print(f"  • {keyword}: {count}회")
        
        # 샘플 데이터 출력
        print(f"\n📝 실제 데이터 샘플 (최대 5개):")
        print("-"*80)
        
        for i, inquiry in enumerate(inquiries[:5], 1):
            print(f"\n{i}. ID: {inquiry.id}")
            
            if inquiry.subject:
                print(f"   📌 제목: {inquiry.subject}")
            
            if inquiry.content:
                content_preview = inquiry.content[:150] + "..." if len(inquiry.content) > 150 else inquiry.content
                print(f"   👤 사용자 질문: {content_preview}")
            
            if inquiry.answer:
                answer_preview = inquiry.answer[:150] + "..." if len(inquiry.answer) > 150 else inquiry.answer
                print(f"   🏪 셀러 답변: {answer_preview}")
            
            print(f"   📅 작성일: {inquiry.created_at}")
            print(f"   ✅ 답변여부: {'있음' if inquiry.has_answer else '없음'}")
        
        # AI 분류 결과
        if classifications:
            print(f"\n🤖 AI 분류 결과 (최대 5개):")
            print("-"*80)
            
            for i, result in enumerate(classifications[:5], 1):
                print(f"\n{i}. 질문: {result['question']}")
                print(f"   🏷️  분류: {result['category']}")
                print(f"   📊 신뢰도: {result['confidence']:.3f}")
                print(f"   🔧 방법: {result['method']}")
                if result['answer']:
                    print(f"   💬 답변: {result['answer']}")
        
        print("\n" + "="*80)


async def run_comprehensive_analysis():
    """종합 분석 실행 함수"""
    from config.config import SUPABASE_URL, SUPABASE_KEY
    
    print("🚀 Personal_Inquiries 종합 분석 시작...")
    
    # 서비스 초기화
    service = ComprehensiveInquiryService(SUPABASE_URL, SUPABASE_KEY)
    
    try:
        # 1. 데이터 조회
        print("\n1️⃣ 실제 데이터 조회 중...")
        inquiries = await service.get_comprehensive_inquiries(
            limit=20,
            only_meaningful=True
        )
        
        # 2. 패턴 분석
        print("\n2️⃣ 문의 패턴 분석 중...")
        analysis = await service.analyze_inquiry_patterns(inquiries)
        
        # 3. AI 분류
        print("\n3️⃣ AI 분류 실행 중...")
        classifications = await service.classify_inquiries_with_ai(
            inquiries[:10],  # 처음 10개만 분류
            use_llm_fallback=True
        )
        
        # 4. 결과 출력
        service.print_comprehensive_analysis(inquiries, classifications, analysis)
        
        print("\n✅ 종합 분석 완료!")
        
    except Exception as e:
        print(f"❌ 분석 중 오류 발생: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(run_comprehensive_analysis())
