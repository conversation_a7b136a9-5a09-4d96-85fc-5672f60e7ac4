"""
QA 분석 에이전트 - 독립적인 모듈로 사용 가능
"""
import asyncio
import json
import time
from typing import Dict, List, Optional, Tuple, Any, Union
from dataclasses import dataclass, asdict

from src.models.ai_model_interfaces import BaseEmbeddingModel, BaseLLMModel
from src.config.model_config_manager import ModelConfigManager, AnalysisConfig
from src.services.async_inquiry_service import AsyncInquiryService


@dataclass
class QAAnalysisRequest:
    """QA 분석 요청"""
    inquiry_id: Optional[str] = None
    question_text: Optional[str] = None
    answer_text: Optional[str] = None
    analysis_type: str = "full"  # full, similarity_only, quality_only
    include_feedback: bool = True


@dataclass
class QAAnalysisResult:
    """QA 분석 결과"""
    # 기본 정보
    inquiry_id: Optional[str]
    question_text: str
    answer_text: str
    
    # 분석 점수
    semantic_similarity: float
    topic_relevance: float
    keyword_overlap: float
    answer_completeness: float
    answer_accuracy: float
    answer_helpfulness: float
    
    # 종합 결과
    overall_score: float
    pass_threshold: bool
    grade: str
    
    # 피드백
    strengths: List[str]
    weaknesses: List[str]
    recommendations: List[str]
    
    # 메타데이터
    analysis_timestamp: str
    processing_time_ms: float
    model_info: Dict[str, str]
    
    def to_dict(self) -> Dict[str, Any]:
        """딕셔너리 변환"""
        return asdict(self)
    
    def to_json(self, **kwargs) -> str:
        """JSON 문자열 변환"""
        return json.dumps(self.to_dict(), ensure_ascii=False, **kwargs)


class QAAnalysisAgent:
    """QA 분석 에이전트"""
    
    def __init__(
        self, 
        config_manager: Optional[ModelConfigManager] = None,
        supabase_url: Optional[str] = None,
        supabase_key: Optional[str] = None
    ):
        """
        에이전트 초기화
        
        Args:
            config_manager: 모델 설정 관리자
            supabase_url: Supabase URL (DB 연동용)
            supabase_key: Supabase API 키 (DB 연동용)
        """
        # 설정 관리자
        if config_manager is None:
            from src.config.model_config_manager import get_config_manager
            self.config_manager = get_config_manager()
        else:
            self.config_manager = config_manager
        
        # 분석 설정
        self.analysis_config = self.config_manager.get_analysis_config()
        
        # 모델 인스턴스 (지연 로딩)
        self.embedding_model: Optional[BaseEmbeddingModel] = None
        self.llm_model: Optional[BaseLLMModel] = None
        
        # DB 서비스 (선택적)
        self.db_service = None
        if supabase_url and supabase_key:
            self.db_service = AsyncInquiryService(supabase_url, supabase_key)
        
        # 캐시
        self._similarity_cache = {}
        self._classification_cache = {}
        
        print(f"✅ QA 분석 에이전트 초기화 완료 (프로필: {self.config_manager.current_profile})")
    
    def _init_models(self) -> None:
        """모델 지연 초기화"""
        if self.embedding_model is None:
            try:
                self.embedding_model = self.config_manager.create_embedding_model()
                self.embedding_model.load_model()
            except Exception as e:
                print(f"⚠️ 임베딩 모델 초기화 실패: {e}")
        
        if self.llm_model is None:
            try:
                self.llm_model = self.config_manager.create_llm_model()
                self.llm_model.initialize()
            except Exception as e:
                print(f"⚠️ LLM 모델 초기화 실패: {e}")
    
    def switch_profile(self, profile_name: str) -> bool:
        """프로필 변경 및 모델 재초기화"""
        if self.config_manager.set_profile(profile_name):
            # 모델 재초기화
            self.embedding_model = None
            self.llm_model = None
            self.analysis_config = self.config_manager.get_analysis_config()
            
            # 캐시 클리어
            self._similarity_cache.clear()
            self._classification_cache.clear()
            
            print(f"✅ 프로필 변경 및 모델 재초기화 완료: {profile_name}")
            return True
        return False
    
    def _calculate_semantic_similarity(self, question: str, answer: str) -> float:
        """의미적 유사도 계산"""
        if not self.embedding_model or not question or not answer:
            return 0.0
        
        # 캐시 확인
        cache_key = hash(f"{question[:100]}{answer[:100]}")
        if cache_key in self._similarity_cache:
            return self._similarity_cache[cache_key]
        
        try:
            similarity = self.embedding_model.calculate_similarity(question, answer)
            self._similarity_cache[cache_key] = similarity
            return similarity
        except Exception as e:
            print(f"⚠️ 유사도 계산 실패: {e}")
            return 0.0
    
    def _calculate_topic_relevance(self, question: str, answer: str) -> float:
        """주제 관련성 계산"""
        if not question or not answer:
            return 0.0
        
        # 간단한 키워드 기반 관련성 계산
        import re
        
        # 한국어 불용어
        stopwords = {'은', '는', '이', '가', '을', '를', '에', '의', '와', '과', '도', '만'}
        
        # 키워드 추출
        question_words = set(re.findall(r'[가-힣a-zA-Z0-9]+', question.lower()))
        answer_words = set(re.findall(r'[가-힣a-zA-Z0-9]+', answer.lower()))
        
        # 불용어 제거
        question_words = {w for w in question_words if len(w) > 1 and w not in stopwords}
        answer_words = {w for w in answer_words if len(w) > 1 and w not in stopwords}
        
        if not question_words:
            return 0.0
        
        # 교집합 비율
        common_words = question_words & answer_words
        relevance = len(common_words) / len(question_words)
        
        return min(1.0, relevance)
    
    def _calculate_keyword_overlap(self, question: str, answer: str) -> float:
        """키워드 겹침도 계산"""
        if not question or not answer:
            return 0.0
        
        import re
        
        question_words = set(re.findall(r'[가-힣a-zA-Z0-9]+', question.lower()))
        answer_words = set(re.findall(r'[가-힣a-zA-Z0-9]+', answer.lower()))
        
        if not question_words and not answer_words:
            return 0.0
        
        overlap = len(question_words & answer_words)
        total = len(question_words | answer_words)
        
        return overlap / total if total > 0 else 0.0
    
    def _evaluate_answer_quality(self, question: str, answer: str) -> Tuple[float, float, float]:
        """답변 품질 평가 (완성도, 정확성, 도움도)"""
        if not answer or answer.strip().lower() in ["nan", "null", ""]:
            return 0.0, 0.0, 0.0
        
        # 완성도 (길이 기반)
        completeness = min(1.0, len(answer) / 200)
        
        # 정중함 보너스
        politeness_keywords = ["안녕하세요", "감사합니다", "죄송합니다", "고객님"]
        if any(keyword in answer for keyword in politeness_keywords):
            completeness += 0.1
        
        # 구체적 정보 보너스
        specific_keywords = ["주문번호", "운송장", "연락드리겠습니다", "처리", "확인"]
        if any(keyword in answer for keyword in specific_keywords):
            completeness += 0.2
        
        completeness = min(1.0, completeness)
        
        # 정확성 (주제 관련성과 동일)
        accuracy = self._calculate_topic_relevance(question, answer)
        
        # 도움도
        helpfulness = 0.5  # 기본 점수
        
        # 해결 지향적 표현
        solution_keywords = ["해결", "처리", "완료", "안내", "확인", "도움"]
        if any(keyword in answer for keyword in solution_keywords):
            helpfulness += 0.3
        
        # 추가 지원 제안
        support_keywords = ["고객센터", "문의", "연락", "게시판", "상담"]
        if any(keyword in answer for keyword in support_keywords):
            helpfulness += 0.2
        
        helpfulness = min(1.0, helpfulness)
        
        return completeness, accuracy, helpfulness
    
    def _determine_grade(self, score: float) -> str:
        """점수에 따른 등급 결정"""
        for grade, threshold in self.analysis_config.grade_thresholds.items():
            if score >= threshold:
                return grade
        return 'F'
    
    def _generate_feedback(
        self, 
        question: str, 
        answer: str, 
        scores: Dict[str, float]
    ) -> Tuple[List[str], List[str], List[str]]:
        """피드백 생성"""
        strengths = []
        weaknesses = []
        recommendations = []
        
        # 강점 분석
        if scores['semantic_similarity'] > 0.7:
            strengths.append("질문과 답변의 의미적 연관성이 높음")
        
        if scores['answer_completeness'] > 0.8:
            strengths.append("답변이 충분히 상세함")
        
        if "안녕하세요" in answer and "감사합니다" in answer:
            strengths.append("정중하고 친절한 답변 톤")
        
        if any(word in answer for word in ["주문번호", "운송장", "처리"]):
            strengths.append("구체적인 정보 제공")
        
        # 약점 분석
        if scores['semantic_similarity'] < 0.5:
            weaknesses.append("질문과 답변의 연관성이 낮음")
        
        if scores['answer_completeness'] < 0.5:
            weaknesses.append("답변이 너무 간단함")
        
        if scores['topic_relevance'] < 0.6:
            weaknesses.append("질문의 핵심 주제를 충분히 다루지 못함")
        
        if not answer or answer.strip().lower() in ["nan", "null", ""]:
            weaknesses.append("답변이 없음")
        
        # 개선사항 제안
        if scores['semantic_similarity'] < 0.6:
            recommendations.append("질문의 핵심 키워드를 답변에 더 많이 포함")
        
        if scores['answer_completeness'] < 0.7:
            recommendations.append("더 상세하고 구체적인 답변 제공")
        
        if scores['answer_helpfulness'] < 0.7:
            recommendations.append("고객이 다음에 취할 수 있는 구체적 행동 안내")
        
        if not any(word in answer for word in ["고객센터", "문의"]):
            recommendations.append("추가 문의 채널 안내 추가")
        
        return strengths, weaknesses, recommendations
    
    async def analyze_qa_pair(self, request: QAAnalysisRequest) -> QAAnalysisResult:
        """QA 쌍 분석 (메인 메서드)"""
        start_time = time.time()
        
        # 모델 초기화
        self._init_models()
        
        # 데이터 준비
        question_text = request.question_text
        answer_text = request.answer_text
        inquiry_id = request.inquiry_id
        
        # DB에서 데이터 조회 (ID가 제공된 경우)
        if inquiry_id and self.db_service and not question_text:
            try:
                inquiries = await self.db_service.get_all_inquiries(
                    table='personal',
                    limit=1000
                )
                
                target_inquiry = None
                for inquiry in inquiries:
                    if inquiry.get('id') == inquiry_id:
                        target_inquiry = inquiry
                        break
                
                if target_inquiry:
                    subject = target_inquiry.get('subject', '') or ''
                    content = target_inquiry.get('content', '') or ''
                    question_text = f"{subject} {content}".strip()
                    answer_text = target_inquiry.get('answer', '') or ''
                else:
                    raise ValueError(f"ID {inquiry_id}에 해당하는 문의를 찾을 수 없습니다")
                    
            except Exception as e:
                raise ValueError(f"DB 조회 실패: {e}")
        
        if not question_text:
            raise ValueError("질문 텍스트가 제공되지 않았습니다")
        
        # 분석 실행
        scores = {}
        
        if request.analysis_type in ["full", "similarity_only"]:
            scores['semantic_similarity'] = self._calculate_semantic_similarity(question_text, answer_text)
            scores['topic_relevance'] = self._calculate_topic_relevance(question_text, answer_text)
            scores['keyword_overlap'] = self._calculate_keyword_overlap(question_text, answer_text)
        else:
            scores['semantic_similarity'] = 0.0
            scores['topic_relevance'] = 0.0
            scores['keyword_overlap'] = 0.0
        
        if request.analysis_type in ["full", "quality_only"]:
            completeness, accuracy, helpfulness = self._evaluate_answer_quality(question_text, answer_text)
            scores['answer_completeness'] = completeness
            scores['answer_accuracy'] = accuracy
            scores['answer_helpfulness'] = helpfulness
        else:
            scores['answer_completeness'] = 0.0
            scores['answer_accuracy'] = 0.0
            scores['answer_helpfulness'] = 0.0
        
        # 종합 점수 계산
        weights = self.analysis_config.score_weights
        overall_score = sum(scores[key] * weights.get(key, 0) for key in scores.keys())
        
        # 통과 여부 및 등급
        pass_threshold = overall_score >= self.analysis_config.pass_threshold
        grade = self._determine_grade(overall_score)
        
        # 피드백 생성
        strengths, weaknesses, recommendations = [], [], []
        if request.include_feedback:
            strengths, weaknesses, recommendations = self._generate_feedback(
                question_text, answer_text, scores
            )
        
        # 처리 시간 계산
        processing_time = (time.time() - start_time) * 1000
        
        # 모델 정보
        model_info = {
            "embedding_model": self.embedding_model.config.model_name if self.embedding_model else "N/A",
            "llm_model": self.llm_model.config.model_name if self.llm_model else "N/A",
            "profile": self.config_manager.current_profile
        }
        
        return QAAnalysisResult(
            inquiry_id=inquiry_id,
            question_text=question_text,
            answer_text=answer_text or "",
            semantic_similarity=scores['semantic_similarity'],
            topic_relevance=scores['topic_relevance'],
            keyword_overlap=scores['keyword_overlap'],
            answer_completeness=scores['answer_completeness'],
            answer_accuracy=scores['answer_accuracy'],
            answer_helpfulness=scores['answer_helpfulness'],
            overall_score=overall_score,
            pass_threshold=pass_threshold,
            grade=grade,
            strengths=strengths,
            weaknesses=weaknesses,
            recommendations=recommendations,
            analysis_timestamp=time.strftime("%Y-%m-%d %H:%M:%S"),
            processing_time_ms=processing_time,
            model_info=model_info
        )
    
    async def analyze_multiple_pairs(
        self, 
        requests: List[QAAnalysisRequest]
    ) -> List[QAAnalysisResult]:
        """여러 QA 쌍 일괄 분석"""
        results = []
        
        for request in requests:
            try:
                result = await self.analyze_qa_pair(request)
                results.append(result)
            except Exception as e:
                print(f"⚠️ 분석 실패 (ID: {request.inquiry_id}): {e}")
                continue
        
        return results
    
    def export_results(
        self, 
        results: List[QAAnalysisResult], 
        filename: str = None
    ) -> Dict[str, Any]:
        """결과 내보내기"""
        if not results:
            return {}
        
        # 통계 계산
        total_count = len(results)
        pass_count = sum(1 for r in results if r.pass_threshold)
        avg_score = sum(r.overall_score for r in results) / total_count
        
        grade_distribution = {}
        for result in results:
            grade_distribution[result.grade] = grade_distribution.get(result.grade, 0) + 1
        
        output = {
            "metadata": {
                "total_pairs": total_count,
                "pass_count": pass_count,
                "pass_rate": (pass_count / total_count * 100) if total_count > 0 else 0,
                "average_score": round(avg_score, 3),
                "analysis_timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
                "model_profile": self.config_manager.current_profile
            },
            "statistics": {
                "grade_distribution": grade_distribution,
                "score_ranges": {
                    "excellent": sum(1 for r in results if r.overall_score >= 0.9),
                    "good": sum(1 for r in results if 0.8 <= r.overall_score < 0.9),
                    "fair": sum(1 for r in results if 0.7 <= r.overall_score < 0.8),
                    "poor": sum(1 for r in results if r.overall_score < 0.7)
                }
            },
            "qa_pairs": [result.to_dict() for result in results]
        }
        
        # 파일 저장
        if filename:
            try:
                output_settings = self.config_manager.get_output_settings()
                json_format = output_settings.get('json_format', {})
                
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(output, f, **json_format)
                print(f"📄 결과를 {filename}에 저장했습니다.")
            except Exception as e:
                print(f"⚠️ 파일 저장 실패: {e}")
        
        return output
    
    def get_agent_info(self) -> Dict[str, Any]:
        """에이전트 정보"""
        return {
            "agent_type": "QA Analysis Agent",
            "version": "1.0.0",
            "current_profile": self.config_manager.current_profile,
            "available_profiles": self.config_manager.get_profile_names(),
            "embedding_model": self.embedding_model.config.model_name if self.embedding_model else "Not loaded",
            "llm_model": self.llm_model.config.model_name if self.llm_model else "Not loaded",
            "db_connected": self.db_service is not None,
            "cache_size": {
                "similarity": len(self._similarity_cache),
                "classification": len(self._classification_cache)
            }
        }


# 편의 함수들
def create_qa_agent(
    profile: str = None,
    supabase_url: str = None,
    supabase_key: str = None
) -> QAAnalysisAgent:
    """QA 분석 에이전트 생성"""
    agent = QAAnalysisAgent(supabase_url=supabase_url, supabase_key=supabase_key)
    if profile:
        agent.switch_profile(profile)
    return agent


async def analyze_single_qa(
    question: str,
    answer: str,
    profile: str = None
) -> QAAnalysisResult:
    """단일 QA 쌍 분석 (간편 함수)"""
    agent = create_qa_agent(profile=profile)
    request = QAAnalysisRequest(
        question_text=question,
        answer_text=answer,
        analysis_type="full",
        include_feedback=True
    )
    return await agent.analyze_qa_pair(request)


if __name__ == "__main__":
    # 테스트
    async def test_agent():
        agent = create_qa_agent()
        print(agent.get_agent_info())
        
        # 테스트 분석
        request = QAAnalysisRequest(
            question_text="배송 언제 오나요?",
            answer_text="안녕하세요. 주문하신 상품은 내일 발송 예정입니다. 감사합니다.",
            analysis_type="full",
            include_feedback=True
        )
        
        result = await agent.analyze_qa_pair(request)
        print(result.to_json(indent=2))
    
    asyncio.run(test_agent())
