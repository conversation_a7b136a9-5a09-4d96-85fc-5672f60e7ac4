{
  "servers": {
    "filesystem": {
      "type": "stdio",
      "command": "npx",
      "args": [
        "-y",
        "@modelcontextprotocol/server-filesystem",
        "."
      ],
      "toolFilter": ["readFile", "writeFile"]
    },
    "Builton": {
      "type": "stdio",
      "command": "npx",
      "args": [
        "--yes",
        "@supabase/mcp-server-supabase@latest",
        "--access-token",
        "sbp_v0_9671e76b4107283f9cc38cd3bb765194d15c6ed2"
      ],
      "toolFilter": ["query", "upsert", "listTables"]  // 여기에 최소 도구만 선언!
    }
  }
}