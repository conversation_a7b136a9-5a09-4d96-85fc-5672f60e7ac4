{
    "workbench.iconTheme": "material-icon-theme",
    "editor.linkedEditing": true,
    "liveServer.settings.donotVerifyTags": true,
    "editor.fontSize": 16,
    "notebook.lineNumbers": "on",
    "terminal.integrated.inheritEnv": false,
    "notebook.displayOrder": [
        "image/png",
        "text/html",
        "text/plain"
    ],
    "notebook.find.scope": {

        "markupSource": true,
        "markupPreview": true,
        "codeSource": true,
        "codeOutput": true
    },
    "files.autoSave": "afterDelay",
    "workbench.editor.enablePreview": false,
    "git.openRepositoryInParentFolders": "always",
    "window.commandCenter": true,
    "diffEditor.hideUnchangedRegions.enabled": true,
    "vsicons.dontShowNewVersionMessage": true,
    "[python]": {
        
        "diffEditor.ignoreTrimWhitespace": false,
        "gitlens.codeLens.symbolScopes": [
            "!Module"
        ],
        "editor.formatOnType": true,
        "editor.wordBasedSuggestions": "off",
    },
    "editor.wordWrap": "on",
    "cmake.showOptionsMovedNotification": false,
    "cmake.configureOnOpen": false,
    "notebook.editorOptionsCustomizations": {

        

    },
    "code-runner.clearPreviousOutput": true,
    "code-runner.showExecutionMessage": false,
    "editor.wordWrapColumn": 40,
    "editor.wordWrap": "on",
    "editor.wordBreak": "normal",
    "terminal.integrated.scrollback": 100000000,
    "fontAwesomeAutocomplete.disableTriggerWordAutoClearPatterns": [

        "**/*.html"
    ],
    "notebook.output.scrolling": true,
    "notebook.output.wordWrap": true,
    "json.schemas": [
    

    ],

    "workbench.colorCustomizations": {
        "terminal.background":"#20201D",
        "terminal.foreground":"#A6A28C",
        "terminalCursor.background":"#A6A28C",
        "terminalCursor.foreground":"#A6A28C",
        "terminal.ansiBlack":"#20201D",
        "terminal.ansiBlue":"#6684E1",
        "terminal.ansiBrightBlack":"#7D7A68",
        "terminal.ansiBrightBlue":"#6684E1",
        "terminal.ansiBrightCyan":"#1FAD83",
        "terminal.ansiBrightGreen":"#60AC39",
        "terminal.ansiBrightMagenta":"#B854D4",
        "terminal.ansiBrightRed":"#D73737",
        "terminal.ansiBrightWhite":"#FEFBEC",
        "terminal.ansiBrightYellow":"#AE9513",
        "terminal.ansiCyan":"#1FAD83",
        "terminal.ansiGreen":"#60AC39",
        "terminal.ansiMagenta":"#B854D4",
        "terminal.ansiRed":"#D73737",
        "terminal.ansiWhite":"#A6A28C",
        "terminal.ansiYellow":"#AE9513"
    },
    "terminal.integrated.fontFamily": "monospace",
    "window.zoomLevel": 1,
    "python.createEnvironment.trigger": "off",
    "terminal.external.windowsExec": "C:\\Program Files\\Git\\git-bash",
    "terminal.integrated.defaultProfile.windows": "Git Bash",
    "C_Cpp.default.intelliSenseMode": "macos-clang-arm64",
    "notebook.output.textLineLimit": 1000,
    "chat.editor.wordWrap": "on",
    "jupyter.interactiveWindow.creationMode": "perFile",
    "github.copilot.enable": {
        "*": false
    },
    "python.defaultInterpreterPath": "/Users/<USER>/Desktop/Lyan/python/sportic_sns/.venv",
    "chat.mcp.discovery.enabled": true,
    "mcp.servers.glob": false,
    "mcp.server.autoDetect": false,
    "python.pythonPath": "/Users/<USER>/Desktop/Lyan/python/POC/.venv/bin/python"
    
}