# 프로젝트 구조

```
src/
├── api/                  # API 서비스
│   ├── __init__.py
│   └── classifier_api.py  # FastAPI 기반 분류 API
├── models/               # 데이터 모델
│   ├── __init__.py
│   ├── classification.py  # 분류 결과 모델
│   └── inquiry.py         # 문의 데이터 모델
├── services/             # 비즈니스 로직
│   ├── __init__.py
│   ├── async_inquiry_service.py  # 비동기 문의 서비스
│   ├── inquiry_classifier_service.py  # 분류 서비스
│   └── bulk_processing_service.py  # 대량 처리 서비스
├── utils/                # 유틸리티
│   ├── __init__.py
│   ├── async_utils.py     # 비동기 유틸리티
│   └── logging_utils.py   # 로깅 유틸리티
└── main.py               # 애플리케이션 진입점
```
config/
├── __init__.py
└── config.py             # 환경 설정

```
models/                   # 학습된 모델 저장
├── ko_ecommerce_classifier.bin  # 한국어 이커머스 분류 모델
└── embeddings/           # 임베딩 모델

```
tests/                    # 테스트
├── __init__.py
├── test_classifier.py
└── test_api.py

```
docs/                     # 문서
├── architecture.md       # 아키텍처 문서
└── implementation_plan.md  # 구현 계획

```
scripts/                  # 스크립트
├── setup.sh              # 환경 설정 스크립트
└── train_model.py        # 모델 학습 스크립트