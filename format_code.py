"""
코드 포맷팅 스크립트 - Trailing whitespace 제거 및 f-string 수정
"""
import os
import re


def remove_trailing_whitespace(content: str) -> str:
    """Trailing whitespace 제거"""
    lines = content.split('\n')
    cleaned_lines = [line.rstrip() for line in lines]
    return '\n'.join(cleaned_lines)


def fix_f_string_placeholders(content: str) -> str:
    """불필요한 f-string 수정"""
    # f"문자열" 패턴에서 {} 없는 경우 일반 문자열로 변경
    pattern = r'f"([^"{}]*)"'
    
    def replace_func(match):
        string_content = match.group(1)
        if '{' not in string_content and '}' not in string_content:
            return f'"{string_content}"'
        return match.group(0)
    
    return re.sub(pattern, replace_func, content)


def fix_bare_except(content: str) -> str:
    """bare except 수정"""
    return content.replace('except:', 'except Exception:')


def format_file(file_path: str) -> bool:
    """파일 포맷팅"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 포맷팅 적용
        content = remove_trailing_whitespace(content)
        content = fix_f_string_placeholders(content)
        content = fix_bare_except(content)
        
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"✅ 포맷팅 완료: {file_path}")
        return True
    except Exception as e:
        print(f"❌ 포맷팅 실패: {file_path} - {e}")
        return False


def main():
    """메인 함수"""
    files_to_format = [
        'main_simple.py',
        'app/services/unified_classification.py',
        'app/services/hybrid_pipeline.py',
        'app/services/simple_factory.py',
        'app/utils/cost_calculator.py'
    ]
    
    success_count = 0
    for file_path in files_to_format:
        if os.path.exists(file_path):
            if format_file(file_path):
                success_count += 1
        else:
            print(f"⚠️ 파일을 찾을 수 없습니다: {file_path}")
    
    print(f"\n📊 포맷팅 완료: {success_count}/{len(files_to_format)} 파일")


if __name__ == "__main__":
    main()
