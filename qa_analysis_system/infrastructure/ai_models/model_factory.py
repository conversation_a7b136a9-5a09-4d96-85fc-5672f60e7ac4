"""
AI 모델 팩토리 - SOLID 원칙 적용
"""
from typing import Dict, Type, List
from qa_analysis_system.core.interfaces.ai_model_interface import (
    ModelConfig, ModelType, ModelProvider,
    EmbeddingModelInterface, LLMInterface, ClassificationModelInterface
)
from qa_analysis_system.shared.exceptions.model_exceptions import (
    ModelNotFoundError, UnsupportedModelError
)


class AIModelFactory:
    """AI 모델 팩토리 (Factory Pattern + Registry Pattern)"""
    
    def __init__(self):
        """팩토리 초기화"""
        self._embedding_models: Dict[ModelProvider, Type] = {}
        self._llm_models: Dict[ModelProvider, Type] = {}
        self._classification_models: Dict[ModelProvider, Type] = {}
        
        # 기본 모델들 등록
        self._register_default_models()
    
    def _register_default_models(self) -> None:
        """기본 모델들 등록"""
        try:
            # 임베딩 모델들
            from qa_analysis_system.infrastructure.ai_models.embedding.kr_sbert import KRSBERTEmbeddingModel
            from qa_analysis_system.infrastructure.ai_models.embedding.kobert import KoBERTEmbeddingModel
            from qa_analysis_system.infrastructure.ai_models.embedding.klue_roberta import KLUERoBERTaEmbeddingModel
            
            self._embedding_models[ModelProvider.HUGGINGFACE] = self._create_huggingface_embedding_dispatcher()
            
            # LLM 모델들
            from qa_analysis_system.infrastructure.ai_models.llm.openai_gpt import OpenAIGPTModel
            self._llm_models[ModelProvider.OPENAI] = OpenAIGPTModel
            
            # 분류 모델들
            from qa_analysis_system.infrastructure.ai_models.embedding.kcelectra import KcELECTRAClassificationModel
            from qa_analysis_system.infrastructure.ai_models.embedding.klue_roberta import KLUERoBERTaClassificationModel
            
            self._classification_models[ModelProvider.HUGGINGFACE] = self._create_huggingface_classification_dispatcher()
            
            print("✅ 기본 모델들 등록 완료")
            
        except ImportError as e:
            print(f"⚠️ 일부 모델 등록 실패: {e}")
    
    def _create_huggingface_embedding_dispatcher(self) -> Type:
        """HuggingFace 임베딩 모델 디스패처 생성"""
        class HuggingFaceEmbeddingDispatcher:
            def __new__(cls, config: ModelConfig):
                model_name = config.name.lower()
                
                if "kr-sbert" in model_name or "snunlp" in model_name:
                    from qa_analysis_system.infrastructure.ai_models.embedding.kr_sbert import KRSBERTEmbeddingModel
                    return KRSBERTEmbeddingModel(config)
                elif "kobert" in model_name or "skt/kobert" in model_name:
                    from qa_analysis_system.infrastructure.ai_models.embedding.kobert import KoBERTEmbeddingModel
                    return KoBERTEmbeddingModel(config)
                elif "klue" in model_name and "roberta" in model_name:
                    from qa_analysis_system.infrastructure.ai_models.embedding.klue_roberta import KLUERoBERTaEmbeddingModel
                    return KLUERoBERTaEmbeddingModel(config)
                else:
                    # 기본값으로 KR-SBERT 사용
                    from qa_analysis_system.infrastructure.ai_models.embedding.kr_sbert import KRSBERTEmbeddingModel
                    return KRSBERTEmbeddingModel(config)
        
        return HuggingFaceEmbeddingDispatcher
    
    def _create_huggingface_classification_dispatcher(self) -> Type:
        """HuggingFace 분류 모델 디스패처 생성"""
        class HuggingFaceClassificationDispatcher:
            def __new__(cls, config: ModelConfig):
                model_name = config.name.lower()
                
                if "kcelectra" in model_name or "beomi" in model_name:
                    from qa_analysis_system.infrastructure.ai_models.embedding.kcelectra import KcELECTRAClassificationModel
                    return KcELECTRAClassificationModel(config)
                elif "klue" in model_name and "roberta" in model_name:
                    from qa_analysis_system.infrastructure.ai_models.embedding.klue_roberta import KLUERoBERTaClassificationModel
                    return KLUERoBERTaClassificationModel(config)
                else:
                    # 기본값으로 KcELECTRA 사용
                    from qa_analysis_system.infrastructure.ai_models.embedding.kcelectra import KcELECTRAClassificationModel
                    return KcELECTRAClassificationModel(config)
        
        return HuggingFaceClassificationDispatcher
    
    def create_embedding_model(self, config: ModelConfig) -> EmbeddingModelInterface:
        """임베딩 모델 생성"""
        if config.provider not in self._embedding_models:
            raise UnsupportedModelError(
                config.name,
                list(self._embedding_models.keys())
            )
        
        try:
            model_class = self._embedding_models[config.provider]
            return model_class(config)
        except Exception as e:
            raise ModelNotFoundError(config.name, config.provider.value)
    
    def create_llm_model(self, config: ModelConfig) -> LLMInterface:
        """LLM 모델 생성"""
        if config.provider not in self._llm_models:
            raise UnsupportedModelError(
                config.name,
                list(self._llm_models.keys())
            )
        
        try:
            model_class = self._llm_models[config.provider]
            return model_class(config)
        except Exception as e:
            raise ModelNotFoundError(config.name, config.provider.value)
    
    def create_classification_model(self, config: ModelConfig) -> ClassificationModelInterface:
        """분류 모델 생성"""
        if config.provider not in self._classification_models:
            raise UnsupportedModelError(
                config.name,
                list(self._classification_models.keys())
            )
        
        try:
            model_class = self._classification_models[config.provider]
            return model_class(config)
        except Exception as e:
            raise ModelNotFoundError(config.name, config.provider.value)
    
    def register_embedding_model(self, provider: ModelProvider, model_class: Type) -> None:
        """임베딩 모델 등록"""
        self._embedding_models[provider] = model_class
        print(f"✅ 임베딩 모델 등록: {provider.value}")
    
    def register_llm_model(self, provider: ModelProvider, model_class: Type) -> None:
        """LLM 모델 등록"""
        self._llm_models[provider] = model_class
        print(f"✅ LLM 모델 등록: {provider.value}")
    
    def register_classification_model(self, provider: ModelProvider, model_class: Type) -> None:
        """분류 모델 등록"""
        self._classification_models[provider] = model_class
        print(f"✅ 분류 모델 등록: {provider.value}")
    
    def list_available_models(self) -> Dict[str, List[str]]:
        """사용 가능한 모델 목록"""
        return {
            "embedding": [provider.value for provider in self._embedding_models.keys()],
            "llm": [provider.value for provider in self._llm_models.keys()],
            "classification": [provider.value for provider in self._classification_models.keys()]
        }
    
    def get_supported_models_by_type(self, model_type: ModelType) -> List[str]:
        """타입별 지원 모델 목록"""
        if model_type == ModelType.EMBEDDING:
            return [provider.value for provider in self._embedding_models.keys()]
        elif model_type == ModelType.LLM:
            return [provider.value for provider in self._llm_models.keys()]
        elif model_type == ModelType.CLASSIFICATION:
            return [provider.value for provider in self._classification_models.keys()]
        else:
            return []
    
    def validate_model_config(self, config: ModelConfig) -> bool:
        """모델 설정 검증"""
        try:
            # 제공자 지원 여부 확인
            if config.model_type == ModelType.EMBEDDING:
                supported = config.provider in self._embedding_models
            elif config.model_type == ModelType.LLM:
                supported = config.provider in self._llm_models
            elif config.model_type == ModelType.CLASSIFICATION:
                supported = config.provider in self._classification_models
            else:
                return False
            
            if not supported:
                print(f"⚠️ 지원하지 않는 제공자: {config.provider.value}")
                return False
            
            # API 키 확인 (필요한 경우)
            if config.provider == ModelProvider.OPENAI and not config.api_key:
                print("⚠️ OpenAI API 키가 필요합니다")
                return False
            
            return True
            
        except Exception as e:
            print(f"⚠️ 모델 설정 검증 실패: {e}")
            return False
    
    def get_model_info(self) -> Dict[str, Dict[str, List[str]]]:
        """상세 모델 정보"""
        return {
            "embedding_models": {
                "huggingface": [
                    "snunlp/KR-SBERT-V40K-klueNLI-augSTS",
                    "skt/kobert-base-v1", 
                    "klue/roberta-base"
                ]
            },
            "llm_models": {
                "openai": [
                    "gpt-4o-mini",
                    "gpt-4o",
                    "gpt-4-turbo", 
                    "gpt-3.5-turbo"
                ]
            },
            "classification_models": {
                "huggingface": [
                    "beomi/KcELECTRA-base",
                    "klue/roberta-base"
                ]
            }
        }


# 전역 팩토리 인스턴스
_model_factory = None


def get_model_factory() -> AIModelFactory:
    """전역 모델 팩토리 인스턴스 반환 (Singleton 패턴)"""
    global _model_factory
    if _model_factory is None:
        _model_factory = AIModelFactory()
    return _model_factory


def create_model_from_config(config: ModelConfig):
    """설정에서 모델 생성 (편의 함수)"""
    factory = get_model_factory()
    
    if config.model_type == ModelType.EMBEDDING:
        return factory.create_embedding_model(config)
    elif config.model_type == ModelType.LLM:
        return factory.create_llm_model(config)
    elif config.model_type == ModelType.CLASSIFICATION:
        return factory.create_classification_model(config)
    else:
        raise UnsupportedModelError(config.name, ["embedding", "llm", "classification"])


# 편의 함수들
def create_kr_sbert_model(device: str = "cpu"):
    """KR-SBERT 모델 생성"""
    config = ModelConfig(
        name="snunlp/KR-SBERT-V40K-klueNLI-augSTS",
        provider=ModelProvider.HUGGINGFACE,
        model_type=ModelType.EMBEDDING,
        device=device
    )
    return create_model_from_config(config)


def create_kcelectra_model(device: str = "cpu"):
    """KcELECTRA 모델 생성"""
    config = ModelConfig(
        name="beomi/KcELECTRA-base",
        provider=ModelProvider.HUGGINGFACE,
        model_type=ModelType.CLASSIFICATION,
        device=device
    )
    return create_model_from_config(config)


def create_gpt4o_mini_model(api_key: str):
    """GPT-4o-mini 모델 생성"""
    config = ModelConfig(
        name="gpt-4o-mini",
        provider=ModelProvider.OPENAI,
        model_type=ModelType.LLM,
        api_key=api_key,
        max_tokens=500,
        temperature=0.1
    )
    return create_model_from_config(config)
