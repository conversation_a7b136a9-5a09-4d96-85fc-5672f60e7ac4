"""
KLUE-RoBERTa 모델 구현
한국어 자연어 이해에 특화된 RoBERTa 모델
"""
from typing import List, Tuple, Any
import torch
import numpy as np
from transformers import <PERSON>Tokenizer, AutoModel, AutoModelForSequenceClassification
from sklearn.metrics.pairwise import cosine_similarity

from qa_analysis_system.core.interfaces.ai_model_interface import (
    BaseEmbeddingModel, BaseClassificationModel, ModelInfo, ModelConfig
)
from qa_analysis_system.shared.exceptions.model_exceptions import ModelLoadError


class KLUERoBERTaEmbeddingModel(BaseEmbeddingModel):
    """KLUE-RoBERTa 임베딩 모델"""
    
    def __init__(self, config: ModelConfig):
        super().__init__(config)
        self.model_name = config.name or "klue/roberta-base"
        self.tokenizer = None
        self.max_length = 512
    
    def _load_model_impl(self) -> Any:
        """KLUE-RoBERTa 모델 로드"""
        try:
            print(f"🤖 KLUE-RoBERTa 모델 로딩 중: {self.model_name}")
            
            # 토크나이저 로드
            self.tokenizer = AutoTokenizer.from_pretrained(self.model_name)
            
            # 모델 로드
            model = AutoModel.from_pretrained(self.model_name)
            
            # 디바이스 설정
            device = torch.device(self.config.device if torch.cuda.is_available() and self.config.device == "cuda" else "cpu")
            model = model.to(device)
            model.eval()
            
            print(f"✅ KLUE-RoBERTa 모델 로드 완료: {self.model_name}")
            return model
            
        except ImportError:
            raise ModelLoadError(
                self.model_name,
                "transformers library not installed. Run: pip install transformers torch"
            )
        except Exception as e:
            raise ModelLoadError(self.model_name, str(e))
    
    def encode_texts(self, texts: List[str]) -> List[List[float]]:
        """텍스트 리스트를 임베딩으로 변환"""
        if not self._is_loaded:
            self.load_model()
        
        if not texts:
            return []
        
        try:
            # 빈 텍스트 처리
            processed_texts = [text if text.strip() else " " for text in texts]
            
            embeddings = []
            
            # 배치 처리 (메모리 효율성)
            batch_size = 16
            for i in range(0, len(processed_texts), batch_size):
                batch_texts = processed_texts[i:i + batch_size]
                batch_embeddings = self._encode_batch(batch_texts)
                embeddings.extend(batch_embeddings)
            
            return embeddings
            
        except Exception as e:
            raise ModelLoadError(self.model_name, f"Encoding failed: {str(e)}")
    
    def _encode_batch(self, texts: List[str]) -> List[List[float]]:
        """배치 인코딩"""
        # 토크나이징
        inputs = self.tokenizer(
            texts,
            return_tensors="pt",
            truncation=True,
            padding=True,
            max_length=self.max_length
        )
        
        # 디바이스로 이동
        device = next(self._model.parameters()).device
        inputs = {k: v.to(device) for k, v in inputs.items()}
        
        # 임베딩 생성
        with torch.no_grad():
            outputs = self._model(**inputs)
            
            # Mean pooling 사용 (RoBERTa는 [CLS] 토큰이 없음)
            embeddings = self._mean_pooling(outputs.last_hidden_state, inputs['attention_mask'])
            
            # 정규화
            if self.config.additional_params.get("normalize_embeddings", True):
                embeddings = torch.nn.functional.normalize(embeddings, p=2, dim=1)
            
            return embeddings.cpu().numpy().tolist()
    
    def _mean_pooling(self, token_embeddings: torch.Tensor, attention_mask: torch.Tensor) -> torch.Tensor:
        """Mean pooling with attention mask"""
        input_mask_expanded = attention_mask.unsqueeze(-1).expand(token_embeddings.size()).float()
        sum_embeddings = torch.sum(token_embeddings * input_mask_expanded, 1)
        sum_mask = torch.clamp(input_mask_expanded.sum(1), min=1e-9)
        return sum_embeddings / sum_mask
    
    def calculate_similarity(self, text1: str, text2: str) -> float:
        """두 텍스트 간 코사인 유사도 계산"""
        if not text1.strip() or not text2.strip():
            return 0.0
        
        try:
            embeddings = self.encode_texts([text1, text2])
            similarity = cosine_similarity([embeddings[0]], [embeddings[1]])[0][0]
            return float(similarity)
            
        except Exception as e:
            print(f"⚠️ 유사도 계산 실패: {e}")
            return 0.0
    
    def get_embedding_dimension(self) -> int:
        """임베딩 차원 수 반환"""
        if not self._is_loaded:
            self.load_model()
        return self._model.config.hidden_size
    
    def semantic_search(
        self, 
        query: str, 
        corpus: List[str],
        top_k: int = 5,
        threshold: float = 0.3
    ) -> List[Tuple[str, float, int]]:
        """의미적 검색"""
        if not query.strip() or not corpus:
            return []
        
        try:
            # 쿼리와 코퍼스 인코딩
            query_embedding = self.encode_single(query)
            corpus_embeddings = self.encode_texts(corpus)
            
            # 유사도 계산
            similarities = []
            for i, corpus_emb in enumerate(corpus_embeddings):
                sim = cosine_similarity([query_embedding], [corpus_emb])[0][0]
                if sim >= threshold:
                    similarities.append((corpus[i], float(sim), i))
            
            # 유사도 순으로 정렬
            similarities.sort(key=lambda x: x[1], reverse=True)
            
            return similarities[:top_k]
            
        except Exception as e:
            print(f"⚠️ 의미적 검색 실패: {e}")
            return []
    
    def cluster_texts(
        self, 
        texts: List[str], 
        similarity_threshold: float = 0.7
    ) -> List[List[int]]:
        """텍스트 클러스터링 (유사도 기반)"""
        if len(texts) < 2:
            return [[i] for i in range(len(texts))]
        
        try:
            # 모든 텍스트 인코딩
            embeddings = self.encode_texts(texts)
            
            # 유사도 매트릭스 계산
            similarity_matrix = cosine_similarity(embeddings)
            
            # 간단한 클러스터링 (threshold 기반)
            clusters = []
            visited = set()
            
            for i in range(len(texts)):
                if i in visited:
                    continue
                
                cluster = [i]
                visited.add(i)
                
                for j in range(i + 1, len(texts)):
                    if j not in visited and similarity_matrix[i][j] >= similarity_threshold:
                        cluster.append(j)
                        visited.add(j)
                
                clusters.append(cluster)
            
            return clusters
            
        except Exception as e:
            print(f"⚠️ 텍스트 클러스터링 실패: {e}")
            return [[i] for i in range(len(texts))]
    
    def _get_model_info_impl(self) -> ModelInfo:
        """모델 정보 반환"""
        return ModelInfo(
            name=self.model_name,
            provider="huggingface",
            version="1.0",
            description="KLUE RoBERTa for Korean natural language understanding",
            supported_languages=["ko"],
            max_input_length=self.max_length,
            is_loaded=self._is_loaded
        )


class KLUERoBERTaClassificationModel(BaseClassificationModel):
    """KLUE-RoBERTa 분류 모델 (파인튜닝용)"""
    
    def __init__(self, config: ModelConfig):
        super().__init__(config)
        self.model_name = config.name or "klue/roberta-base"
        self.tokenizer = None
        self.num_labels = config.additional_params.get("num_labels", 6)
        
        # 기본 카테고리
        self.categories = [
            "배송문의", "환불문의", "상품문의", "교환반품", "결제문의", "기타"
        ]
    
    def _load_model_impl(self) -> Any:
        """분류용 KLUE-RoBERTa 모델 로드"""
        try:
            print(f"🤖 KLUE-RoBERTa 분류 모델 로딩 중: {self.model_name}")
            
            # 토크나이저 로드
            self.tokenizer = AutoTokenizer.from_pretrained(self.model_name)
            
            # 분류 모델 로드 (또는 기본 모델에 분류 헤드 추가)
            try:
                model = AutoModelForSequenceClassification.from_pretrained(
                    self.model_name, 
                    num_labels=self.num_labels
                )
            except:
                # 사전 훈련된 분류 모델이 없으면 기본 모델 사용
                model = AutoModel.from_pretrained(self.model_name)
            
            # 디바이스 설정
            device = torch.device(self.config.device if torch.cuda.is_available() and self.config.device == "cuda" else "cpu")
            model = model.to(device)
            model.eval()
            
            print(f"✅ KLUE-RoBERTa 분류 모델 로드 완료")
            return model
            
        except Exception as e:
            raise ModelLoadError(self.model_name, str(e))
    
    def predict(self, text: str) -> Tuple[str, float]:
        """텍스트 분류 (키워드 기반 fallback)"""
        if not text.strip():
            return "기타", 0.5
        
        # 키워드 기반 분류 (파인튜닝된 모델이 없는 경우)
        return self._keyword_based_classification(text)
    
    def _keyword_based_classification(self, text: str) -> Tuple[str, float]:
        """키워드 기반 분류"""
        text_lower = text.lower()
        
        category_keywords = {
            "배송문의": ["배송", "택배", "발송", "도착", "언제", "늦어"],
            "환불문의": ["환불", "취소", "돌려", "카드취소"],
            "상품문의": ["상품", "제품", "품질", "색상", "사이즈"],
            "교환반품": ["교환", "반품", "불량", "파손", "바꿔"],
            "결제문의": ["결제", "카드", "계좌", "할인", "쿠폰"],
        }
        
        best_category = "기타"
        best_score = 0.0
        
        for category, keywords in category_keywords.items():
            matches = sum(1 for keyword in keywords if keyword in text_lower)
            if matches > 0:
                score = min(1.0, matches / len(keywords) * 2)
                if score > best_score:
                    best_score = score
                    best_category = category
        
        return best_category, best_score
    
    def get_categories(self) -> List[str]:
        """지원하는 카테고리 목록"""
        return self.categories
    
    def _get_model_info_impl(self) -> ModelInfo:
        """모델 정보 반환"""
        return ModelInfo(
            name=self.model_name,
            provider="huggingface",
            version="1.0",
            description="KLUE RoBERTa for Korean text classification",
            supported_languages=["ko"],
            max_input_length=512,
            is_loaded=self._is_loaded
        )


def create_klue_roberta_embedding_model(
    model_name: str = "klue/roberta-base",
    device: str = "cpu",
    normalize_embeddings: bool = True
) -> KLUERoBERTaEmbeddingModel:
    """KLUE-RoBERTa 임베딩 모델 생성"""
    from qa_analysis_system.core.interfaces.ai_model_interface import ModelConfig, ModelProvider, ModelType
    
    config = ModelConfig(
        name=model_name,
        provider=ModelProvider.HUGGINGFACE,
        model_type=ModelType.EMBEDDING,
        device=device,
        additional_params={
            "normalize_embeddings": normalize_embeddings
        }
    )
    
    return KLUERoBERTaEmbeddingModel(config)


def create_klue_roberta_classification_model(
    model_name: str = "klue/roberta-base",
    device: str = "cpu",
    num_labels: int = 6
) -> KLUERoBERTaClassificationModel:
    """KLUE-RoBERTa 분류 모델 생성"""
    from qa_analysis_system.core.interfaces.ai_model_interface import ModelConfig, ModelProvider, ModelType
    
    config = ModelConfig(
        name=model_name,
        provider=ModelProvider.HUGGINGFACE,
        model_type=ModelType.CLASSIFICATION,
        device=device,
        additional_params={
            "num_labels": num_labels
        }
    )
    
    return KLUERoBERTaClassificationModel(config)
