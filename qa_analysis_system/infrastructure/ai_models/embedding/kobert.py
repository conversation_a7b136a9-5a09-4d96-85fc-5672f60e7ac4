"""
KoBERT 모델 구현
한국어 BERT 기반 다목적 분류 모델
"""
from typing import List, Tuple, Any
import torch
import numpy as np
from transformers import AutoTokenizer, AutoModel
from sklearn.metrics.pairwise import cosine_similarity

from qa_analysis_system.core.interfaces.ai_model_interface import (
    BaseEmbeddingModel, ModelInfo, ModelConfig
)
from qa_analysis_system.shared.exceptions.model_exceptions import ModelLoadError


class KoBERTEmbeddingModel(BaseEmbeddingModel):
    """KoBERT 임베딩 모델"""
    
    def __init__(self, config: ModelConfig):
        super().__init__(config)
        self.model_name = config.name or "skt/kobert-base-v1"
        self.tokenizer = None
        self.max_length = 512
    
    def _load_model_impl(self) -> Any:
        """KoBERT 모델 로드"""
        try:
            print(f"🤖 KoBERT 모델 로딩 중: {self.model_name}")
            
            # 토크나이저 로드
            self.tokenizer = AutoTokenizer.from_pretrained(self.model_name)
            
            # 모델 로드
            model = AutoModel.from_pretrained(self.model_name)
            
            # 디바이스 설정
            device = torch.device(self.config.device if torch.cuda.is_available() and self.config.device == "cuda" else "cpu")
            model = model.to(device)
            model.eval()
            
            print(f"✅ KoBERT 모델 로드 완료: {self.model_name}")
            return model
            
        except ImportError:
            raise ModelLoadError(
                self.model_name,
                "transformers library not installed. Run: pip install transformers torch"
            )
        except Exception as e:
            raise ModelLoadError(self.model_name, str(e))
    
    def encode_texts(self, texts: List[str]) -> List[List[float]]:
        """텍스트 리스트를 임베딩으로 변환"""
        if not self._is_loaded:
            self.load_model()
        
        if not texts:
            return []
        
        try:
            # 빈 텍스트 처리
            processed_texts = [text if text.strip() else " " for text in texts]
            
            embeddings = []
            
            # 배치 처리
            batch_size = 8  # 메모리 효율성을 위해 작은 배치 사용
            for i in range(0, len(processed_texts), batch_size):
                batch_texts = processed_texts[i:i + batch_size]
                batch_embeddings = self._encode_batch(batch_texts)
                embeddings.extend(batch_embeddings)
            
            return embeddings
            
        except Exception as e:
            raise ModelLoadError(self.model_name, f"Encoding failed: {str(e)}")
    
    def _encode_batch(self, texts: List[str]) -> List[List[float]]:
        """배치 인코딩"""
        # 토크나이징
        inputs = self.tokenizer(
            texts,
            return_tensors="pt",
            truncation=True,
            padding=True,
            max_length=self.max_length
        )
        
        # 디바이스로 이동
        device = next(self._model.parameters()).device
        inputs = {k: v.to(device) for k, v in inputs.items()}
        
        # 임베딩 생성
        with torch.no_grad():
            outputs = self._model(**inputs)
            
            # [CLS] 토큰의 임베딩 사용
            embeddings = outputs.last_hidden_state[:, 0, :]  # [batch_size, hidden_size]
            
            # 정규화 (선택적)
            if self.config.additional_params.get("normalize_embeddings", True):
                embeddings = torch.nn.functional.normalize(embeddings, p=2, dim=1)
            
            return embeddings.cpu().numpy().tolist()
    
    def calculate_similarity(self, text1: str, text2: str) -> float:
        """두 텍스트 간 코사인 유사도 계산"""
        if not text1.strip() or not text2.strip():
            return 0.0
        
        try:
            embeddings = self.encode_texts([text1, text2])
            similarity = cosine_similarity([embeddings[0]], [embeddings[1]])[0][0]
            return float(similarity)
            
        except Exception as e:
            print(f"⚠️ 유사도 계산 실패: {e}")
            return 0.0
    
    def get_embedding_dimension(self) -> int:
        """임베딩 차원 수 반환"""
        if not self._is_loaded:
            self.load_model()
        return self._model.config.hidden_size
    
    def classify_by_similarity(
        self, 
        text: str, 
        reference_texts: dict,
        threshold: float = 0.5
    ) -> Tuple[str, float]:
        """유사도 기반 분류"""
        if not text.strip():
            return "기타", 0.0
        
        text_embedding = self.encode_single(text)
        best_category = "기타"
        best_score = 0.0
        
        for category, ref_texts in reference_texts.items():
            if not ref_texts:
                continue
            
            # 참조 텍스트들과의 유사도 계산
            ref_embeddings = self.encode_texts(ref_texts)
            similarities = []
            
            for ref_emb in ref_embeddings:
                sim = cosine_similarity([text_embedding], [ref_emb])[0][0]
                similarities.append(sim)
            
            # 평균 유사도 사용
            avg_similarity = np.mean(similarities) if similarities else 0.0
            
            if avg_similarity > best_score and avg_similarity >= threshold:
                best_score = avg_similarity
                best_category = category
        
        return best_category, float(best_score)
    
    def find_most_similar(
        self, 
        query_text: str, 
        candidate_texts: List[str],
        top_k: int = 5
    ) -> List[Tuple[str, float]]:
        """가장 유사한 텍스트 찾기"""
        if not query_text.strip() or not candidate_texts:
            return []
        
        try:
            # 모든 텍스트 인코딩
            all_texts = [query_text] + candidate_texts
            embeddings = self.encode_texts(all_texts)
            
            query_embedding = embeddings[0]
            candidate_embeddings = embeddings[1:]
            
            # 유사도 계산
            similarities = []
            for i, candidate_emb in enumerate(candidate_embeddings):
                sim = cosine_similarity([query_embedding], [candidate_emb])[0][0]
                similarities.append((candidate_texts[i], float(sim)))
            
            # 유사도 순으로 정렬
            similarities.sort(key=lambda x: x[1], reverse=True)
            
            return similarities[:top_k]
            
        except Exception as e:
            print(f"⚠️ 유사 텍스트 검색 실패: {e}")
            return []
    
    def _get_model_info_impl(self) -> ModelInfo:
        """모델 정보 반환"""
        return ModelInfo(
            name=self.model_name,
            provider="huggingface",
            version="1.0",
            description="Korean BERT for general-purpose text embedding and classification",
            supported_languages=["ko"],
            max_input_length=self.max_length,
            is_loaded=self._is_loaded
        )


# 한국어 이커머스 참조 텍스트
KOBERT_REFERENCE_TEXTS = {
    "배송문의": [
        "배송 언제 되나요?",
        "택배 조회 부탁드립니다",
        "언제 받을 수 있나요?",
        "발송 상태 확인해주세요",
        "배송이 늦어지고 있어요"
    ],
    "환불문의": [
        "환불 신청합니다",
        "돈 돌려받고 싶어요",
        "취소하고 환불해주세요",
        "카드 취소 언제 되나요?",
        "환불 절차 알려주세요"
    ],
    "상품문의": [
        "이 상품 어떤가요?",
        "품질이 좋나요?",
        "사이즈 정보 알려주세요",
        "색상이 실제와 다른가요?",
        "재질이 무엇인가요?"
    ],
    "교환반품": [
        "교환 가능한가요?",
        "반품 신청합니다",
        "사이즈 교환하고 싶어요",
        "불량품이라 교환해주세요",
        "다른 색상으로 바꿔주세요"
    ],
    "결제문의": [
        "결제가 안돼요",
        "카드 오류 발생했어요",
        "할인 적용 안됐어요",
        "쿠폰 사용법 알려주세요",
        "적립금은 언제 들어오나요?"
    ]
}


def create_kobert_model(
    model_name: str = "skt/kobert-base-v1",
    device: str = "cpu",
    normalize_embeddings: bool = True
) -> KoBERTEmbeddingModel:
    """KoBERT 모델 생성 헬퍼 함수"""
    from qa_analysis_system.core.interfaces.ai_model_interface import ModelConfig, ModelProvider, ModelType
    
    config = ModelConfig(
        name=model_name,
        provider=ModelProvider.HUGGINGFACE,
        model_type=ModelType.EMBEDDING,
        device=device,
        additional_params={
            "normalize_embeddings": normalize_embeddings
        }
    )
    
    return KoBERTEmbeddingModel(config)
