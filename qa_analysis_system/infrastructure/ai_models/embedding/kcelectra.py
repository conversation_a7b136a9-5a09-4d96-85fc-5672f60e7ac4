"""
KcELECTRA 모델 구현
한국어 감성 분석에 특화된 모델
"""
from typing import List, Tuple, Any
import torch
import numpy as np
from transformers import AutoTokenizer, AutoModelForSequenceClassification

from qa_analysis_system.core.interfaces.ai_model_interface import (
    BaseClassificationModel, ModelInfo, ModelConfig
)
from qa_analysis_system.shared.exceptions.model_exceptions import ModelLoadError


class KcELECTRAClassificationModel(BaseClassificationModel):
    """KcELECTRA 감성 분석 모델"""
    
    def __init__(self, config: ModelConfig):
        super().__init__(config)
        self.model_name = config.name or "beomi/KcELECTRA-base"
        self.tokenizer = None
        
        # 감성 라벨 매핑
        self.sentiment_labels = {
            0: "부정",
            1: "중립", 
            2: "긍정"
        }
        
        # 이커머스 특화 카테고리
        self.ecommerce_categories = [
            "배송문의", "환불문의", "상품문의", "교환반품", "결제문의", "기타"
        ]
    
    def _load_model_impl(self) -> Any:
        """KcELECTRA 모델 로드"""
        try:
            print(f"🤖 KcELECTRA 모델 로딩 중: {self.model_name}")
            
            # 토크나이저 로드
            self.tokenizer = AutoTokenizer.from_pretrained(self.model_name)
            
            # 모델 로드
            model = AutoModelForSequenceClassification.from_pretrained(self.model_name)
            
            # 디바이스 설정
            device = torch.device(self.config.device if torch.cuda.is_available() and self.config.device == "cuda" else "cpu")
            model = model.to(device)
            model.eval()
            
            print(f"✅ KcELECTRA 모델 로드 완료: {self.model_name}")
            return model
            
        except ImportError:
            raise ModelLoadError(
                self.model_name,
                "transformers library not installed. Run: pip install transformers torch"
            )
        except Exception as e:
            raise ModelLoadError(self.model_name, str(e))
    
    def predict(self, text: str) -> Tuple[str, float]:
        """감성 분석 예측"""
        if not self._is_loaded:
            self.load_model()
        
        if not text.strip():
            return "중립", 0.5
        
        try:
            # 토크나이징
            inputs = self.tokenizer(
                text,
                return_tensors="pt",
                truncation=True,
                padding=True,
                max_length=512
            )
            
            # 디바이스로 이동
            device = next(self._model.parameters()).device
            inputs = {k: v.to(device) for k, v in inputs.items()}
            
            # 예측
            with torch.no_grad():
                outputs = self._model(**inputs)
                predictions = torch.nn.functional.softmax(outputs.logits, dim=-1)
                predicted_class = torch.argmax(predictions, dim=-1).item()
                confidence = predictions[0][predicted_class].item()
            
            sentiment = self.sentiment_labels.get(predicted_class, "중립")
            return sentiment, float(confidence)
            
        except Exception as e:
            print(f"⚠️ 감성 분석 실패: {e}")
            return "중립", 0.5
    
    def predict_batch(self, texts: List[str]) -> List[Tuple[str, float]]:
        """배치 감성 분석"""
        if not self._is_loaded:
            self.load_model()
        
        if not texts:
            return []
        
        try:
            # 빈 텍스트 처리
            processed_texts = [text if text.strip() else " " for text in texts]
            
            # 배치 토크나이징
            inputs = self.tokenizer(
                processed_texts,
                return_tensors="pt",
                truncation=True,
                padding=True,
                max_length=512
            )
            
            # 디바이스로 이동
            device = next(self._model.parameters()).device
            inputs = {k: v.to(device) for k, v in inputs.items()}
            
            # 배치 예측
            with torch.no_grad():
                outputs = self._model(**inputs)
                predictions = torch.nn.functional.softmax(outputs.logits, dim=-1)
                predicted_classes = torch.argmax(predictions, dim=-1)
                confidences = torch.max(predictions, dim=-1)[0]
            
            results = []
            for i in range(len(texts)):
                predicted_class = predicted_classes[i].item()
                confidence = confidences[i].item()
                sentiment = self.sentiment_labels.get(predicted_class, "중립")
                results.append((sentiment, float(confidence)))
            
            return results
            
        except Exception as e:
            print(f"⚠️ 배치 감성 분석 실패: {e}")
            return [("중립", 0.5)] * len(texts)
    
    def classify_ecommerce_intent(self, text: str) -> Tuple[str, float]:
        """이커머스 의도 분류 (키워드 기반)"""
        if not text.strip():
            return "기타", 0.5
        
        text_lower = text.lower()
        
        # 키워드 기반 분류
        category_keywords = {
            "배송문의": ["배송", "택배", "발송", "도착", "언제", "늦어", "빨리"],
            "환불문의": ["환불", "취소", "돌려", "카드취소", "계좌환불"],
            "상품문의": ["상품", "제품", "품질", "색상", "사이즈", "재질", "성분"],
            "교환반품": ["교환", "반품", "불량", "파손", "잘못", "다른", "바꿔"],
            "결제문의": ["결제", "카드", "계좌", "입금", "할인", "쿠폰", "적립"],
        }
        
        best_category = "기타"
        best_score = 0.0
        
        for category, keywords in category_keywords.items():
            matches = sum(1 for keyword in keywords if keyword in text_lower)
            if matches > 0:
                score = min(1.0, matches / len(keywords) * 2)  # 최대 1.0
                if score > best_score:
                    best_score = score
                    best_category = category
        
        return best_category, best_score
    
    def analyze_customer_emotion(self, text: str) -> dict:
        """고객 감정 종합 분석"""
        sentiment, sentiment_confidence = self.predict(text)
        category, category_confidence = self.classify_ecommerce_intent(text)
        
        # 긴급도 분석
        urgency_keywords = ["급해", "빨리", "긴급", "당장", "오늘", "지금"]
        urgency_score = sum(1 for keyword in urgency_keywords if keyword in text.lower())
        urgency_level = "높음" if urgency_score >= 2 else "보통" if urgency_score == 1 else "낮음"
        
        # 불만 강도 분석
        complaint_keywords = ["화나", "짜증", "불만", "실망", "최악", "엉망", "어이없"]
        complaint_score = sum(1 for keyword in complaint_keywords if keyword in text.lower())
        complaint_level = "높음" if complaint_score >= 2 else "보통" if complaint_score == 1 else "낮음"
        
        return {
            "sentiment": sentiment,
            "sentiment_confidence": sentiment_confidence,
            "category": category,
            "category_confidence": category_confidence,
            "urgency_level": urgency_level,
            "complaint_level": complaint_level,
            "text_length": len(text),
            "word_count": len(text.split())
        }
    
    def get_categories(self) -> List[str]:
        """지원하는 카테고리 목록"""
        return self.ecommerce_categories
    
    def _get_model_info_impl(self) -> ModelInfo:
        """모델 정보 반환"""
        return ModelInfo(
            name=self.model_name,
            provider="huggingface",
            version="1.0",
            description="Korean ELECTRA for sentiment analysis and ecommerce classification",
            supported_languages=["ko"],
            max_input_length=512,
            is_loaded=self._is_loaded
        )


def create_kcelectra_model(
    model_name: str = "beomi/KcELECTRA-base",
    device: str = "cpu"
) -> KcELECTRAClassificationModel:
    """KcELECTRA 모델 생성 헬퍼 함수"""
    from qa_analysis_system.core.interfaces.ai_model_interface import ModelConfig, ModelProvider, ModelType
    
    config = ModelConfig(
        name=model_name,
        provider=ModelProvider.HUGGINGFACE,
        model_type=ModelType.CLASSIFICATION,
        device=device
    )
    
    return KcELECTRAClassificationModel(config)
