"""
KR-SBERT 임베딩 모델 구현
한국어 문장 임베딩에 특화된 모델
"""
from typing import List, Any
import numpy as np
from sklearn.metrics.pairwise import cosine_similarity

from qa_analysis_system.core.interfaces.ai_model_interface import (
    BaseEmbeddingModel, ModelInfo, ModelConfig
)
from qa_analysis_system.shared.exceptions.model_exceptions import ModelLoadError


class KRSBERTEmbeddingModel(BaseEmbeddingModel):
    """KR-SBERT 임베딩 모델"""
    
    def __init__(self, config: ModelConfig):
        super().__init__(config)
        self.model_name = config.name or "snunlp/KR-SBERT-V40K-klueNLI-augSTS"
        
    def _load_model_impl(self) -> Any:
        """KR-SBERT 모델 로드"""
        try:
            from sentence_transformers import SentenceTransformer
            
            print(f"🔤 KR-SBERT 모델 로딩 중: {self.model_name}")
            model = SentenceTransformer(self.model_name)
            
            # 디바이스 설정
            if self.config.device == "cuda" and model.device.type != "cuda":
                model = model.to("cuda")
            
            print(f"✅ KR-SBERT 모델 로드 완료: {self.model_name}")
            return model
            
        except ImportError:
            raise ModelLoadError(
                self.model_name, 
                "sentence-transformers library not installed. Run: pip install sentence-transformers"
            )
        except Exception as e:
            raise ModelLoadError(self.model_name, str(e))
    
    def encode_texts(self, texts: List[str]) -> List[List[float]]:
        """텍스트 리스트를 임베딩으로 변환"""
        if not self._is_loaded:
            self.load_model()
        
        try:
            # 빈 텍스트 처리
            processed_texts = [text if text.strip() else " " for text in texts]
            
            # 임베딩 생성
            embeddings = self._model.encode(
                processed_texts,
                normalize_embeddings=self.config.additional_params.get("normalize_embeddings", True),
                show_progress_bar=False
            )
            
            return embeddings.tolist()
            
        except Exception as e:
            raise ModelLoadError(self.model_name, f"Encoding failed: {str(e)}")
    
    def calculate_similarity(self, text1: str, text2: str) -> float:
        """두 텍스트 간 코사인 유사도 계산"""
        if not text1.strip() or not text2.strip():
            return 0.0
        
        try:
            embeddings = self.encode_texts([text1, text2])
            similarity = cosine_similarity([embeddings[0]], [embeddings[1]])[0][0]
            return float(similarity)
            
        except Exception as e:
            print(f"⚠️ 유사도 계산 실패: {e}")
            return 0.0
    
    def _get_model_info_impl(self) -> ModelInfo:
        """모델 정보 반환"""
        return ModelInfo(
            name=self.model_name,
            provider="huggingface",
            version="1.0",
            description="Korean Sentence-BERT for semantic similarity",
            supported_languages=["ko", "en"],
            max_input_length=512,
            is_loaded=self._is_loaded
        )
    
    def get_embedding_dimension(self) -> int:
        """임베딩 차원 수 반환"""
        if not self._is_loaded:
            self.load_model()
        return self._model.get_sentence_embedding_dimension()
    
    def classify_text_by_similarity(
        self, 
        text: str, 
        category_examples: dict
    ) -> tuple[str, float]:
        """유사도 기반 텍스트 분류"""
        if not text.strip():
            return "기타", 0.0
        
        best_category = "기타"
        best_score = 0.0
        
        text_embedding = self.encode_single(text)
        
        for category, examples in category_examples.items():
            if not examples:
                continue
            
            # 각 예시와의 유사도 계산
            example_embeddings = self.encode_texts(examples)
            similarities = []
            
            for example_emb in example_embeddings:
                sim = cosine_similarity([text_embedding], [example_emb])[0][0]
                similarities.append(sim)
            
            # 최대 유사도 사용
            max_similarity = max(similarities) if similarities else 0.0
            
            if max_similarity > best_score:
                best_score = max_similarity
                best_category = category
        
        return best_category, float(best_score)


# 한국어 이커머스 특화 카테고리 예시
KOREAN_ECOMMERCE_CATEGORIES = {
    "배송문의": [
        "언제 배송되나요?",
        "배송 조회 부탁드립니다",
        "택배가 언제 오나요?",
        "발송은 언제 되나요?",
        "배송 상태 확인해주세요"
    ],
    "환불문의": [
        "환불 요청합니다",
        "돈 돌려주세요",
        "취소하고 환불받고 싶어요",
        "환불 절차 알려주세요",
        "카드 취소 언제 되나요?"
    ],
    "상품문의": [
        "이 제품 어떤가요?",
        "사이즈가 어떻게 되나요?",
        "색상이 다른가요?",
        "품질이 좋나요?",
        "재질이 뭔가요?"
    ],
    "교환반품": [
        "교환하고 싶어요",
        "반품 신청합니다",
        "다른 사이즈로 바꿔주세요",
        "불량품이에요",
        "파손되어 왔어요"
    ],
    "결제문의": [
        "결제가 안돼요",
        "카드 오류나요",
        "할인 적용 안됐어요",
        "쿠폰 사용법 알려주세요",
        "적립금 언제 들어오나요?"
    ],
    "기타": [
        "안녕하세요",
        "문의드립니다",
        "확인 부탁드려요",
        "도움이 필요해요"
    ]
}


def create_kr_sbert_model(
    model_name: str = "snunlp/KR-SBERT-V40K-klueNLI-augSTS",
    device: str = "cpu",
    normalize_embeddings: bool = True
) -> KRSBERTEmbeddingModel:
    """KR-SBERT 모델 생성 헬퍼 함수"""
    from qa_analysis_system.core.interfaces.ai_model_interface import ModelConfig, ModelProvider, ModelType
    
    config = ModelConfig(
        name=model_name,
        provider=ModelProvider.HUGGINGFACE,
        model_type=ModelType.EMBEDDING,
        device=device,
        additional_params={
            "normalize_embeddings": normalize_embeddings
        }
    )
    
    return KRSBERTEmbeddingModel(config)
