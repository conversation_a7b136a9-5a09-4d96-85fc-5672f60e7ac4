"""
QA 쌍 도메인 모델 - 핵심 비즈니스 엔티티
"""
from dataclasses import dataclass, field
from typing import Optional, List, Dict, Any
from datetime import datetime
from enum import Enum

from qa_analysis_system.shared.exceptions.analysis_exceptions import InvalidQAPairError


class AnalysisType(Enum):
    """분석 타입"""
    FULL = "full"
    SIMILARITY_ONLY = "similarity_only"
    QUALITY_ONLY = "quality_only"


class Grade(Enum):
    """등급"""
    A = "A"
    B = "B" 
    C = "C"
    D = "D"
    F = "F"


@dataclass(frozen=True)
class QAPairId:
    """QA 쌍 식별자 (Value Object)"""
    value: str
    
    def __post_init__(self):
        if not self.value or not isinstance(self.value, str):
            raise InvalidQAPairError("QA Pair ID must be a non-empty string")


@dataclass(frozen=True)
class Question:
    """질문 (Value Object)"""
    subject: str
    content: str
    
    def __post_init__(self):
        if not self.subject and not self.content:
            raise InvalidQAPairError("Question must have either subject or content")
    
    @property
    def full_text(self) -> str:
        """전체 질문 텍스트"""
        return f"{self.subject} {self.content}".strip()
    
    @property
    def word_count(self) -> int:
        """단어 수"""
        return len(self.full_text.split())


@dataclass(frozen=True)
class Answer:
    """답변 (Value Object)"""
    content: str
    created_at: Optional[datetime] = None
    
    def __post_init__(self):
        # 빈 답변도 허용 (미답변 상태)
        pass
    
    @property
    def is_empty(self) -> bool:
        """빈 답변 여부"""
        return not self.content or self.content.strip().lower() in ["", "nan", "null"]
    
    @property
    def word_count(self) -> int:
        """단어 수"""
        if self.is_empty:
            return 0
        return len(self.content.split())
    
    @property
    def character_count(self) -> int:
        """문자 수"""
        if self.is_empty:
            return 0
        return len(self.content)


@dataclass(frozen=True)
class AnalysisScores:
    """분석 점수 (Value Object)"""
    semantic_similarity: float
    topic_relevance: float
    keyword_overlap: float
    answer_completeness: float
    answer_accuracy: float
    answer_helpfulness: float
    
    def __post_init__(self):
        # 모든 점수는 0.0 ~ 1.0 범위
        for field_name, value in self.__dict__.items():
            if not isinstance(value, (int, float)) or not (0.0 <= value <= 1.0):
                raise InvalidQAPairError(f"{field_name} must be between 0.0 and 1.0")
    
    def calculate_overall_score(self, weights: Dict[str, float]) -> float:
        """가중평균으로 종합 점수 계산"""
        total_score = 0.0
        total_weight = 0.0
        
        for field_name, weight in weights.items():
            if hasattr(self, field_name):
                score = getattr(self, field_name)
                total_score += score * weight
                total_weight += weight
        
        return total_score / total_weight if total_weight > 0 else 0.0


@dataclass(frozen=True)
class AnalysisFeedback:
    """분석 피드백 (Value Object)"""
    strengths: List[str] = field(default_factory=list)
    weaknesses: List[str] = field(default_factory=list)
    recommendations: List[str] = field(default_factory=list)
    
    def __post_init__(self):
        # 리스트들을 튜플로 변환하여 불변성 보장
        object.__setattr__(self, 'strengths', tuple(self.strengths))
        object.__setattr__(self, 'weaknesses', tuple(self.weaknesses))
        object.__setattr__(self, 'recommendations', tuple(self.recommendations))


@dataclass
class QAPair:
    """QA 쌍 엔티티 - 핵심 도메인 객체"""
    
    # 식별자
    id: QAPairId
    
    # 핵심 데이터
    question: Question
    answer: Answer
    
    # 메타데이터
    created_at: datetime = field(default_factory=datetime.now)
    source: str = "unknown"
    
    # 분석 결과 (선택적)
    scores: Optional[AnalysisScores] = None
    feedback: Optional[AnalysisFeedback] = None
    overall_score: Optional[float] = None
    grade: Optional[Grade] = None
    pass_threshold: Optional[bool] = None
    
    # 분석 메타데이터
    analysis_timestamp: Optional[datetime] = None
    processing_time_ms: Optional[float] = None
    model_info: Optional[Dict[str, str]] = None
    
    def __post_init__(self):
        """초기화 후 검증"""
        if not isinstance(self.id, QAPairId):
            raise InvalidQAPairError("Invalid QA Pair ID")
        
        if not isinstance(self.question, Question):
            raise InvalidQAPairError("Invalid Question")
            
        if not isinstance(self.answer, Answer):
            raise InvalidQAPairError("Invalid Answer")
    
    def is_analyzed(self) -> bool:
        """분석 완료 여부"""
        return self.scores is not None
    
    def is_answered(self) -> bool:
        """답변 존재 여부"""
        return not self.answer.is_empty
    
    def update_analysis_result(
        self,
        scores: AnalysisScores,
        feedback: AnalysisFeedback,
        overall_score: float,
        grade: Grade,
        pass_threshold: bool,
        processing_time_ms: float,
        model_info: Dict[str, str]
    ) -> None:
        """분석 결과 업데이트"""
        self.scores = scores
        self.feedback = feedback
        self.overall_score = overall_score
        self.grade = grade
        self.pass_threshold = pass_threshold
        self.analysis_timestamp = datetime.now()
        self.processing_time_ms = processing_time_ms
        self.model_info = model_info
    
    def to_dict(self) -> Dict[str, Any]:
        """딕셔너리 변환"""
        result = {
            "id": self.id.value,
            "question": {
                "subject": self.question.subject,
                "content": self.question.content,
                "full_text": self.question.full_text,
                "word_count": self.question.word_count
            },
            "answer": {
                "content": self.answer.content,
                "is_empty": self.answer.is_empty,
                "word_count": self.answer.word_count,
                "character_count": self.answer.character_count
            },
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "source": self.source
        }
        
        # 분석 결과가 있는 경우 추가
        if self.is_analyzed():
            result.update({
                "scores": {
                    "semantic_similarity": self.scores.semantic_similarity,
                    "topic_relevance": self.scores.topic_relevance,
                    "keyword_overlap": self.scores.keyword_overlap,
                    "answer_completeness": self.scores.answer_completeness,
                    "answer_accuracy": self.scores.answer_accuracy,
                    "answer_helpfulness": self.scores.answer_helpfulness
                },
                "feedback": {
                    "strengths": list(self.feedback.strengths),
                    "weaknesses": list(self.feedback.weaknesses),
                    "recommendations": list(self.feedback.recommendations)
                },
                "overall_score": self.overall_score,
                "grade": self.grade.value if self.grade else None,
                "pass_threshold": self.pass_threshold,
                "analysis_timestamp": self.analysis_timestamp.isoformat() if self.analysis_timestamp else None,
                "processing_time_ms": self.processing_time_ms,
                "model_info": self.model_info
            })
        
        return result
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'QAPair':
        """딕셔너리에서 생성"""
        qa_pair = cls(
            id=QAPairId(data["id"]),
            question=Question(
                subject=data["question"]["subject"],
                content=data["question"]["content"]
            ),
            answer=Answer(
                content=data["answer"]["content"]
            ),
            created_at=datetime.fromisoformat(data["created_at"]) if data.get("created_at") else datetime.now(),
            source=data.get("source", "unknown")
        )
        
        # 분석 결과가 있는 경우 복원
        if "scores" in data:
            scores = AnalysisScores(**data["scores"])
            feedback = AnalysisFeedback(**data["feedback"])
            grade = Grade(data["grade"]) if data.get("grade") else None
            
            qa_pair.update_analysis_result(
                scores=scores,
                feedback=feedback,
                overall_score=data["overall_score"],
                grade=grade,
                pass_threshold=data["pass_threshold"],
                processing_time_ms=data["processing_time_ms"],
                model_info=data["model_info"]
            )
        
        return qa_pair
    
    def __str__(self) -> str:
        """문자열 표현"""
        status = "분석완료" if self.is_analyzed() else "미분석"
        answered = "답변있음" if self.is_answered() else "미답변"
        return f"QAPair({self.id.value[:8]}..., {status}, {answered})"
    
    def __repr__(self) -> str:
        """개발자용 문자열 표현"""
        return (f"QAPair(id={self.id.value}, "
                f"question_words={self.question.word_count}, "
                f"answer_words={self.answer.word_count}, "
                f"analyzed={self.is_analyzed()})")


# 팩토리 함수들
def create_qa_pair_from_inquiry(
    inquiry_id: str,
    subject: str,
    content: str,
    answer: str,
    created_at: Optional[datetime] = None,
    source: str = "database"
) -> QAPair:
    """문의 데이터에서 QA 쌍 생성"""
    return QAPair(
        id=QAPairId(inquiry_id),
        question=Question(subject=subject, content=content),
        answer=Answer(content=answer, created_at=created_at),
        created_at=created_at or datetime.now(),
        source=source
    )


def create_qa_pair_from_text(
    qa_id: str,
    question_text: str,
    answer_text: str,
    source: str = "manual"
) -> QAPair:
    """텍스트에서 QA 쌍 생성"""
    return QAPair(
        id=QAPairId(qa_id),
        question=Question(subject="", content=question_text),
        answer=Answer(content=answer_text),
        source=source
    )
