"""
AI 모델 인터페이스 - SOLID 원칙 적용
"""
from abc import ABC, abstractmethod
from typing import List, Tuple, Dict, Any, Optional, Protocol
from dataclasses import dataclass
from enum import Enum

from qa_analysis_system.core.domain.qa_pair import QAPair, AnalysisScores


class ModelType(Enum):
    """모델 타입"""
    EMBEDDING = "embedding"
    LLM = "llm"
    CLASSIFICATION = "classification"


class ModelProvider(Enum):
    """모델 제공자"""
    HUGGINGFACE = "huggingface"
    OPENAI = "openai"
    ANTHROPIC = "anthropic"
    GOOGLE = "google"
    LOCAL = "local"


@dataclass(frozen=True)
class ModelConfig:
    """모델 설정 (Value Object)"""
    name: str
    provider: ModelProvider
    model_type: ModelType
    model_path: Optional[str] = None
    api_key: Optional[str] = None
    max_tokens: Optional[int] = None
    temperature: Optional[float] = None
    device: str = "cpu"
    additional_params: Optional[Dict[str, Any]] = None
    
    def __post_init__(self):
        if self.additional_params is None:
            object.__setattr__(self, 'additional_params', {})


@dataclass(frozen=True)
class ModelInfo:
    """모델 정보 (Value Object)"""
    name: str
    provider: str
    version: str
    description: str
    supported_languages: List[str]
    max_input_length: int
    is_loaded: bool = False


class EmbeddingModelInterface(ABC):
    """임베딩 모델 인터페이스 (ISP 적용)"""
    
    @abstractmethod
    def load_model(self) -> None:
        """모델 로드"""
        pass
    
    @abstractmethod
    def encode_texts(self, texts: List[str]) -> List[List[float]]:
        """텍스트 리스트를 임베딩으로 변환"""
        pass
    
    @abstractmethod
    def encode_single(self, text: str) -> List[float]:
        """단일 텍스트를 임베딩으로 변환"""
        pass
    
    @abstractmethod
    def calculate_similarity(self, text1: str, text2: str) -> float:
        """두 텍스트 간 유사도 계산"""
        pass
    
    @abstractmethod
    def get_model_info(self) -> ModelInfo:
        """모델 정보 반환"""
        pass
    
    @abstractmethod
    def is_loaded(self) -> bool:
        """모델 로드 상태 확인"""
        pass


class LLMInterface(ABC):
    """LLM 인터페이스 (ISP 적용)"""
    
    @abstractmethod
    async def initialize(self) -> None:
        """모델 초기화"""
        pass
    
    @abstractmethod
    async def generate_response(self, prompt: str, **kwargs) -> str:
        """응답 생성"""
        pass
    
    @abstractmethod
    async def classify_text(self, text: str, categories: List[str]) -> Tuple[str, float]:
        """텍스트 분류"""
        pass
    
    @abstractmethod
    async def analyze_sentiment(self, text: str) -> Tuple[str, float]:
        """감정 분석"""
        pass
    
    @abstractmethod
    def get_model_info(self) -> ModelInfo:
        """모델 정보 반환"""
        pass
    
    @abstractmethod
    def is_initialized(self) -> bool:
        """초기화 상태 확인"""
        pass


class ClassificationModelInterface(ABC):
    """분류 모델 인터페이스 (ISP 적용)"""
    
    @abstractmethod
    def load_model(self) -> None:
        """모델 로드"""
        pass
    
    @abstractmethod
    def predict(self, text: str) -> Tuple[str, float]:
        """단일 텍스트 분류"""
        pass
    
    @abstractmethod
    def predict_batch(self, texts: List[str]) -> List[Tuple[str, float]]:
        """배치 텍스트 분류"""
        pass
    
    @abstractmethod
    def get_categories(self) -> List[str]:
        """지원하는 카테고리 목록"""
        pass
    
    @abstractmethod
    def get_model_info(self) -> ModelInfo:
        """모델 정보 반환"""
        pass


class QAAnalysisModelInterface(ABC):
    """QA 분석 전용 모델 인터페이스"""
    
    @abstractmethod
    async def analyze_qa_pair(self, qa_pair: QAPair) -> AnalysisScores:
        """QA 쌍 분석"""
        pass
    
    @abstractmethod
    async def generate_feedback(
        self, 
        qa_pair: QAPair, 
        scores: AnalysisScores
    ) -> Tuple[List[str], List[str], List[str]]:
        """피드백 생성 (강점, 약점, 개선사항)"""
        pass


# Protocol for dependency injection
class ModelFactory(Protocol):
    """모델 팩토리 프로토콜 (DIP 적용)"""
    
    def create_embedding_model(self, config: ModelConfig) -> EmbeddingModelInterface:
        """임베딩 모델 생성"""
        ...
    
    def create_llm_model(self, config: ModelConfig) -> LLMInterface:
        """LLM 모델 생성"""
        ...
    
    def create_classification_model(self, config: ModelConfig) -> ClassificationModelInterface:
        """분류 모델 생성"""
        ...
    
    def list_available_models(self) -> Dict[ModelType, List[str]]:
        """사용 가능한 모델 목록"""
        ...


class ModelRegistry(Protocol):
    """모델 레지스트리 프로토콜"""
    
    def register_model(
        self, 
        model_type: ModelType, 
        provider: ModelProvider, 
        model_class: type
    ) -> None:
        """모델 등록"""
        ...
    
    def get_model_class(
        self, 
        model_type: ModelType, 
        provider: ModelProvider
    ) -> type:
        """모델 클래스 조회"""
        ...
    
    def list_registered_models(self) -> Dict[str, List[str]]:
        """등록된 모델 목록"""
        ...


# Base classes for common functionality
class BaseModel(ABC):
    """모델 기본 클래스"""
    
    def __init__(self, config: ModelConfig):
        self.config = config
        self._is_loaded = False
        self._model = None
    
    @property
    def name(self) -> str:
        """모델 이름"""
        return self.config.name
    
    @property
    def provider(self) -> ModelProvider:
        """모델 제공자"""
        return self.config.provider
    
    @abstractmethod
    def _load_model_impl(self) -> Any:
        """모델 로드 구현"""
        pass
    
    @abstractmethod
    def _get_model_info_impl(self) -> ModelInfo:
        """모델 정보 구현"""
        pass
    
    def is_loaded(self) -> bool:
        """로드 상태 확인"""
        return self._is_loaded
    
    def get_model_info(self) -> ModelInfo:
        """모델 정보 반환"""
        return self._get_model_info_impl()


class BaseEmbeddingModel(BaseModel, EmbeddingModelInterface):
    """임베딩 모델 기본 클래스"""
    
    def load_model(self) -> None:
        """모델 로드"""
        if not self._is_loaded:
            self._model = self._load_model_impl()
            self._is_loaded = True
    
    def encode_single(self, text: str) -> List[float]:
        """단일 텍스트 인코딩"""
        return self.encode_texts([text])[0]


class BaseLLMModel(BaseModel, LLMInterface):
    """LLM 모델 기본 클래스"""
    
    def __init__(self, config: ModelConfig):
        super().__init__(config)
        self._is_initialized = False
        self._client = None
    
    async def initialize(self) -> None:
        """초기화"""
        if not self._is_initialized:
            self._client = await self._initialize_client_impl()
            self._is_initialized = True
    
    def is_initialized(self) -> bool:
        """초기화 상태 확인"""
        return self._is_initialized
    
    @abstractmethod
    async def _initialize_client_impl(self) -> Any:
        """클라이언트 초기화 구현"""
        pass


class BaseClassificationModel(BaseModel, ClassificationModelInterface):
    """분류 모델 기본 클래스"""
    
    def predict_batch(self, texts: List[str]) -> List[Tuple[str, float]]:
        """배치 예측 기본 구현"""
        return [self.predict(text) for text in texts]
