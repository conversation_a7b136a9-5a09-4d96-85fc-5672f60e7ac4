"""
QA 분석 에이전트 v2.0 - SOLID 원칙 적용
"""
import asyncio
import time
import json
from typing import List, Optional, Dict, Any
from datetime import datetime

from qa_analysis_system.core.domain.qa_pair import (
    QAPair, AnalysisScores, AnalysisFeedback, Grade, 
    create_qa_pair_from_inquiry, create_qa_pair_from_text
)
from qa_analysis_system.core.interfaces.ai_model_interface import (
    EmbeddingModelInterface, LLMInterface, ClassificationModelInterface
)
from qa_analysis_system.config.managers.config_manager import ConfigManager
from qa_analysis_system.infrastructure.ai_models.model_factory import get_model_factory
from qa_analysis_system.application.dto.analysis_request import AnalysisRequest, BatchAnalysisRequest
from qa_analysis_system.application.dto.analysis_response import AnalysisResponse, BatchAnalysisResponse
from qa_analysis_system.shared.exceptions.analysis_exceptions import AnalysisError, AnalysisTimeoutError


class QAAnalysisAgent:
    """QA 분석 에이전트 - Facade 패턴 적용"""
    
    def __init__(
        self,
        config_manager: Optional[ConfigManager] = None,
        supabase_url: Optional[str] = None,
        supabase_key: Optional[str] = None
    ):
        """
        에이전트 초기화
        
        Args:
            config_manager: 설정 관리자
            supabase_url: Supabase URL (DB 연동용)
            supabase_key: Supabase API 키 (DB 연동용)
        """
        # 의존성 주입 (DIP 적용)
        self.config_manager = config_manager or ConfigManager()
        self.model_factory = get_model_factory()
        
        # 모델 인스턴스 (지연 로딩)
        self._embedding_model: Optional[EmbeddingModelInterface] = None
        self._llm_model: Optional[LLMInterface] = None
        self._classification_model: Optional[ClassificationModelInterface] = None
        
        # DB 서비스 (선택적)
        self._db_service = None
        if supabase_url and supabase_key:
            self._init_db_service(supabase_url, supabase_key)
        
        # 분석 설정
        self._analysis_settings = self.config_manager.get_analysis_settings()
        
        # 캐시 (성능 최적화)
        self._similarity_cache = {}
        self._classification_cache = {}
        
        print(f"✅ QA 분석 에이전트 v2.0 초기화 완료")
        print(f"📋 현재 프로필: {self.config_manager.get_current_profile()}")
    
    def _init_db_service(self, supabase_url: str, supabase_key: str) -> None:
        """DB 서비스 초기화"""
        try:
            from src.services.async_inquiry_service import AsyncInquiryService
            self._db_service = AsyncInquiryService(supabase_url, supabase_key)
            print("✅ DB 서비스 연결 완료")
        except Exception as e:
            print(f"⚠️ DB 서비스 초기화 실패: {e}")
    
    def _get_embedding_model(self) -> EmbeddingModelInterface:
        """임베딩 모델 지연 로딩"""
        if self._embedding_model is None:
            config = self.config_manager.get_embedding_model_config()
            self._embedding_model = self.model_factory.create_embedding_model(config)
            self._embedding_model.load_model()
        return self._embedding_model
    
    async def _get_llm_model(self) -> LLMInterface:
        """LLM 모델 지연 로딩"""
        if self._llm_model is None:
            config = self.config_manager.get_llm_model_config()
            self._llm_model = self.model_factory.create_llm_model(config)
            await self._llm_model.initialize()
        return self._llm_model
    
    def _get_classification_model(self) -> Optional[ClassificationModelInterface]:
        """분류 모델 지연 로딩"""
        if self._classification_model is None:
            config = self.config_manager.get_classification_model_config()
            if config:
                self._classification_model = self.model_factory.create_classification_model(config)
                self._classification_model.load_model()
        return self._classification_model
    
    async def _load_qa_pair_from_db(self, inquiry_id: str) -> QAPair:
        """DB에서 QA 쌍 로드"""
        if not self._db_service:
            raise AnalysisError("DB service not available")
        
        try:
            inquiries = await self._db_service.get_all_inquiries(
                table='personal',
                limit=1000
            )
            
            target_inquiry = None
            for inquiry in inquiries:
                if inquiry.get('id') == inquiry_id:
                    target_inquiry = inquiry
                    break
            
            if not target_inquiry:
                raise AnalysisError(f"Inquiry not found: {inquiry_id}")
            
            return create_qa_pair_from_inquiry(
                inquiry_id=inquiry_id,
                subject=target_inquiry.get('subject', '') or '',
                content=target_inquiry.get('content', '') or '',
                answer=target_inquiry.get('answer', '') or '',
                created_at=None,
                source="database"
            )
            
        except Exception as e:
            raise AnalysisError(f"Failed to load QA pair from DB: {str(e)}")
    
    def _calculate_semantic_similarity(self, qa_pair: QAPair) -> float:
        """의미적 유사도 계산"""
        question_text = qa_pair.question.full_text
        answer_text = qa_pair.answer.content
        
        if not question_text or not answer_text or qa_pair.answer.is_empty:
            return 0.0
        
        # 캐시 확인
        cache_key = hash(f"{question_text[:100]}{answer_text[:100]}")
        if cache_key in self._similarity_cache:
            return self._similarity_cache[cache_key]
        
        try:
            embedding_model = self._get_embedding_model()
            similarity = embedding_model.calculate_similarity(question_text, answer_text)
            self._similarity_cache[cache_key] = similarity
            return similarity
        except Exception as e:
            print(f"⚠️ 유사도 계산 실패: {e}")
            return 0.0
    
    def _calculate_topic_relevance(self, qa_pair: QAPair) -> float:
        """주제 관련성 계산"""
        question_text = qa_pair.question.full_text
        answer_text = qa_pair.answer.content
        
        if not question_text or qa_pair.answer.is_empty:
            return 0.0
        
        # 간단한 키워드 기반 관련성 계산
        import re
        
        # 한국어 불용어
        stopwords = {'은', '는', '이', '가', '을', '를', '에', '의', '와', '과', '도', '만'}
        
        # 키워드 추출
        question_words = set(re.findall(r'[가-힣a-zA-Z0-9]+', question_text.lower()))
        answer_words = set(re.findall(r'[가-힣a-zA-Z0-9]+', answer_text.lower()))
        
        # 불용어 제거
        question_words = {w for w in question_words if len(w) > 1 and w not in stopwords}
        answer_words = {w for w in answer_words if len(w) > 1 and w not in stopwords}
        
        if not question_words:
            return 0.0
        
        # 교집합 비율
        common_words = question_words & answer_words
        relevance = len(common_words) / len(question_words)
        
        return min(1.0, relevance)
    
    def _calculate_keyword_overlap(self, qa_pair: QAPair) -> float:
        """키워드 겹침도 계산"""
        question_text = qa_pair.question.full_text
        answer_text = qa_pair.answer.content
        
        if not question_text or qa_pair.answer.is_empty:
            return 0.0
        
        import re
        
        question_words = set(re.findall(r'[가-힣a-zA-Z0-9]+', question_text.lower()))
        answer_words = set(re.findall(r'[가-힣a-zA-Z0-9]+', answer_text.lower()))
        
        if not question_words and not answer_words:
            return 0.0
        
        overlap = len(question_words & answer_words)
        total = len(question_words | answer_words)
        
        return overlap / total if total > 0 else 0.0
    
    def _evaluate_answer_quality(self, qa_pair: QAPair) -> tuple[float, float, float]:
        """답변 품질 평가 (완성도, 정확성, 도움도)"""
        answer_text = qa_pair.answer.content
        
        if qa_pair.answer.is_empty:
            return 0.0, 0.0, 0.0
        
        # 완성도 (길이 기반)
        completeness = min(1.0, len(answer_text) / 200)
        
        # 정중함 보너스
        politeness_keywords = ["안녕하세요", "감사합니다", "죄송합니다", "고객님"]
        if any(keyword in answer_text for keyword in politeness_keywords):
            completeness += 0.1
        
        # 구체적 정보 보너스
        specific_keywords = ["주문번호", "운송장", "연락드리겠습니다", "처리", "확인"]
        if any(keyword in answer_text for keyword in specific_keywords):
            completeness += 0.2
        
        completeness = min(1.0, completeness)
        
        # 정확성 (주제 관련성과 동일)
        accuracy = self._calculate_topic_relevance(qa_pair)
        
        # 도움도
        helpfulness = 0.5  # 기본 점수
        
        # 해결 지향적 표현
        solution_keywords = ["해결", "처리", "완료", "안내", "확인", "도움"]
        if any(keyword in answer_text for keyword in solution_keywords):
            helpfulness += 0.3
        
        # 추가 지원 제안
        support_keywords = ["고객센터", "문의", "연락", "게시판", "상담"]
        if any(keyword in answer_text for keyword in support_keywords):
            helpfulness += 0.2
        
        helpfulness = min(1.0, helpfulness)
        
        return completeness, accuracy, helpfulness
    
    def _generate_feedback(self, qa_pair: QAPair, scores: AnalysisScores) -> AnalysisFeedback:
        """피드백 생성"""
        strengths = []
        weaknesses = []
        recommendations = []
        
        question_text = qa_pair.question.full_text
        answer_text = qa_pair.answer.content
        
        # 강점 분석
        if scores.semantic_similarity > 0.7:
            strengths.append("질문과 답변의 의미적 연관성이 높음")
        
        if scores.answer_completeness > 0.8:
            strengths.append("답변이 충분히 상세함")
        
        if "안녕하세요" in answer_text and "감사합니다" in answer_text:
            strengths.append("정중하고 친절한 답변 톤")
        
        if any(word in answer_text for word in ["주문번호", "운송장", "처리"]):
            strengths.append("구체적인 정보 제공")
        
        # 약점 분석
        if scores.semantic_similarity < 0.5:
            weaknesses.append("질문과 답변의 연관성이 낮음")
        
        if scores.answer_completeness < 0.5:
            weaknesses.append("답변이 너무 간단함")
        
        if scores.topic_relevance < 0.6:
            weaknesses.append("질문의 핵심 주제를 충분히 다루지 못함")
        
        if qa_pair.answer.is_empty:
            weaknesses.append("답변이 없음")
        
        # 개선사항 제안
        if scores.semantic_similarity < 0.6:
            recommendations.append("질문의 핵심 키워드를 답변에 더 많이 포함")
        
        if scores.answer_completeness < 0.7:
            recommendations.append("더 상세하고 구체적인 답변 제공")
        
        if scores.answer_helpfulness < 0.7:
            recommendations.append("고객이 다음에 취할 수 있는 구체적 행동 안내")
        
        if not any(word in answer_text for word in ["고객센터", "문의"]):
            recommendations.append("추가 문의 채널 안내 추가")
        
        return AnalysisFeedback(
            strengths=strengths,
            weaknesses=weaknesses,
            recommendations=recommendations
        )
    
    def _determine_grade(self, overall_score: float) -> Grade:
        """점수에 따른 등급 결정"""
        thresholds = self._analysis_settings.get("grade_thresholds", {
            "A": 0.9, "B": 0.8, "C": 0.7, "D": 0.6, "F": 0.0
        })
        
        for grade_str, threshold in thresholds.items():
            if overall_score >= threshold:
                return Grade(grade_str)
        
        return Grade.F
    
    async def analyze_qa_pair(self, request: AnalysisRequest) -> AnalysisResponse:
        """QA 쌍 분석 (메인 메서드)"""
        start_time = time.time()
        
        try:
            # QA 쌍 로드
            if request.is_db_based():
                qa_pair = await self._load_qa_pair_from_db(request.inquiry_id)
            else:
                qa_pair = create_qa_pair_from_text(
                    qa_id=request.inquiry_id or f"manual_{int(time.time())}",
                    question_text=request.question_text,
                    answer_text=request.answer_text or "",
                    source="manual"
                )
            
            # 분석 실행
            scores_dict = {}
            
            if request.analysis_type in ["full", "similarity_only"]:
                scores_dict['semantic_similarity'] = self._calculate_semantic_similarity(qa_pair)
                scores_dict['topic_relevance'] = self._calculate_topic_relevance(qa_pair)
                scores_dict['keyword_overlap'] = self._calculate_keyword_overlap(qa_pair)
            else:
                scores_dict['semantic_similarity'] = 0.0
                scores_dict['topic_relevance'] = 0.0
                scores_dict['keyword_overlap'] = 0.0
            
            if request.analysis_type in ["full", "quality_only"]:
                completeness, accuracy, helpfulness = self._evaluate_answer_quality(qa_pair)
                scores_dict['answer_completeness'] = completeness
                scores_dict['answer_accuracy'] = accuracy
                scores_dict['answer_helpfulness'] = helpfulness
            else:
                scores_dict['answer_completeness'] = 0.0
                scores_dict['answer_accuracy'] = 0.0
                scores_dict['answer_helpfulness'] = 0.0
            
            # 분석 점수 객체 생성
            scores = AnalysisScores(**scores_dict)
            
            # 종합 점수 계산
            weights = self._analysis_settings.get("score_weights", {
                "semantic_similarity": 0.25,
                "topic_relevance": 0.25,
                "keyword_overlap": 0.15,
                "answer_completeness": 0.15,
                "answer_accuracy": 0.10,
                "answer_helpfulness": 0.10
            })
            
            overall_score = scores.calculate_overall_score(weights)
            
            # 통과 여부 및 등급
            pass_threshold_value = self._analysis_settings.get("pass_threshold", 0.6)
            pass_threshold = overall_score >= pass_threshold_value
            grade = self._determine_grade(overall_score)
            
            # 피드백 생성
            feedback = AnalysisFeedback()
            if request.include_feedback:
                feedback = self._generate_feedback(qa_pair, scores)
            
            # 처리 시간 계산
            processing_time = (time.time() - start_time) * 1000
            
            # 모델 정보
            model_info = {
                "embedding_model": self._embedding_model.get_model_info().name if self._embedding_model else "N/A",
                "llm_model": "N/A",  # LLM은 현재 사용하지 않음
                "profile": self.config_manager.get_current_profile()
            }
            
            # 응답 생성
            return AnalysisResponse(
                inquiry_id=qa_pair.id.value,
                question_text=qa_pair.question.full_text,
                answer_text=qa_pair.answer.content,
                semantic_similarity=scores.semantic_similarity,
                topic_relevance=scores.topic_relevance,
                keyword_overlap=scores.keyword_overlap,
                answer_completeness=scores.answer_completeness,
                answer_accuracy=scores.answer_accuracy,
                answer_helpfulness=scores.answer_helpfulness,
                overall_score=overall_score,
                pass_threshold=pass_threshold,
                grade=grade.value,
                strengths=list(feedback.strengths),
                weaknesses=list(feedback.weaknesses),
                recommendations=list(feedback.recommendations),
                analysis_timestamp=datetime.now().isoformat(),
                processing_time_ms=processing_time,
                model_info=model_info,
                analysis_type=request.analysis_type
            )
            
        except Exception as e:
            error_msg = f"Analysis failed: {str(e)}"
            print(f"❌ {error_msg}")
            
            return AnalysisResponse.create_error_response(
                inquiry_id=request.inquiry_id or "unknown",
                error_message=error_msg,
                question_text=request.question_text or "",
                answer_text=request.answer_text or ""
            )
    
    async def analyze_multiple_pairs(self, requests: List[AnalysisRequest]) -> List[AnalysisResponse]:
        """여러 QA 쌍 일괄 분석"""
        results = []
        
        for request in requests:
            try:
                result = await self.analyze_qa_pair(request)
                results.append(result)
            except Exception as e:
                print(f"⚠️ 분석 실패 (ID: {request.inquiry_id}): {e}")
                error_response = AnalysisResponse.create_error_response(
                    inquiry_id=request.inquiry_id or "unknown",
                    error_message=str(e)
                )
                results.append(error_response)
        
        return results
    
    def export_results(
        self, 
        results: List[AnalysisResponse], 
        filename: str = None
    ) -> Dict[str, Any]:
        """결과 내보내기"""
        if not results:
            return {}
        
        # 배치 응답 생성
        batch_response = BatchAnalysisResponse.from_results(
            results=results,
            processing_time_seconds=sum(r.processing_time_ms for r in results) / 1000,
            batch_settings=self._analysis_settings,
            model_info={
                "profile": self.config_manager.get_current_profile(),
                "embedding_model": self._embedding_model.get_model_info().name if self._embedding_model else "N/A"
            }
        )
        
        # 파일 저장
        if filename:
            try:
                output_settings = self.config_manager.get_output_settings()
                json_format = output_settings.get('json_format', {})
                
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(batch_response.to_json(**json_format))
                print(f"📄 결과를 {filename}에 저장했습니다.")
            except Exception as e:
                print(f"⚠️ 파일 저장 실패: {e}")
        
        return batch_response.to_dict()
    
    def get_agent_info(self) -> Dict[str, Any]:
        """에이전트 정보"""
        return {
            "agent_type": "QA Analysis Agent v2.0",
            "version": "2.0.0",
            "current_profile": self.config_manager.get_current_profile(),
            "available_profiles": self.config_manager.get_available_profiles(),
            "embedding_model": self._embedding_model.get_model_info().name if self._embedding_model else "Not loaded",
            "llm_model": "Not used",
            "db_connected": self._db_service is not None,
            "cache_size": {
                "similarity": len(self._similarity_cache),
                "classification": len(self._classification_cache)
            },
            "analysis_settings": self._analysis_settings
        }
