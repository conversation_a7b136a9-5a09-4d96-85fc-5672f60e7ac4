"""
분석 응답 DTO (Data Transfer Object)
"""
from dataclasses import dataclass, asdict
from typing import List, Dict, Any, Optional
from datetime import datetime
import json


@dataclass
class AnalysisResponse:
    """QA 분석 응답 DTO"""
    
    # 기본 정보
    inquiry_id: str
    question_text: str
    answer_text: str
    
    # 분석 점수
    semantic_similarity: float
    topic_relevance: float
    keyword_overlap: float
    answer_completeness: float
    answer_accuracy: float
    answer_helpfulness: float
    
    # 종합 결과
    overall_score: float
    pass_threshold: bool
    grade: str
    
    # 피드백
    strengths: List[str]
    weaknesses: List[str]
    recommendations: List[str]
    
    # 메타데이터
    analysis_timestamp: str
    processing_time_ms: float
    model_info: Dict[str, str]
    
    # 추가 정보
    analysis_type: str = "full"
    confidence_level: float = 0.0
    error_message: Optional[str] = None
    
    def __post_init__(self):
        """초기화 후 검증"""
        # 점수 범위 검증
        scores = [
            self.semantic_similarity, self.topic_relevance, self.keyword_overlap,
            self.answer_completeness, self.answer_accuracy, self.answer_helpfulness,
            self.overall_score
        ]
        
        for score in scores:
            if not (0.0 <= score <= 1.0):
                raise ValueError(f"Score must be between 0.0 and 1.0, got {score}")
        
        # 등급 검증
        valid_grades = ["A", "B", "C", "D", "F"]
        if self.grade not in valid_grades:
            self.grade = "F"
    
    def is_successful(self) -> bool:
        """분석 성공 여부"""
        return self.error_message is None
    
    def is_high_quality(self) -> bool:
        """고품질 답변 여부 (B등급 이상)"""
        return self.grade in ["A", "B"]
    
    def get_score_breakdown(self) -> Dict[str, float]:
        """점수 세부 분석"""
        return {
            "semantic_similarity": self.semantic_similarity,
            "topic_relevance": self.topic_relevance,
            "keyword_overlap": self.keyword_overlap,
            "answer_completeness": self.answer_completeness,
            "answer_accuracy": self.answer_accuracy,
            "answer_helpfulness": self.answer_helpfulness
        }
    
    def get_feedback_summary(self) -> Dict[str, List[str]]:
        """피드백 요약"""
        return {
            "strengths": self.strengths,
            "weaknesses": self.weaknesses,
            "recommendations": self.recommendations
        }
    
    def to_dict(self) -> Dict[str, Any]:
        """딕셔너리 변환"""
        return asdict(self)
    
    def to_json(self, **kwargs) -> str:
        """JSON 문자열 변환"""
        return json.dumps(self.to_dict(), ensure_ascii=False, **kwargs)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'AnalysisResponse':
        """딕셔너리에서 생성"""
        return cls(**data)
    
    @classmethod
    def create_error_response(
        cls,
        inquiry_id: str,
        error_message: str,
        question_text: str = "",
        answer_text: str = ""
    ) -> 'AnalysisResponse':
        """오류 응답 생성"""
        return cls(
            inquiry_id=inquiry_id,
            question_text=question_text,
            answer_text=answer_text,
            semantic_similarity=0.0,
            topic_relevance=0.0,
            keyword_overlap=0.0,
            answer_completeness=0.0,
            answer_accuracy=0.0,
            answer_helpfulness=0.0,
            overall_score=0.0,
            pass_threshold=False,
            grade="F",
            strengths=[],
            weaknesses=["분석 실패"],
            recommendations=["다시 시도해주세요"],
            analysis_timestamp=datetime.now().isoformat(),
            processing_time_ms=0.0,
            model_info={"error": "analysis_failed"},
            error_message=error_message
        )
    
    def get_performance_category(self) -> str:
        """성능 카테고리"""
        if self.overall_score >= 0.9:
            return "excellent"
        elif self.overall_score >= 0.8:
            return "good"
        elif self.overall_score >= 0.7:
            return "fair"
        elif self.overall_score >= 0.6:
            return "poor"
        else:
            return "very_poor"
    
    def get_priority_level(self) -> str:
        """우선순위 레벨 (개선 필요도 기준)"""
        if not self.pass_threshold:
            return "high"  # 미통과는 높은 우선순위
        elif self.overall_score < 0.7:
            return "medium"
        else:
            return "low"


@dataclass
class BatchAnalysisResponse:
    """배치 분석 응답 DTO"""
    
    # 메타데이터
    total_requests: int
    successful_analyses: int
    failed_analyses: int
    processing_time_seconds: float
    analysis_timestamp: str
    
    # 결과 목록
    results: List[AnalysisResponse]
    
    # 통계
    average_score: float
    pass_rate: float
    grade_distribution: Dict[str, int]
    
    # 설정 정보
    batch_settings: Dict[str, Any]
    model_info: Dict[str, str]
    
    def __post_init__(self):
        """초기화 후 검증"""
        if self.total_requests != len(self.results):
            print(f"⚠️ 요청 수와 결과 수가 일치하지 않음: {self.total_requests} vs {len(self.results)}")
    
    def get_success_rate(self) -> float:
        """성공률"""
        if self.total_requests == 0:
            return 0.0
        return self.successful_analyses / self.total_requests
    
    def get_failed_results(self) -> List[AnalysisResponse]:
        """실패한 결과들"""
        return [result for result in self.results if not result.is_successful()]
    
    def get_high_quality_results(self) -> List[AnalysisResponse]:
        """고품질 결과들"""
        return [result for result in self.results if result.is_high_quality()]
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """성능 요약"""
        performance_counts = {}
        for result in self.results:
            category = result.get_performance_category()
            performance_counts[category] = performance_counts.get(category, 0) + 1
        
        return {
            "total_requests": self.total_requests,
            "success_rate": self.get_success_rate(),
            "pass_rate": self.pass_rate,
            "average_score": self.average_score,
            "grade_distribution": self.grade_distribution,
            "performance_distribution": performance_counts,
            "processing_speed": self.total_requests / self.processing_time_seconds if self.processing_time_seconds > 0 else 0
        }
    
    def get_recommendations_summary(self) -> Dict[str, int]:
        """개선사항 요약"""
        all_recommendations = []
        for result in self.results:
            all_recommendations.extend(result.recommendations)
        
        # 빈도 계산
        rec_counts = {}
        for rec in all_recommendations:
            rec_counts[rec] = rec_counts.get(rec, 0) + 1
        
        # 상위 10개 반환
        sorted_recs = sorted(rec_counts.items(), key=lambda x: x[1], reverse=True)
        return dict(sorted_recs[:10])
    
    def to_dict(self) -> Dict[str, Any]:
        """딕셔너리 변환"""
        return {
            "metadata": {
                "total_requests": self.total_requests,
                "successful_analyses": self.successful_analyses,
                "failed_analyses": self.failed_analyses,
                "processing_time_seconds": self.processing_time_seconds,
                "analysis_timestamp": self.analysis_timestamp,
                "batch_settings": self.batch_settings,
                "model_info": self.model_info
            },
            "statistics": {
                "average_score": self.average_score,
                "pass_rate": self.pass_rate,
                "grade_distribution": self.grade_distribution,
                "performance_summary": self.get_performance_summary(),
                "recommendations_summary": self.get_recommendations_summary()
            },
            "results": [result.to_dict() for result in self.results]
        }
    
    def to_json(self, **kwargs) -> str:
        """JSON 문자열 변환"""
        return json.dumps(self.to_dict(), ensure_ascii=False, **kwargs)
    
    @classmethod
    def from_results(
        cls,
        results: List[AnalysisResponse],
        processing_time_seconds: float,
        batch_settings: Dict[str, Any] = None,
        model_info: Dict[str, str] = None
    ) -> 'BatchAnalysisResponse':
        """결과 목록에서 생성"""
        total_requests = len(results)
        successful_analyses = sum(1 for r in results if r.is_successful())
        failed_analyses = total_requests - successful_analyses
        
        # 통계 계산
        successful_results = [r for r in results if r.is_successful()]
        if successful_results:
            average_score = sum(r.overall_score for r in successful_results) / len(successful_results)
            pass_rate = sum(1 for r in successful_results if r.pass_threshold) / len(successful_results)
        else:
            average_score = 0.0
            pass_rate = 0.0
        
        # 등급 분포
        grade_distribution = {}
        for result in successful_results:
            grade = result.grade
            grade_distribution[grade] = grade_distribution.get(grade, 0) + 1
        
        return cls(
            total_requests=total_requests,
            successful_analyses=successful_analyses,
            failed_analyses=failed_analyses,
            processing_time_seconds=processing_time_seconds,
            analysis_timestamp=datetime.now().isoformat(),
            results=results,
            average_score=average_score,
            pass_rate=pass_rate,
            grade_distribution=grade_distribution,
            batch_settings=batch_settings or {},
            model_info=model_info or {}
        )
