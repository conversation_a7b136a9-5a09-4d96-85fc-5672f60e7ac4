"""
통합 설정 관리자 - SOLID 원칙 적용
"""
import os
import yaml
from typing import Dict, Any, List, Optional
from dataclasses import dataclass
from pathlib import Path

from qa_analysis_system.core.interfaces.ai_model_interface import ModelConfig, ModelProvider, ModelType
from qa_analysis_system.shared.exceptions.model_exceptions import ModelConfigError


@dataclass
class ProfileInfo:
    """프로필 정보"""
    name: str
    description: str
    embedding_model: str
    llm_model: str
    classification_model: str
    performance_level: str
    use_cases: List[str]


class ConfigManager:
    """통합 설정 관리자 (SRP 적용)"""
    
    def __init__(self, config_dir: str = None):
        """
        설정 관리자 초기화

        Args:
            config_dir: 설정 파일 디렉토리 경로
        """
        if config_dir:
            self.config_dir = Path(config_dir)
        else:
            # 현재 파일 기준으로 settings 디렉토리 찾기
            self.config_dir = Path(__file__).parent.parent / "settings"

        self.model_profiles_path = self.config_dir / "model_profiles.yaml"
        self.analysis_config_path = self.config_dir / "analysis_config.yaml"
        self.app_config_path = self.config_dir / "app_config.yaml"
        
        # 설정 데이터
        self._model_profiles = None
        self._analysis_config = None
        self._app_config = None
        self._current_profile = None
        
        # 초기화
        self._load_all_configs()
    
    def _load_all_configs(self) -> None:
        """모든 설정 파일 로드"""
        try:
            self._load_model_profiles()
            self._load_analysis_config()
            self._load_app_config()
            
            # 기본 프로필 설정
            default_profile = self._model_profiles.get("default_profile", "korean_ecommerce_optimized")
            self.set_profile(default_profile)
            
            print(f"✅ 설정 로드 완료 (프로필: {self._current_profile})")
            
        except Exception as e:
            print(f"❌ 설정 로드 실패: {e}")
            self._create_default_configs()
    
    def _load_model_profiles(self) -> None:
        """모델 프로필 설정 로드"""
        if self.model_profiles_path.exists():
            with open(self.model_profiles_path, 'r', encoding='utf-8') as f:
                self._model_profiles = yaml.safe_load(f)
        else:
            raise FileNotFoundError(f"Model profiles config not found: {self.model_profiles_path}")
    
    def _load_analysis_config(self) -> None:
        """분석 설정 로드"""
        if self.analysis_config_path.exists():
            with open(self.analysis_config_path, 'r', encoding='utf-8') as f:
                self._analysis_config = yaml.safe_load(f)
        else:
            self._analysis_config = self._get_default_analysis_config()
    
    def _load_app_config(self) -> None:
        """앱 설정 로드"""
        if self.app_config_path.exists():
            with open(self.app_config_path, 'r', encoding='utf-8') as f:
                self._app_config = yaml.safe_load(f)
        else:
            self._app_config = self._get_default_app_config()
    
    def _create_default_configs(self) -> None:
        """기본 설정 생성"""
        print("🔧 기본 설정 생성 중...")
        
        # 기본 모델 프로필
        self._model_profiles = {
            "default_profile": "korean_ecommerce_optimized",
            "profiles": {
                "korean_ecommerce_optimized": {
                    "name": "한국어 이커머스 최적화",
                    "description": "기본 한국어 분석 프로필",
                    "embedding_model": {
                        "provider": "huggingface",
                        "model_name": "snunlp/KR-SBERT-V40K-klueNLI-augSTS",
                        "model_type": "embedding",
                        "device": "cpu"
                    },
                    "llm_model": {
                        "provider": "openai",
                        "model_name": "gpt-4o-mini",
                        "model_type": "llm",
                        "max_tokens": 500,
                        "temperature": 0.1
                    }
                }
            }
        }
        
        self._analysis_config = self._get_default_analysis_config()
        self._app_config = self._get_default_app_config()
        self._current_profile = "korean_ecommerce_optimized"
    
    def _get_default_analysis_config(self) -> Dict[str, Any]:
        """기본 분석 설정"""
        return {
            "qa_pair_analysis": {
                "similarity_threshold": 0.6,
                "pass_threshold": 0.6,
                "grade_thresholds": {
                    "A": 0.9, "B": 0.8, "C": 0.7, "D": 0.6, "F": 0.0
                },
                "score_weights": {
                    "semantic_similarity": 0.25,
                    "topic_relevance": 0.25,
                    "keyword_overlap": 0.15,
                    "answer_completeness": 0.15,
                    "answer_accuracy": 0.10,
                    "answer_helpfulness": 0.10
                }
            },
            "batch_processing": {
                "max_workers": 4,
                "batch_size": 50,
                "timeout_seconds": 30
            }
        }
    
    def _get_default_app_config(self) -> Dict[str, Any]:
        """기본 앱 설정"""
        return {
            "logging": {
                "level": "INFO",
                "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
            },
            "output": {
                "json_format": {
                    "ensure_ascii": False,
                    "indent": 2
                }
            }
        }
    
    # 프로필 관리 메서드들
    def get_available_profiles(self) -> List[str]:
        """사용 가능한 프로필 목록"""
        if not self._model_profiles or "profiles" not in self._model_profiles:
            return ["korean_ecommerce_optimized"]
        return list(self._model_profiles["profiles"].keys())
    
    def get_current_profile(self) -> str:
        """현재 프로필 이름"""
        return self._current_profile
    
    def set_profile(self, profile_name: str) -> bool:
        """프로필 변경"""
        if profile_name not in self.get_available_profiles():
            raise ModelConfigError(profile_name, f"Profile not found: {profile_name}")
        
        self._current_profile = profile_name
        print(f"✅ 프로필 변경: {profile_name}")
        return True
    
    def get_profile_info(self, profile_name: str = None) -> ProfileInfo:
        """프로필 정보 조회"""
        profile = profile_name or self._current_profile
        
        if profile not in self.get_available_profiles():
            raise ModelConfigError(profile, f"Profile not found: {profile}")
        
        profile_data = self._model_profiles["profiles"][profile]
        
        return ProfileInfo(
            name=profile_data.get("name", profile),
            description=profile_data.get("description", ""),
            embedding_model=profile_data.get("embedding_model", {}).get("model_name", ""),
            llm_model=profile_data.get("llm_model", {}).get("model_name", ""),
            classification_model=profile_data.get("classification_model", {}).get("model_name", ""),
            performance_level=profile_data.get("performance_level", "medium"),
            use_cases=profile_data.get("use_cases", [])
        )
    
    # 모델 설정 메서드들
    def get_embedding_model_config(self, profile_name: str = None) -> ModelConfig:
        """임베딩 모델 설정"""
        profile = profile_name or self._current_profile
        profile_data = self._model_profiles["profiles"][profile]
        embedding_data = profile_data["embedding_model"]
        
        return ModelConfig(
            name=embedding_data["model_name"],
            provider=ModelProvider(embedding_data["provider"]),
            model_type=ModelType.EMBEDDING,
            device=embedding_data.get("device", "cpu"),
            additional_params=embedding_data.get("additional_params", {})
        )
    
    def get_llm_model_config(self, profile_name: str = None) -> ModelConfig:
        """LLM 모델 설정"""
        profile = profile_name or self._current_profile
        profile_data = self._model_profiles["profiles"][profile]
        llm_data = profile_data["llm_model"]
        
        # API 키 가져오기
        api_key = None
        provider = llm_data["provider"]
        if provider == "openai":
            api_key = os.getenv("OPENAI_API_KEY")
        elif provider == "anthropic":
            api_key = os.getenv("ANTHROPIC_API_KEY")
        
        return ModelConfig(
            name=llm_data["model_name"],
            provider=ModelProvider(provider),
            model_type=ModelType.LLM,
            api_key=api_key,
            max_tokens=llm_data.get("max_tokens"),
            temperature=llm_data.get("temperature"),
            additional_params=llm_data.get("additional_params", {})
        )
    
    def get_classification_model_config(self, profile_name: str = None) -> Optional[ModelConfig]:
        """분류 모델 설정"""
        profile = profile_name or self._current_profile
        profile_data = self._model_profiles["profiles"][profile]
        
        if "classification_model" not in profile_data:
            return None
        
        classification_data = profile_data["classification_model"]
        
        return ModelConfig(
            name=classification_data["model_name"],
            provider=ModelProvider(classification_data["provider"]),
            model_type=ModelType.CLASSIFICATION,
            device=classification_data.get("device", "cpu"),
            additional_params=classification_data.get("additional_params", {})
        )
    
    # 분석 설정 메서드들
    def get_analysis_settings(self) -> Dict[str, Any]:
        """분석 설정"""
        return self._analysis_config.get("qa_pair_analysis", {})
    
    def get_batch_settings(self) -> Dict[str, Any]:
        """배치 처리 설정"""
        return self._analysis_config.get("batch_processing", {})
    
    def get_output_settings(self) -> Dict[str, Any]:
        """출력 설정"""
        return self._app_config.get("output", {})
    
    # 유틸리티 메서드들
    def print_current_config(self) -> None:
        """현재 설정 출력"""
        profile_info = self.get_profile_info()
        
        print(f"\n📋 현재 설정 (프로필: {self._current_profile})")
        print("=" * 60)
        print(f"📝 설명: {profile_info.description}")
        print(f"🔤 임베딩 모델: {profile_info.embedding_model}")
        print(f"🤖 LLM 모델: {profile_info.llm_model}")
        if profile_info.classification_model:
            print(f"📊 분류 모델: {profile_info.classification_model}")
        print("=" * 60)
    
    def list_all_profiles(self) -> None:
        """모든 프로필 목록 출력"""
        print(f"\n📋 사용 가능한 프로필:")
        print("-" * 50)
        
        for profile_name in self.get_available_profiles():
            try:
                info = self.get_profile_info(profile_name)
                marker = "✅" if profile_name == self._current_profile else "  "
                print(f"{marker} {profile_name}")
                print(f"     {info.description}")
                print(f"     임베딩: {info.embedding_model}")
                print(f"     LLM: {info.llm_model}")
                print()
            except Exception as e:
                print(f"  ❌ {profile_name} (설정 오류: {e})")
    
    def validate_current_config(self) -> bool:
        """현재 설정 검증"""
        try:
            # 모델 설정 검증
            embedding_config = self.get_embedding_model_config()
            llm_config = self.get_llm_model_config()
            
            # API 키 검증
            if llm_config.provider == ModelProvider.OPENAI and not llm_config.api_key:
                print("⚠️ OpenAI API 키가 설정되지 않았습니다")
                return False
            
            print("✅ 설정 검증 완료")
            return True
            
        except Exception as e:
            print(f"❌ 설정 검증 실패: {e}")
            return False
    
    def get_model_recommendations(self, use_case: str) -> List[str]:
        """사용 사례별 모델 추천"""
        recommendations = {
            "general": ["korean_ecommerce_optimized"],
            "high_accuracy": ["high_performance"],
            "fast_processing": ["lightweight_fast"],
            "sentiment_analysis": ["sentiment_specialized"],
            "experimentation": ["experimental"]
        }
        
        return recommendations.get(use_case, ["korean_ecommerce_optimized"])


# 전역 설정 관리자 인스턴스
_config_manager = None


def get_config_manager() -> ConfigManager:
    """전역 설정 관리자 인스턴스 반환 (Singleton 패턴)"""
    global _config_manager
    if _config_manager is None:
        _config_manager = ConfigManager()
    return _config_manager


def reset_config_manager() -> None:
    """설정 관리자 재설정 (테스트용)"""
    global _config_manager
    _config_manager = None
