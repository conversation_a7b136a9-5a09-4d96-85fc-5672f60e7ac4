[project]
name = "poc"
version = "0.1.0"
description = "이커머스 문의 데이터 처리 및 분류 시스템"
readme = "README.md"
requires-python = ">=3.11.6"
dependencies = [
    # 기본 라이브러리
    "fastapi>=0.100.0",
    "uvicorn>=0.23.0",
    "pydantic>=2.0.0",
    "python-dotenv>=1.0.0",
    "httpx>=0.24.1",

    # 데이터베이스
    "supabase>=2.0.0",
    "asyncpg>=0.28.0",

    # AI/ML
    "numpy>=1.24.0",
    "scikit-learn>=1.3.0",
    "sentence-transformers>=2.2.2",
    "openai>=1.0.0",
    "torch>=2.0.0",
    "transformers>=4.30.0",

    # 유틸리티
    "pandas>=2.0.0",
    "tqdm>=4.65.0",
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",

    # 웹 스크래핑 (기존)
    "bs4>=0.0.2",
    "chardet>=5.2.0",
]
