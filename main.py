#!/usr/bin/env python3
"""
문의 데이터 처리 메인 프로그램
"""
import sys

from config.config import SUPABASE_KEY, SUPABASE_URL
from src.services.async_inquiry_runner import run_async_service
from src.services.bulk_processing_service import run_bulk_processing
from src.services.inquiry_service import get_personal_inquiries_summary


def run_multi_table_processing():
    """여러 테이블 통합 처리 실행"""
    try:
        from src.services.multi_table_inquiry_service import MultiTableInquiryService
        from src.utils.async_utils import run_async_in_thread

        @run_async_in_thread
        async def process():
            service = MultiTableInquiryService(SUPABASE_URL, SUPABASE_KEY)

            # 통계 조회
            stats = await service.get_inquiry_statistics()
            print(f"\n📊 전체 통계: 총 {stats['total_inquiries']}건")
            print(f"답변률: {stats['answer_rate']:.1f}%")

            # 통합 목록 조회
            inquiries = await service.get_unified_inquiry_list(limit_per_table=20)
            service.print_unified_summary(inquiries)

            return 0

        return process()
    except Exception as e:
        print(f"여러 테이블 처리 중 오류: {e}")
        return 1


def run_korean_ai_test():
    """한국어 AI 분류 테스트 실행"""
    try:
        from src.services.embedding_model_service import EmbeddingModelService
        from src.services.llm_service import LLMService
        from config.config import OPENAI_API_KEY

        print("한국어 AI 분류 모델 테스트 시작...")

        # 테스트 문의 샘플
        test_inquiries = [
            "상품이 불량이라서 반품하고 싶어요",
            "배송이 너무 늦어요 언제 오나요?",
            "이 제품 사이즈가 어떻게 되나요?",
            "환불 언제 되나요? 급해요",
            "주문 취소하고 싶습니다"
        ]

        # 1. 임베딩 모델 테스트
        print("\n1️⃣ 한국어 임베딩 모델 테스트")
        print("=" * 50)

        try:
            embedding_service = EmbeddingModelService()

            for i, inquiry in enumerate(test_inquiries, 1):
                category, confidence = embedding_service.classify_text(inquiry)
                print(f"{i}. 문의: {inquiry}")
                print(f"   분류: {category} (신뢰도: {confidence:.3f})")
                print()
        except Exception as e:
            print(f"임베딩 모델 테스트 실패: {e}")

        # 2. LLM 모델 테스트 (OpenAI API 키가 있는 경우)
        if OPENAI_API_KEY:
            print("\n2️⃣ LLM 모델 테스트")
            print("=" * 50)

            try:
                import asyncio
                from src.utils.async_utils import run_async_in_thread

                @run_async_in_thread
                async def test_llm():
                    llm_service = LLMService()

                    for i, inquiry in enumerate(test_inquiries[:2], 1):  # 비용 절약을 위해 2개만
                        try:
                            category, confidence = await llm_service.classify_inquiry(inquiry)
                            print(f"{i}. 문의: {inquiry}")
                            print(f"   LLM 분류: {category} (신뢰도: {confidence:.3f})")
                            print()
                        except Exception as e:
                            print(f"LLM 분류 실패: {e}")

                test_llm()

            except Exception as e:
                print(f"LLM 모델 테스트 실패: {e}")
        else:
            print("\n2️⃣ LLM 테스트 건너뜀 (OpenAI API 키 없음)")

        print("✅ 한국어 AI 분류 테스트 완료!")
        return 0

    except Exception as e:
        print("AI 테스트 중 오류: {e}")
        return 1


def run_comprehensive_analysis():
    """종합 문의 분석 실행"""
    try:
        from src.services.comprehensive_inquiry_service import (
            run_comprehensive_analysis as run_analysis
        )
        from src.utils.async_utils import run_async_in_thread

        @run_async_in_thread
        async def analyze():
            await run_analysis()
            return 0

        return analyze()

    except Exception as e:
        print(f"종합 분석 중 오류: {e}")
        return 1


def run_question_answer_tagging():
    """질문-답변 태깅 시스템 실행"""
    try:
        from src.services.question_answer_tagger import (
            run_question_answer_tagging as run_tagging
        )
        from src.utils.async_utils import run_async_in_thread

        @run_async_in_thread
        async def tag():
            await run_tagging()
            return 0

        return tag()

    except Exception as e:
        print(f"태깅 시스템 중 오류: {e}")
        return 1


def run_optimized_json_analysis():
    """최적화된 JSON 분석 실행"""
    try:
        from src.services.optimized_inquiry_analyzer import (
            run_optimized_analysis
        )
        from src.utils.async_utils import run_async_in_thread

        @run_async_in_thread
        async def analyze():
            await run_optimized_analysis()
            return 0

        return analyze()

    except Exception as e:
        print(f"최적화된 분석 중 오류: {e}")
        return 1


def run_qa_pair_analysis():
    """개별 QA 쌍 유사도 분석 실행"""
    try:
        from src.services.qa_pair_analyzer import (
            run_qa_pair_analysis as run_qa_analysis
        )
        from src.utils.async_utils import run_async_in_thread

        @run_async_in_thread
        async def analyze():
            await run_qa_analysis()
            return 0

        return analyze()

    except Exception as e:
        print(f"QA 쌍 분석 중 오류: {e}")
        return 1


def main():
    """
    메인 실행 함수 - 사용자 선택에 따라 다른 기능을 실행합니다.
    """
    print("=== 문의 데이터 처리 시스템 ===")

    # None 체크
    if not SUPABASE_URL or not SUPABASE_KEY:
        print("오류: Supabase URL 또는 API 키가 설정되지 않았습니다.")
        print("config/config.py 파일을 확인하세요.")
        return 1

    # 메뉴 표시
    print("\n사용할 기능을 선택하세요:")
    print("1. 기본 문의 데이터 조회 (동기식)")
    print("2. 비동기 문의 데이터 처리")
    print("3. 대규모 문의 데이터 일괄 처리")
    print("4. 여러 테이블 통합 처리")
    print("5. 한국어 AI 분류 테스트")
    print("6. 📊 종합 문의 분석 (subject+content+answer)")
    print("7. 🏷️ 질문-답변 태깅 시스템")
    print("8. ⚡ 최적화된 JSON 분석")
    print("9. 🔍 개별 QA 쌍 유사도 분석 (NEW)")
    print("0. 종료")

    # 사용자 입력 받기
    try:
        choice = int(input("\n선택 (0-9): "))
    except ValueError:
        print("숫자를 입력하세요.")
        return 1

    # 선택에 따른 처리
    if choice == 1:
        print("\n[기본 문의 데이터 조회]")
        get_personal_inquiries_summary(SUPABASE_URL, SUPABASE_KEY)
    elif choice == 2:
        print("\n[비동기 문의 데이터 처리]")
        run_async_service()
    elif choice == 3:
        print("\n[대규모 문의 데이터 일괄 처리]")
        return run_bulk_processing()
    elif choice == 4:
        print("\n[여러 테이블 통합 처리]")
        return run_multi_table_processing()
    elif choice == 5:
        print("\n[한국어 AI 분류 테스트]")
        return run_korean_ai_test()
    elif choice == 6:
        print("\n[📊 종합 문의 분석]")
        return run_comprehensive_analysis()
    elif choice == 7:
        print("\n[🏷️ 질문-답변 태깅 시스템]")
        return run_question_answer_tagging()
    elif choice == 8:
        print("\n[⚡ 최적화된 JSON 분석]")
        return run_optimized_json_analysis()
    elif choice == 9:
        print("\n[🔍 개별 QA 쌍 유사도 분석]")
        return run_qa_pair_analysis()
    elif choice == 0:
        print("프로그램을 종료합니다.")
        return 0
    else:
        print("올바른 메뉴를 선택하세요 (0-9).")
        return 1

    return 0


if __name__ == "__main__":
    sys.exit(main())