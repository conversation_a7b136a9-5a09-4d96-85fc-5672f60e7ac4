"""
QA Analysis System v2.2 - 모듈화 완료 버전
한국어 이커머스 문의 분석 시스템

이 시스템은 한국어 이커머스 문의와 답변의 품질을 분석하는 LLM 기반 시스템입니다.
주요 기능:
- 개별 QA 분석
- 배치 QA 분석
- 프로필 관리
- 시스템 정보 조회
- 모델 테스트
"""
import sys
import asyncio
from pathlib import Path

# 프로젝트 루트를 Python 경로에 추가
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from app.config.manager import ConfigManager
from app.ui.console import display_welcome, display_main_menu, get_user_choice, print_info
from app.handlers.analysis_handlers import (
    handle_single_analysis, handle_batch_analysis, handle_profile_management,
    handle_system_info, handle_model_test, handle_model_comparison,
    handle_classification_system, handle_model_management,
    handle_hybrid_classification, handle_result_management
)


async def main() -> int:
    """메인 함수"""
    display_welcome()

    # 설정 초기화
    config_manager = ConfigManager()
    config_manager.validate_current_config()

    while True:
        display_main_menu()
        choice = get_user_choice("선택 (0-10): ", range(0, 11))

        try:
            if choice == 1:
                await handle_single_analysis()
            elif choice == 2:
                await handle_batch_analysis()
            elif choice == 3:
                await handle_model_test()
            elif choice == 4:
                await handle_model_comparison()
            elif choice == 5:
                await handle_classification_system()
            elif choice == 6:
                await handle_hybrid_classification()
            elif choice == 7:
                handle_model_management()
            elif choice == 8:
                await handle_result_management()
            elif choice == 9:
                handle_profile_management()
            elif choice == 10:
                handle_system_info()
            elif choice == 0:
                print_info("프로그램을 종료합니다.")
                return 0
        except KeyboardInterrupt:
            print_info("\n프로그램을 종료합니다.")
            return 0
        except Exception as e:
            print(f"❌ 오류가 발생했습니다: {e}")

    return 0


if __name__ == "__main__":
    sys.exit(asyncio.run(main()))