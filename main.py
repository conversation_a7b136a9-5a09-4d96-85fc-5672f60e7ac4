"""
QA Analysis System v2.2 - 모듈화 완료 버전
한국어 이커머스 문의 분석 시스템

이 시스템은 한국어 이커머스 문의와 답변의 품질을 분석하는 LLM 기반 시스템입니다.
주요 기능:
- 개별 QA 분석
- 배치 QA 분석
- 프로필 관리
- 시스템 정보 조회
- 모델 테스트
"""
import sys
import asyncio
from pathlib import Path

# 프로젝트 루트를 Python 경로에 추가
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from app.config.manager import ConfigManager
from app.ui.console import display_welcome, display_main_menu, get_user_choice, print_info
from app.handlers.analysis_handlers import (
    handle_single_analysis, handle_batch_analysis, handle_profile_management,
    handle_system_info, handle_model_test, handle_model_comparison,
    handle_classification_system, handle_model_management,
    handle_hybrid_classification, handle_result_management
)


async def main() -> int:
    """메인 함수"""
    display_welcome()

    # 설정 초기화
    config_manager = ConfigManager()
    config_manager.validate_current_config()

    while True:
        display_main_menu()
        choice = get_user_choice("선택 (0-10): ", range(0, 11))

        try:
            if choice == 1:
                await handle_single_analysis()
            elif choice == 2:
                await handle_batch_analysis()
            elif choice == 3:
                await handle_model_test()
            elif choice == 4:
                await handle_model_comparison()
            elif choice == 5:
                await handle_classification_system()
            elif choice == 6:
                await handle_hybrid_classification()
            elif choice == 7:
                handle_model_management()
            elif choice == 8:
                await handle_result_management()
            elif choice == 9:
                handle_profile_management()
            elif choice == 10:
                handle_system_info()
            elif choice == 0:
                print_info("프로그램을 종료합니다.")
                return 0
        except KeyboardInterrupt:
            print_info("\n프로그램을 종료합니다.")
            return 0
        except Exception as e:
            print(f"❌ 오류가 발생했습니다: {e}")

    return 0


async def handle_classification_system():
    """통합 분류 시스템"""
    print("\n🏷️ 통합 분류 시스템")
    print("=" * 40)

    print("분류 방식을 선택하세요:")
    print("1. 단일 문의 분류")
    print("2. 배치 분류 (DB에서)")

    try:
        choice = int(input("선택 (1-2): "))
    except ValueError:
        choice = 1

    # 서비스 초기화
    from app.services.service_factory import ServiceFactory
    from app.services.unified_classification import UnifiedClassificationSystem

    service_factory = ServiceFactory()
    classification_system = UnifiedClassificationSystem(
        service_factory.get_embedding_model()
    )

    if choice == 1:
        # 단일 문의 분류
        question = input("분류할 문의를 입력하세요: ").strip()
        if not question:
            print("❌ 문의 내용이 필요합니다.")
            return

        print("\n🔍 분류 중...")
        result = await classification_system.classify_inquiry(question)
        print_classification_result(result)

    else:
        # DB에서 배치 분류
        try:
            limit = int(input("분류할 문의 수 (기본: 10): ") or "10")
        except ValueError:
            limit = 10

        print(f"\n📊 {limit}건 분류 중...")
        # 샘플 데이터로 테스트
        sample_data = [
            {"id": f"test_{i}", "inquiry_content": f"테스트 문의 {i}", "answer_content": f"테스트 답변 {i}"}
            for i in range(1, limit + 1)
        ]

        results = await classification_system.classify_batch(sample_data)
        summary = classification_system.generate_classification_summary(results)
        print_classification_summary(summary, results)


async def handle_hybrid_pipeline():
    """하이브리드 분류 파이프라인"""
    print("\n🔄 하이브리드 분류 파이프라인")
    print("=" * 40)

    print("파이프라인 모드를 선택하세요:")
    print("1. 고정밀도 모드 (느림, 정확)")
    print("2. 균형 모드 (보통)")
    print("3. 고속 모드 (빠름, 기본)")

    try:
        mode_choice = int(input("선택 (1-3): "))
    except ValueError:
        mode_choice = 3

    # 모드 매핑
    mode_mapping = {1: "high_accuracy", 2: "balanced", 3: "high_speed"}
    scenario = mode_mapping.get(mode_choice, "high_speed")

    # 서비스 초기화
    from app.services.service_factory import ServiceFactory
    from app.services.hybrid_pipeline import HybridClassificationPipeline

    service_factory = ServiceFactory()
    pipeline = HybridClassificationPipeline(service_factory)
    pipeline.apply_scenario_config(scenario)

    print(f"✅ {scenario} 모드로 설정되었습니다.")

    # 단일 분류 테스트
    text = input("분류할 텍스트를 입력하세요: ").strip()
    if not text:
        print("❌ 텍스트가 필요합니다.")
        return

    print("\n🔍 하이브리드 분류 중...")
    result = await pipeline.classify_single(text, use_hybrid=True)
    print_hybrid_result(result)


def handle_model_management():
    """모델 관리"""
    print("\n🤖 모델 관리")
    print("=" * 40)
    print("🔄 모델 관리 기능은 개발 중입니다.")


def handle_result_management():
    """결과 관리"""
    print("\n📁 결과 관리")
    print("=" * 40)
    print("🔄 결과 관리 기능은 개발 중입니다.")


def print_classification_result(result: Dict[str, Any]):
    """분류 결과 출력"""
    print("\n" + "=" * 60)
    print("🏷️ 분류 결과")
    print("=" * 60)

    classification = result.get("classification", {})
    sentiment = result.get("sentiment_analysis", {})
    urgency = result.get("urgency_analysis", {})

    print(f"📂 카테고리: {classification.get('category', 'N/A')} (신뢰도: {classification.get('confidence', 0):.3f})")
    print(f"😊 감정: {sentiment.get('sentiment', 'N/A')} (점수: {sentiment.get('score', 0):.3f})")
    print(f"⚡ 긴급도: {urgency.get('urgency', 'N/A')} (점수: {urgency.get('score', 0):.3f})")
    print(f"🎯 우선순위: {result.get('priority_level', 'N/A')} (점수: {result.get('priority_score', 0):.3f})")

    routing = result.get("routing_recommendation", {})
    if routing:
        print(f"🏢 추천 부서: {routing.get('department', 'N/A')}")

    action_items = result.get("action_items", [])
    if action_items:
        print(f"\n📋 액션 아이템:")
        for item in action_items:
            print(f"  • {item}")

    print(f"\n⏱️ 처리 시간: {result.get('processing_time_ms', 0):.2f}ms")
    print("=" * 60)


def print_classification_summary(summary: Dict[str, Any], results: List[Dict[str, Any]]):
    """분류 요약 출력"""
    print("\n" + "=" * 60)
    print("📊 배치 분류 요약")
    print("=" * 60)

    print(f"📋 총 문의: {summary.get('total_inquiries', 0)}건")

    # 카테고리 분포
    category_dist = summary.get('category_distribution', {})
    if category_dist:
        print(f"\n📂 카테고리 분포:")
        for category, count in category_dist.items():
            percentage = (count / summary['total_inquiries']) * 100
            print(f"  • {category}: {count}건 ({percentage:.1f}%)")

    # 감정 분포
    sentiment_dist = summary.get('sentiment_distribution', {})
    if sentiment_dist:
        print(f"\n😊 감정 분포:")
        for sentiment, count in sentiment_dist.items():
            percentage = (count / summary['total_inquiries']) * 100
            print(f"  • {sentiment}: {count}건 ({percentage:.1f}%)")

    # 인사이트
    insights = summary.get('insights', [])
    if insights:
        print(f"\n💡 인사이트:")
        for insight in insights:
            print(f"  • {insight}")

    print("=" * 60)


def print_hybrid_result(result: Dict[str, Any]):
    """하이브리드 결과 출력"""
    print("\n" + "=" * 60)
    print("🔄 하이브리드 분류 결과")
    print("=" * 60)

    print(f"📂 카테고리: {result.get('category', 'N/A')}")
    print(f"🎯 신뢰도: {result.get('confidence', 0):.3f}")
    print(f"🔄 파이프라인 단계: {result.get('pipeline_stage', 'N/A')}")
    print(f"⏱️ 처리 시간: {result.get('processing_time_ms', 0):.2f}ms")

    if "error" in result:
        print(f"❌ 오류: {result['error']}")

    print("=" * 60)


if __name__ == "__main__":
    sys.exit(asyncio.run(main()))