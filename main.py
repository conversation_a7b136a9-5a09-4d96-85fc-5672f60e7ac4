"""
QA Analysis System v2.2 - 간소화된 메인 파일
한국어 이커머스 문의 분석 시스템
"""
import sys
import asyncio
from pathlib import Path
from typing import Dict, List, Any

# 프로젝트 루트를 Python 경로에 추가
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from app.agents.qa_agent import QAAnalysisAgent
from app.config.manager import ConfigManager
from app.core.domain import AnalysisRequest


@staticmethod
def display_welcome():
    """환영 메시지"""
    print("=" * 80)
    print("🚀 QA Analysis System v2.2")
    print("=" * 80)
    print("📊 한국어 이커머스 문의 분석 시스템")
    print("🤖 4개 한국어 특화 모델 지원")
    print("⚡ 완전 모듈화 구조")
    print("📁 LLM 개발 표준 구조")
    print("=" * 80)


@staticmethod
def display_main_menu():
    """메인 메뉴"""
    print("\n🎯 사용할 기능을 선택하세요:")
    print("-" * 60)
    print("📊 기본 분석:")
    print("  1. 📊 개별 QA 분석")
    print("  2. 📈 배치 QA 분석")
    print("  3. 🧪 모델 테스트")
    print()
    print("🔍 고급 분석:")
    print("  4. 🔍 모델 성능 비교")
    print("  5. 🏷️ 통합 분류 시스템")
    print("  6. 🔄 하이브리드 분류 파이프라인")
    print()
    print("⚙️ 관리:")
    print("  7. 🤖 모델 관리")
    print("  8. 📁 결과 관리")
    print("  9. 🔧 프로필 관리")
    print("  10. 📋 시스템 정보")
    print()
    print("  0. 🚪 종료")
    print("-" * 60)


async def handle_single_analysis():
    """개별 QA 분석"""
    print("\n📊 개별 QA 분석")
    print("=" * 40)

    question = input("질문을 입력하세요: ").strip()
    answer = input("답변을 입력하세요: ").strip()

    if not question:
        print("❌ 질문이 필요합니다.")
        return

    try:
        from config.config import SUPABASE_URL, SUPABASE_KEY
        agent = QAAnalysisAgent(SUPABASE_URL, SUPABASE_KEY)
    except Exception:
        agent = QAAnalysisAgent()

    request = AnalysisRequest(
        question_text=question,
        answer_text=answer
    )

    print("\n🔍 분석 중...")
    result = await agent.analyze_single_qa(request)
    print_analysis_result(result)


async def handle_batch_analysis():
    """배치 QA 분석 - 실제 DB 데이터 사용"""
    print("\n📈 배치 QA 분석")
    print("=" * 40)

    print("분석 옵션을 선택하세요:")
    print("1. 최신 문의 분석")
    print("2. 랜덤 샘플 분석")
    print("3. 특정 기간 분석")

    try:
        option = int(input("선택 (1-3): ") or "1")
    except ValueError:
        option = 1

    try:
        limit = int(input("분석할 문의 수 (기본: 20, 최대: 200): ") or "20")
        limit = min(max(1, limit), 200)
    except ValueError:
        limit = 20

    try:
        from config.config import SUPABASE_URL, SUPABASE_KEY
        agent = QAAnalysisAgent(SUPABASE_URL, SUPABASE_KEY)
    except Exception:
        print("⚠️ DB 설정을 찾을 수 없습니다.")
        return

    print(f"\n📊 옵션 {option}으로 {limit}건의 문의 배치 분석 시작...")
    batch_result = await agent.analyze_sample_data(limit)

    print_batch_summary(batch_result)

    # 결과 저장 여부 확인
    save_choice = input("\n💾 결과를 파일로 저장하시겠습니까? (y/N): ").strip().lower()
    if save_choice in ['y', 'yes']:
        agent.export_results(batch_result)
        print("✅ 결과가 저장되었습니다.")

    # 상세 분석 보기
    detail_choice = input("\n🔍 상세 분석 결과를 보시겠습니까? (y/N): ").strip().lower()
    if detail_choice in ['y', 'yes']:
        print_detailed_batch_results(batch_result)


async def handle_classification_system():
    """통합 분류 시스템"""
    print("\n🏷️ 통합 분류 시스템")
    print("=" * 40)

    question = input("분류할 문의를 입력하세요: ").strip()
    if not question:
        print("❌ 문의 내용이 필요합니다.")
        return

    try:
        from app.services.simple_factory import ServiceFactory
        from app.services.unified_classification import UnifiedClassificationSystem

        service_factory = ServiceFactory()
        classification_system = UnifiedClassificationSystem(
            service_factory.get_embedding_model()
        )

        print("\n🔍 분류 중...")
        result = await classification_system.classify_inquiry(question)
        print_classification_result(result)

    except Exception as e:
        print(f"❌ 분류 실패: {e}")


async def handle_model_comparison():
    """모델 성능 비교"""
    print("\n🔍 모델 성능 비교")
    print("=" * 40)

    # 확장된 모델 목록 - 요청하신 모델들 추가
    models_to_compare = [
        # 기존 모델들
        "snunlp/KR-SBERT-V40K-klueNLI-augSTS",
        "xlm-roberta-base",
        "beomi/KcELECTRA-base",
        "monologg/koelectra-base-v3-discriminator",

        # 새로 추가된 모델들
        "xlm-roberta-large",
        "bert-base-multilingual-cased",  # mBERT-base
        "klue/bert-base",
        "klue/roberta-small",
        "klue/roberta-base",
        "klue/roberta-large"
    ]

    print(f"📊 {len(models_to_compare)}개 모델 성능 비교:")
    for i, model in enumerate(models_to_compare, 1):
        print(f"  {i}. {model}")

    # 실제 DB에서 데이터 가져오기
    print("\n📊 실제 DB에서 데이터 로딩 중...")

    try:
        from config.config import SUPABASE_URL, SUPABASE_KEY
        from app.agents.qa_agent import QAAnalysisAgent

        agent = QAAnalysisAgent(SUPABASE_URL, SUPABASE_KEY)

        # DB에서 샘플 데이터 가져오기
        sample_size = int(input("비교할 데이터 수 (기본: 10, 최대: 50): ") or "10")
        sample_size = min(max(sample_size, 5), 50)  # 5~50 제한

        print(f"📊 DB에서 {sample_size}개 데이터 로딩 중...")
        test_data = await agent.get_sample_data(sample_size)

        if not test_data:
            print("⚠️ DB에서 데이터를 가져올 수 없습니다. 샘플 데이터를 사용합니다.")
            test_data = [
                {"inquiry_content": "배송이 너무 늦어요. 언제 도착하나요?"},
                {"inquiry_content": "상품이 설명과 달라요. 환불 가능한가요?"},
                {"inquiry_content": "결제가 안되는데 어떻게 해야 하나요?"}
            ]

        test_questions = [item["inquiry_content"] for item in test_data[:sample_size]]
        print(f"\n🧪 실제 DB 데이터 {len(test_questions)}개로 모델 비교 시작...")

    except Exception as e:
        print(f"⚠️ DB 연결 실패: {e}")
        print("📊 샘플 데이터로 진행합니다...")
        test_questions = [
            "배송이 너무 늦어요. 언제 도착하나요?",
            "상품이 설명과 달라요. 환불 가능한가요?",
            "결제가 안되는데 어떻게 해야 하나요?"
        ]

    try:
        from app.services.simple_factory import ServiceFactory
        from app.services.unified_classification import UnifiedClassificationSystem
        import time

        results = {}

        for model_name in models_to_compare:
            print(f"\n📊 {model_name} 테스트 중...")

            try:
                service_factory = ServiceFactory()
                embedding_model = service_factory.get_embedding_model(model_name)
                classifier = UnifiedClassificationSystem(embedding_model)

                model_results = []
                total_time = 0

                for question in test_questions:
                    start_time = time.time()
                    result = await classifier.classify_inquiry(question)
                    processing_time = (time.time() - start_time) * 1000
                    total_time += processing_time

                    model_results.append({
                        "question": question,
                        "category": result.get("classification", {}).get("category", "기타"),
                        "confidence": result.get("classification", {}).get("confidence", 0.0),
                        "processing_time": processing_time
                    })

                results[model_name] = {
                    "results": model_results,
                    "avg_processing_time": total_time / len(test_questions),
                    "avg_confidence": sum(r["confidence"] for r in model_results) / len(model_results)
                }

                print(f"  ✅ 완료 - 평균 처리시간: {results[model_name]['avg_processing_time']:.2f}ms")

            except Exception as e:
                print(f"  ❌ 실패: {e}")
                results[model_name] = {"error": str(e)}

        # 결과 출력
        print_model_comparison_results(results, test_questions)

    except Exception as e:
        print(f"❌ 모델 비교 실패: {e}")


async def handle_hybrid_pipeline():
    """하이브리드 분류 파이프라인"""
    print("\n🔄 하이브리드 분류 파이프라인")
    print("=" * 40)

    text = input("분류할 텍스트를 입력하세요: ").strip()
    if not text:
        print("❌ 텍스트가 필요합니다.")
        return

    try:
        from app.services.simple_factory import ServiceFactory
        from app.services.hybrid_pipeline import HybridClassificationPipeline

        service_factory = ServiceFactory()
        pipeline = HybridClassificationPipeline(service_factory)

        print("\n🔍 하이브리드 분류 중...")
        result = await pipeline.classify_single(text, use_hybrid=True)
        print_hybrid_result(result)

    except Exception as e:
        print(f"❌ 하이브리드 분류 실패: {e}")


def handle_profile_management():
    """프로필 관리"""
    config_manager = ConfigManager()

    while True:
        print("\n🔧 프로필 관리")
        print("=" * 40)
        print("1. 현재 프로필 보기")
        print("2. 프로필 변경")
        print("3. 모든 프로필 목록")
        print("0. 돌아가기")

        try:
            choice = int(input("\n선택 (0-3): "))
        except ValueError:
            continue

        if choice == 1:
            config_manager.print_current_config()
        elif choice == 2:
            profiles = config_manager.get_available_profiles()
            current = config_manager.get_current_profile()

            print("\n📋 사용 가능한 프로필:")
            for i, profile in enumerate(profiles, 1):
                marker = "✅" if profile == current else "  "
                info = config_manager.get_profile_info(profile)
                print(f"{marker} {i}. {profile}")
                print(f"     {info.get('description', '')}")

            try:
                choice = int(input(f"\n변경할 프로필 번호 (1-{len(profiles)}): "))
                if 1 <= choice <= len(profiles):
                    new_profile = profiles[choice - 1]
                    config_manager.set_profile(new_profile)
            except ValueError:
                print("올바른 번호를 입력하세요.")
        elif choice == 3:
            config_manager.list_all_profiles()
        elif choice == 0:
            break


def handle_model_management():
    """모델 관리"""
    print("\n🤖 모델 관리")
    print("=" * 40)

    while True:
        print("\n모델 관리 메뉴:")
        print("1. 현재 모델 확인")
        print("2. 사용 가능한 모델 목록")
        print("3. 모델 성능 정보")
        print("4. 모델 캐시 초기화")
        print("0. 돌아가기")

        try:
            choice = int(input("\n선택 (0-4): "))
        except ValueError:
            print("올바른 번호를 입력하세요.")
            continue

        if choice == 1:
            # 현재 모델 확인
            try:
                from app.services.simple_factory import ServiceFactory
                factory = ServiceFactory()
                config_manager = factory.get_config_manager()

                profile_info = config_manager.get_profile_info()
                print("\n📋 현재 모델 설정:")
                print(f"  • 프로필: {config_manager.get_current_profile()}")
                print(f"  • 임베딩 모델: {profile_info.get('embedding_model', 'N/A')}")
                print(f"  • LLM 모델: {profile_info.get('llm_model', 'N/A')}")
                print(f"  • 분류 모델: {profile_info.get('classification_model', 'N/A')}")

                service_info = factory.get_service_info()
                print("\n🔧 서비스 상태:")
                print(f"  • 캐시된 서비스: {len(service_info['cached_services'])}개")
                print(f"  • 팩토리 초기화: {service_info['factory_initialized']}")

            except Exception as e:
                print(f"❌ 모델 정보 조회 실패: {e}")

        elif choice == 2:
            # 사용 가능한 모델 목록
            print("\n📋 사용 가능한 모델:")
            print("\n🔤 임베딩/분류 모델 (확장된 목록):")
            embedding_models = [
                ("xlm-roberta-large", "🥇 최고 정확도 (대형)"),
                ("xlm-roberta-base", "🥈 높은 정확도 (기본)"),
                ("klue/roberta-large", "🥉 한국어 대형 모델"),
                ("klue/roberta-base", "🏃 한국어 기본 모델"),
                ("klue/roberta-small", "⚡ 한국어 경량 모델"),
                ("klue/bert-base", "📚 한국어 BERT"),
                ("beomi/KcELECTRA-base", "🔥 한국어 ELECTRA"),
                ("monologg/koelectra-base-v3-discriminator", "🚀 고속 처리"),
                ("bert-base-multilingual-cased", "🌍 다국어 BERT"),
                ("snunlp/KR-SBERT-V40K-klueNLI-augSTS", "🇰� 한국어 특화")
            ]
            for i, (model, desc) in enumerate(embedding_models, 1):
                print(f"  {i:2d}. {model} - {desc}")

            print("\n🧠 LLM 모델:")
            llm_models = [
                ("gpt-4.1-mini", "🎯 최적 성능/비용")
            ]
            for i, (model, desc) in enumerate(llm_models, 1):
                print(f"  {i}. {model} - {desc}")

            print("\n💰 LLM 비용 정보 (GPT-4.1-mini):")
            print("  • 입력: $0.40/1M 토큰 (₩560/1M 토큰)")
            print("  • 캐시된 입력: $0.10/1M 토큰 (₩140/1M 토큰)")
            print("  • 출력: $1.60/1M 토큰 (₩2,240/1M 토큰)")
            print("  • 환율: 1달러 = 1,400원")

            print("\n🔄 현재 조합:")
            print("  • 기본 임베딩: xlm-roberta-base (최고 정확도)")
            print("  • 분류 모델: beomi/KcELECTRA-base (균형 성능)")
            print("  • LLM: gpt-4.1-mini (최적 비용)")
            print("  • 하이브리드: 상황별 최적 모델 자동 선택")

        elif choice == 3:
            # 모델 성능 정보
            print("\n📊 모델 성능 정보:")
            print("\n🔤 임베딩/분류 모델 성능 (통합):")
            print("  • xlm-roberta-base: 🥇 최고 정확도, 다국어 지원")
            print("  • beomi/KcELECTRA-base: 🥈 한국어 특화, 균형 성능")
            print("  • monologg/koelectra-v3: 🥉 고속 처리, 경량화")
            print("  • snunlp/KR-SBERT: 🏃 한국어 특화, 빠른 속도")

            print("\n🧠 LLM 모델 성능:")
            print("  • gpt-4.1-mini: 🎯 최적 성능/비용, 높은 정확도")

            print("\n⚠️ 중요 사항:")
            print("  • 임베딩과 분류는 동일한 모델 사용 (일관성 보장)")
            print("  • 하이브리드 파이프라인에서 상황별 최적 모델 자동 선택")
            print("  • LLM은 2차 분류 및 고급 분석에만 사용")

        elif choice == 4:
            # 모델 캐시 초기화
            try:
                from app.services.simple_factory import ServiceFactory
                factory = ServiceFactory()
                factory.clear_cache()
                print("✅ 모델 캐시가 초기화되었습니다.")
            except Exception as e:
                print(f"❌ 캐시 초기화 실패: {e}")

        elif choice == 0:
            break
        else:
            print("올바른 메뉴를 선택하세요 (0-4).")


def handle_result_management():
    """결과 관리"""
    print("\n📁 결과 관리")
    print("=" * 40)

    while True:
        print("\n결과 관리 메뉴:")
        print("1. 저장된 결과 목록")
        print("2. 결과 검색")
        print("3. 결과 삭제")
        print("4. 저장소 정보")
        print("0. 돌아가기")

        try:
            choice = int(input("\n선택 (0-4): "))
        except ValueError:
            print("올바른 번호를 입력하세요.")
            continue

        if choice == 1:
            # 저장된 결과 목록
            import os
            outputs_dir = "outputs"
            if os.path.exists(outputs_dir):
                files = [f for f in os.listdir(outputs_dir) if f.endswith('.json')]
                if files:
                    print(f"\n📋 저장된 결과 파일 ({len(files)}개):")
                    for i, file in enumerate(files[-10:], 1):  # 최근 10개만
                        print(f"  {i}. {file}")
                else:
                    print("📭 저장된 결과가 없습니다.")
            else:
                print("📁 outputs 폴더가 없습니다.")

        elif choice == 2:
            # 결과 검색
            search_term = input("검색어를 입력하세요: ").strip()
            if search_term:
                print(f"🔍 '{search_term}' 검색 기능은 개발 중입니다.")
            else:
                print("검색어를 입력해주세요.")

        elif choice == 3:
            # 결과 삭제
            filename = input("삭제할 파일명을 입력하세요: ").strip()
            if filename:
                import os
                filepath = os.path.join("outputs", filename)
                if os.path.exists(filepath):
                    try:
                        os.remove(filepath)
                        print(f"✅ {filename} 삭제 완료")
                    except Exception as e:
                        print(f"❌ 삭제 실패: {e}")
                else:
                    print(f"❌ 파일을 찾을 수 없습니다: {filename}")

        elif choice == 4:
            # 저장소 정보
            import os
            outputs_dir = "outputs"
            if os.path.exists(outputs_dir):
                files = os.listdir(outputs_dir)
                total_files = len(files)
                json_files = len([f for f in files if f.endswith('.json')])

                print("\n📁 저장소 정보:")
                print(f"  • 경로: {os.path.abspath(outputs_dir)}")
                print(f"  • 전체 파일: {total_files}개")
                print(f"  • JSON 결과: {json_files}개")

                # 디스크 사용량
                total_size = sum(os.path.getsize(os.path.join(outputs_dir, f))
                               for f in files if os.path.isfile(os.path.join(outputs_dir, f)))
                print(f"  • 사용 용량: {total_size / 1024:.1f} KB")
            else:
                print("📁 outputs 폴더가 없습니다.")

        elif choice == 0:
            break
        else:
            print("올바른 메뉴를 선택하세요 (0-4).")


def handle_system_info():
    """시스템 정보"""
    config_manager = ConfigManager()

    print("\n📋 시스템 정보")
    print("=" * 40)
    print("🚀 QA Analysis System v2.2")
    print("🏗️ LLM 개발 표준 구조")
    print("🇰🇷 한국어 특화 모델 지원")
    print("📁 간결한 폴더 구조")

    profile_info = config_manager.get_profile_info()
    print("\n⚙️ 현재 설정:")
    print(f"  • 프로필: {config_manager.get_current_profile()}")
    print(f"  • 설명: {profile_info.get('description', '')}")
    print(f"  • 임베딩: {profile_info.get('embedding_model', '')}")
    print(f"  • LLM: {profile_info.get('llm_model', '')}")

    print("\n📁 폴더 구조:")
    print("  • app/: 메인 애플리케이션")
    print("  • data/: 데이터 관리")
    print("  • outputs/: 결과 저장")
    print("  • tests/: 테스트 코드")
    print("  • archive/: 기존 파일 백업")


@staticmethod
def print_analysis_result(result):
    """분석 결과 출력"""
    print("\n" + "=" * 60)
    print("📊 QA 분석 결과")
    print("=" * 60)

    print(f"🆔 문의 ID: {result.id}")
    print(f"❓ 질문: {result.question[:100]}...")
    print(f"💬 답변: {result.answer[:100]}..." if result.answer else "💬 답변: (없음)")

    print("\n📈 분석 점수:")
    print(f"  • 의미적 유사도: {result.semantic_similarity:.3f}")
    print(f"  • 주제 관련성: {result.topic_relevance:.3f}")
    print(f"  • 키워드 겹침: {result.keyword_overlap:.3f}")
    print(f"  • 답변 완성도: {result.answer_completeness:.3f}")
    print(f"  • 답변 정확성: {result.answer_accuracy:.3f}")
    print(f"  • 답변 도움도: {result.answer_helpfulness:.3f}")

    print("\n🎯 종합 결과:")
    print(f"  • 전체 점수: {result.overall_score:.3f}")
    print(f"  • 등급: {result.grade.value}")
    print(f"  • 통과 여부: {'✅ 통과' if result.pass_threshold else '❌ 미통과'}")

    if result.strengths:
        print("\n💪 강점:")
        for strength in result.strengths:
            print(f"  • {strength}")

    if result.weaknesses:
        print("\n⚠️ 약점:")
        for weakness in result.weaknesses:
            print(f"  • {weakness}")

    if result.recommendations:
        print("\n💡 개선사항:")
        for rec in result.recommendations:
            print(f"  • {rec}")

    print(f"\n⏱️ 처리 시간: {result.processing_time_ms:.2f}ms")
    print("=" * 60)


def print_batch_summary(batch_result):
    """배치 분석 결과 요약"""
    print("\n" + "=" * 60)
    print("📊 배치 분석 결과 요약")
    print("=" * 60)
    print(f"📈 총 분석: {batch_result.total_count}건")
    print(f"✅ 통과: {batch_result.pass_count}건 ({batch_result.pass_count/batch_result.total_count*100:.1f}%)")
    print(f"📊 평균 점수: {batch_result.average_score:.3f}")

    print("\n🏆 등급 분포:")
    for grade in ['A', 'B', 'C', 'D', 'F']:
        count = batch_result.grade_distribution.get(grade, 0)
        if count > 0:
            print(f"  • {grade}등급: {count}건")

    print(f"\n⏱️ 처리 시간: {batch_result.processing_time_seconds:.2f}초")
    print("=" * 60)


def print_detailed_batch_results(batch_result):
    """상세 배치 분석 결과"""
    print("\n" + "=" * 80)
    print("🔍 상세 배치 분석 결과")
    print("=" * 80)

    for i, result in enumerate(batch_result.results[:10], 1):  # 최대 10개만 표시
        print(f"\n📋 {i}. 문의 ID: {result.id}")
        print(f"❓ 질문: {result.question[:100]}...")
        print(f"💬 답변: {result.answer[:100]}..." if result.answer else "💬 답변: (없음)")
        print(f"📊 점수: {result.overall_score:.3f} | 등급: {result.grade.value} | 통과: {'✅' if result.pass_threshold else '❌'}")

        if result.weaknesses:
            print(f"⚠️ 약점: {', '.join(result.weaknesses[:3])}")
        if result.recommendations:
            print(f"💡 개선사항: {', '.join(result.recommendations[:2])}")
        print("-" * 60)

    if len(batch_result.results) > 10:
        print(f"\n... 및 {len(batch_result.results) - 10}개 추가 결과")

    print("=" * 80)


@staticmethod
def print_classification_result(result: Dict[str, Any]):
    """분류 결과 출력"""
    print("\n" + "=" * 60)
    print("🏷️ 분류 결과")
    print("=" * 60)

    classification = result.get("classification", {})
    sentiment = result.get("sentiment_analysis", {})
    urgency = result.get("urgency_analysis", {})

    print(f"📂 카테고리: {classification.get('category', 'N/A')} "
          f"(신뢰도: {classification.get('confidence', 0):.3f})")
    print(f"😊 감정: {sentiment.get('sentiment', 'N/A')} "
          f"(점수: {sentiment.get('score', 0):.3f})")
    print(f"⚡ 긴급도: {urgency.get('urgency', 'N/A')} "
          f"(점수: {urgency.get('score', 0):.3f})")
    print(f"🎯 우선순위: {result.get('priority_level', 'N/A')} "
          f"(점수: {result.get('priority_score', 0):.3f})")

    routing = result.get("routing_recommendation", {})
    if routing:
        print(f"🏢 추천 부서: {routing.get('department', 'N/A')}")

    action_items = result.get("action_items", [])
    if action_items:
        print("\n📋 액션 아이템:")
        for item in action_items:
            print(f"  • {item}")

    print(f"\n⏱️ 처리 시간: {result.get('processing_time_ms', 0):.2f}ms")
    print("=" * 60)


@staticmethod
def print_model_comparison_results(results: Dict[str, Any], test_questions: List[str]):
    """모델 비교 결과 출력"""
    print("\n" + "=" * 80)
    print("📊 모델 성능 비교 결과")
    print("=" * 80)

    # 성능 순위 계산
    valid_results = {k: v for k, v in results.items() if "error" not in v}

    if not valid_results:
        print("❌ 모든 모델에서 오류가 발생했습니다.")
        return

    # 평균 신뢰도 순위
    confidence_ranking = sorted(
        valid_results.items(),
        key=lambda x: x[1]["avg_confidence"],
        reverse=True
    )

    # 평균 처리 시간 순위 (낮을수록 좋음)
    speed_ranking = sorted(
        valid_results.items(),
        key=lambda x: x[1]["avg_processing_time"]
    )

    print("🏆 성능 순위:")
    print(f"  🎯 신뢰도 최고: {confidence_ranking[0][0]} ({confidence_ranking[0][1]['avg_confidence']:.3f})")
    print(f"  ⚡ 속도 최고: {speed_ranking[0][0]} ({speed_ranking[0][1]['avg_processing_time']:.2f}ms)")

    print("\n📋 상세 결과:")
    for model_name, model_data in results.items():
        print(f"\n🤖 {model_name}")
        print("-" * 50)

        if "error" in model_data:
            print(f"  ❌ 오류: {model_data['error']}")
            continue

        print(f"  📊 평균 신뢰도: {model_data['avg_confidence']:.3f}")
        print(f"  ⏱️ 평균 처리시간: {model_data['avg_processing_time']:.2f}ms")

        print("  📝 테스트 결과:")
        for i, result in enumerate(model_data["results"], 1):
            question_short = result["question"][:30] + "..." if len(result["question"]) > 30 else result["question"]
            print(f"    {i}. {question_short}")
            print(f"       → {result['category']} (신뢰도: {result['confidence']:.3f})")

    # 추천 모델
    print("\n💡 추천:")
    if confidence_ranking[0][0] == speed_ranking[0][0]:
        print(f"  🏆 종합 최고: {confidence_ranking[0][0]} (신뢰도와 속도 모두 우수)")
    else:
        print(f"  🎯 정확도 중심: {confidence_ranking[0][0]}")
        print(f"  ⚡ 속도 중심: {speed_ranking[0][0]}")

    print("=" * 80)


@staticmethod
def print_hybrid_result(result: Dict[str, Any]):
    """하이브리드 결과 출력"""
    print("\n" + "=" * 60)
    print("🔄 하이브리드 분류 결과")
    print("=" * 60)

    print(f"📂 카테고리: {result.get('category', 'N/A')}")
    print(f"🎯 신뢰도: {result.get('confidence', 0):.3f}")
    print(f"🔄 파이프라인 단계: {result.get('pipeline_stage', 'N/A')}")
    print(f"⏱️ 처리 시간: {result.get('processing_time_ms', 0):.2f}ms")

    if "error" in result:
        print(f"❌ 오류: {result['error']}")

    print("=" * 60)


async def main():
    """메인 함수"""
    display_welcome()

    # 설정 초기화
    config_manager = ConfigManager()
    config_manager.validate_current_config()

    while True:
        display_main_menu()

        try:
            choice = int(input("선택 (0-10): "))
        except ValueError:
            print("숫자를 입력하세요.")
            continue

        try:
            if choice == 1:
                await handle_single_analysis()
            elif choice == 2:
                await handle_batch_analysis()
            elif choice == 3:
                await handle_single_analysis()  # 모델 테스트는 단일 분석과 동일
            elif choice == 4:
                await handle_model_comparison()
            elif choice == 5:
                await handle_classification_system()
            elif choice == 6:
                await handle_hybrid_pipeline()
            elif choice == 7:
                handle_model_management()
            elif choice == 8:
                handle_result_management()
            elif choice == 9:
                handle_profile_management()
            elif choice == 10:
                handle_system_info()
            elif choice == 0:
                print("👋 프로그램을 종료합니다.")
                return 0
            else:
                print("올바른 메뉴를 선택하세요 (0-10).")
        except KeyboardInterrupt:
            print("\n👋 프로그램을 종료합니다.")
            return 0
        except Exception as e:
            print(f"❌ 오류가 발생했습니다: {e}")

    return 0


if __name__ == "__main__":
    sys.exit(asyncio.run(main()))
