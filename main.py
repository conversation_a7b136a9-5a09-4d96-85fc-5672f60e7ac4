"""
QA Analysis System v2.1 - 모듈화 완료 버전
한국어 이커머스 문의 분석 시스템
"""
import sys
import asyncio
from pathlib import Path

# 프로젝트 루트를 Python 경로에 추가
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from qa_system.agents.qa_analysis_agent import QAAnalysisAgent
from qa_system.config.config_manager import ConfigManager
from qa_system.core.domain import AnalysisRequest


def display_welcome():
    """환영 메시지"""
    print("=" * 80)
    print("🚀 QA Analysis System v2.1")
    print("=" * 80)
    print("📊 한국어 이커머스 문의 분석 시스템")
    print("🤖 4개 한국어 특화 모델 지원")
    print("⚡ 완전 모듈화 구조")
    print("📁 정리된 폴더 구조")
    print("=" * 80)


def display_main_menu():
    """메인 메뉴"""
    print("\n🎯 사용할 기능을 선택하세요:")
    print("-" * 50)
    print("1. 📊 개별 QA 분석")
    print("2. 📈 배치 QA 분석")
    print("3. 🔧 프로필 관리")
    print("4. 📋 시스템 정보")
    print("5. 🧪 모델 테스트")
    print("0. 🚪 종료")
    print("-" * 50)


async def handle_single_analysis():
    """개별 QA 분석"""
    print("\n📊 개별 QA 분석")
    print("=" * 40)

    # 입력 방식 선택
    print("입력 방식을 선택하세요:")
    print("1. 문의 ID로 분석")
    print("2. 직접 텍스트 입력")

    try:
        choice = int(input("선택 (1-2): "))
    except ValueError:
        choice = 2

    # DB 연결 정보
    try:
        from config.config import SUPABASE_URL, SUPABASE_KEY
        agent = QAAnalysisAgent(SUPABASE_URL, SUPABASE_KEY)
    except:
        print("⚠️ DB 설정을 찾을 수 없습니다. 텍스트 모드로 진행합니다.")
        agent = QAAnalysisAgent()

    if choice == 1:
        inquiry_id = input("문의 ID를 입력하세요: ").strip()
        if not inquiry_id:
            print("❌ 문의 ID가 필요합니다.")
            return

        request = AnalysisRequest(inquiry_id=inquiry_id)
    else:
        question = input("질문을 입력하세요: ").strip()
        answer = input("답변을 입력하세요: ").strip()

        if not question:
            print("❌ 질문이 필요합니다.")
            return

        request = AnalysisRequest(
            question_text=question,
            answer_text=answer
        )

    print("\n🔍 분석 중...")
    result = await agent.analyze_single_qa(request)

    # 결과 출력
    print_analysis_result(result)


def print_analysis_result(result):
    """분석 결과 출력"""
    print("\n" + "=" * 60)
    print("📊 QA 분석 결과")
    print("=" * 60)

    print(f"🆔 문의 ID: {result.id}")
    print(f"❓ 질문: {result.question[:100]}...")
    print(f"💬 답변: {result.answer[:100]}..." if result.answer else "💬 답변: (없음)")

    print(f"\n📈 분석 점수:")
    print(f"  • 의미적 유사도: {result.semantic_similarity:.3f}")
    print(f"  • 주제 관련성: {result.topic_relevance:.3f}")
    print(f"  • 키워드 겹침: {result.keyword_overlap:.3f}")
    print(f"  • 답변 완성도: {result.answer_completeness:.3f}")
    print(f"  • 답변 정확성: {result.answer_accuracy:.3f}")
    print(f"  • 답변 도움도: {result.answer_helpfulness:.3f}")

    print(f"\n🎯 종합 결과:")
    print(f"  • 전체 점수: {result.overall_score:.3f}")
    print(f"  • 등급: {result.grade.value}")
    print(f"  • 통과 여부: {'✅ 통과' if result.pass_threshold else '❌ 미통과'}")

    if result.strengths:
        print(f"\n💪 강점:")
        for strength in result.strengths:
            print(f"  • {strength}")

    if result.weaknesses:
        print(f"\n⚠️ 약점:")
        for weakness in result.weaknesses:
            print(f"  • {weakness}")

    if result.recommendations:
        print(f"\n💡 개선사항:")
        for rec in result.recommendations:
            print(f"  • {rec}")

    print(f"\n⏱️ 처리 시간: {result.processing_time_ms:.2f}ms")
    print("=" * 60)


async def handle_batch_analysis():
    """배치 분석"""
    print("\n📈 배치 QA 분석")
    print("=" * 40)

    try:
        limit = int(input("분석할 문의 수를 입력하세요 (기본: 5): ") or "5")
    except ValueError:
        limit = 5

    # DB 연결 정보
    try:
        from config.config import SUPABASE_URL, SUPABASE_KEY
        agent = QAAnalysisAgent(SUPABASE_URL, SUPABASE_KEY)
    except:
        print("⚠️ DB 설정을 찾을 수 없습니다.")
        return

    print(f"\n📊 {limit}건의 문의 배치 분석 중...")

    # 샘플 데이터 분석
    batch_result = await agent.analyze_sample_data(limit)

    # 결과 요약 출력
    print_batch_summary(batch_result)

    # 결과 저장
    agent.export_results(batch_result)


def print_batch_summary(batch_result):
    """배치 분석 결과 요약"""
    print("\n" + "=" * 60)
    print("📊 배치 분석 결과 요약")
    print("=" * 60)
    print(f"📈 총 분석: {batch_result.total_count}건")
    print(f"✅ 통과: {batch_result.pass_count}건 ({batch_result.pass_count/batch_result.total_count*100:.1f}%)")
    print(f"📊 평균 점수: {batch_result.average_score:.3f}")

    print(f"\n🏆 등급 분포:")
    for grade in ['A', 'B', 'C', 'D', 'F']:
        count = batch_result.grade_distribution.get(grade, 0)
        if count > 0:
            print(f"  • {grade}등급: {count}건")

    print(f"\n⏱️ 처리 시간: {batch_result.processing_time_seconds:.2f}초")
    print("=" * 60)


def handle_profile_management():
    """프로필 관리"""
    config_manager = ConfigManager()

    while True:
        print("\n🔧 프로필 관리")
        print("=" * 40)
        print("1. 현재 프로필 보기")
        print("2. 프로필 변경")
        print("3. 모든 프로필 목록")
        print("4. 설정 검증")
        print("0. 돌아가기")

        try:
            choice = int(input("\n선택 (0-4): "))
        except ValueError:
            print("숫자를 입력하세요.")
            continue

        if choice == 1:
            config_manager.print_current_config()
        elif choice == 2:
            profiles = config_manager.get_available_profiles()
            current = config_manager.get_current_profile()

            print(f"\n📋 사용 가능한 프로필:")
            for i, profile in enumerate(profiles, 1):
                marker = "✅" if profile == current else "  "
                info = config_manager.get_profile_info(profile)
                print(f"{marker} {i}. {profile}")
                print(f"     {info.get('description', '')}")

            try:
                choice = int(input(f"\n변경할 프로필 번호 (1-{len(profiles)}): "))
                if 1 <= choice <= len(profiles):
                    new_profile = profiles[choice - 1]
                    config_manager.set_profile(new_profile)
                else:
                    print("올바른 번호를 입력하세요.")
            except ValueError:
                print("숫자를 입력하세요.")
        elif choice == 3:
            config_manager.list_all_profiles()
        elif choice == 4:
            if config_manager.validate_current_config():
                print("✅ 설정이 올바릅니다.")
            else:
                print("❌ 설정에 문제가 있습니다.")
        elif choice == 0:
            break
        else:
            print("올바른 메뉴를 선택하세요 (0-4).")


def handle_system_info():
    """시스템 정보"""
    config_manager = ConfigManager()

    print("\n📋 시스템 정보")
    print("=" * 40)
    print("🚀 QA Analysis System v2.1")
    print("🏗️ 완전 모듈화 구조")
    print("🇰🇷 한국어 특화 모델 지원")
    print("📁 정리된 폴더 구조")

    profile_info = config_manager.get_profile_info()
    print(f"\n⚙️ 현재 설정:")
    print(f"  • 프로필: {config_manager.get_current_profile()}")
    print(f"  • 설명: {profile_info.get('description', '')}")
    print(f"  • 임베딩: {profile_info.get('embedding_model', '')}")
    print(f"  • LLM: {profile_info.get('llm_model', '')}")

    print(f"\n📁 폴더 구조:")
    print(f"  • qa_system/: 메인 시스템")
    print(f"  • outputs/: 결과 저장")
    print(f"  • legacy/: 기존 파일 백업")


async def handle_model_test():
    """모델 테스트"""
    print("\n🧪 모델 테스트")
    print("=" * 40)

    question = input("테스트 질문을 입력하세요: ").strip()
    answer = input("테스트 답변을 입력하세요: ").strip()

    if not question:
        print("❌ 질문이 필요합니다.")
        return

    agent = QAAnalysisAgent()
    request = AnalysisRequest(
        question_text=question,
        answer_text=answer
    )

    print("\n🔍 테스트 중...")
    result = await agent.analyze_single_qa(request)
    print_analysis_result(result)


async def main():
    """메인 함수"""
    display_welcome()

    # 설정 초기화
    config_manager = ConfigManager()
    config_manager.validate_current_config()

    while True:
        display_main_menu()

        try:
            choice = int(input("선택 (0-5): "))
        except ValueError:
            print("숫자를 입력하세요.")
            continue

        if choice == 1:
            await handle_single_analysis()
        elif choice == 2:
            await handle_batch_analysis()
        elif choice == 3:
            handle_profile_management()
        elif choice == 4:
            handle_system_info()
        elif choice == 5:
            await handle_model_test()
        elif choice == 0:
            print("👋 프로그램을 종료합니다.")
            return 0
        else:
            print("올바른 메뉴를 선택하세요 (0-5).")

    return 0


if __name__ == "__main__":
    sys.exit(asyncio.run(main()))