#!/usr/bin/env python3
"""
문의 데이터 처리 메인 프로그램
"""
import sys

from config.config import SUPABASE_KEY, SUPABASE_URL
from src.services.async_inquiry_runner import run_async_service
from src.services.bulk_processing_service import run_bulk_processing
from src.services.inquiry_service import get_personal_inquiries_summary


def main():
    """
    메인 실행 함수 - 사용자 선택에 따라 다른 기능을 실행합니다.
    """
    print("=== 문의 데이터 처리 시스템 ===")
    
    # None 체크
    if not SUPABASE_URL or not SUPABASE_KEY:
        print("오류: Supabase URL 또는 API 키가 설정되지 않았습니다.")
        print("config/config.py 파일을 확인하세요.")
        return 1
    
    # 메뉴 표시
    print("\n사용할 기능을 선택하세요:")
    print("1. 기본 문의 데이터 조회 (동기식)")
    print("2. 비동기 문의 데이터 처리")
    print("3. 대규모 문의 데이터 일괄 처리")
    print("0. 종료")
    
    # 사용자 입력 받기
    try:
        choice = int(input("\n선택 (0-3): "))
    except ValueError:
        print("숫자를 입력하세요.")
        return 1
    
    # 선택에 따른 처리
    if choice == 1:
        print("\n[기본 문의 데이터 조회]")
        get_personal_inquiries_summary(SUPABASE_URL, SUPABASE_KEY)
    elif choice == 2:
        print("\n[비동기 문의 데이터 처리]")
        run_async_service()
    elif choice == 3:
        print("\n[대규모 문의 데이터 일괄 처리]")
        return run_bulk_processing()
    elif choice == 0:
        print("프로그램을 종료합니다.")
        return 0
    else:
        print("올바른 메뉴를 선택하세요 (0-3).")
        return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(main())