# AI 모델 설정 파일

# 기본 설정
default_profile: "korean_ecommerce"

# 모델 프로필 설정
profiles:
  # 한국어 이커머스 특화 프로필
  korean_ecommerce:
    embedding_model:
      provider: "huggingface"
      model_name: "snunlp/KR-SBERT-V40K-klueNLI-augSTS"
      model_type: "embedding"
      additional_params:
        device: "cpu"
        normalize_embeddings: true
    
    llm_model:
      provider: "openai"
      model_name: "gpt-3.5-turbo"
      model_type: "llm"
      max_tokens: 200
      temperature: 0.1
      additional_params:
        response_format: "json"
        korean_optimized: true

  # 고성능 프로필 (더 큰 모델)
  high_performance:
    embedding_model:
      provider: "huggingface"
      model_name: "sentence-transformers/paraphrase-multilingual-mpnet-base-v2"
      model_type: "embedding"
      additional_params:
        device: "cuda"
        normalize_embeddings: true
    
    llm_model:
      provider: "openai"
      model_name: "gpt-4"
      model_type: "llm"
      max_tokens: 500
      temperature: 0.05
      additional_params:
        response_format: "json"

  # 경량 프로필 (빠른 처리)
  lightweight:
    embedding_model:
      provider: "huggingface"
      model_name: "sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2"
      model_type: "embedding"
      additional_params:
        device: "cpu"
        normalize_embeddings: true
    
    llm_model:
      provider: "openai"
      model_name: "gpt-3.5-turbo"
      model_type: "llm"
      max_tokens: 100
      temperature: 0.2
      additional_params:
        response_format: "text"

  # Anthropic Claude 프로필
  anthropic_profile:
    embedding_model:
      provider: "huggingface"
      model_name: "snunlp/KR-SBERT-V40K-klueNLI-augSTS"
      model_type: "embedding"
    
    llm_model:
      provider: "anthropic"
      model_name: "claude-3-haiku-20240307"
      model_type: "llm"
      max_tokens: 300
      temperature: 0.1

  # Google 모델 프로필
  google_profile:
    embedding_model:
      provider: "google"
      model_name: "https://tfhub.dev/google/universal-sentence-encoder-multilingual/3"
      model_type: "embedding"
    
    llm_model:
      provider: "openai"  # Google LLM은 아직 미구현
      model_name: "gpt-3.5-turbo"
      model_type: "llm"
      max_tokens: 200
      temperature: 0.1

# 분석 설정
analysis_settings:
  # QA 쌍 분석 설정
  qa_pair_analysis:
    similarity_threshold: 0.6
    pass_threshold: 0.6
    grade_thresholds:
      A: 0.9
      B: 0.8
      C: 0.7
      D: 0.6
      F: 0.0
    
    # 가중치 설정
    score_weights:
      semantic_similarity: 0.25
      topic_relevance: 0.25
      keyword_overlap: 0.15
      answer_completeness: 0.15
      answer_accuracy: 0.10
      answer_helpfulness: 0.10

  # 태깅 설정
  tagging:
    max_tags_per_item: 10
    min_confidence: 0.3
    enable_auto_tagging: true
    
  # 배치 처리 설정
  batch_processing:
    max_workers: 4
    batch_size: 50
    timeout_seconds: 30
    enable_parallel: true

# 출력 설정
output_settings:
  json_format:
    ensure_ascii: false
    indent: 2
    sort_keys: false
  
  export_options:
    include_metadata: true
    include_statistics: true
    include_raw_data: true
    
# 로깅 설정
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file_output: "logs/qa_analysis.log"
  console_output: true

# 성능 설정
performance:
  cache_enabled: true
  cache_size: 1000
  model_loading_timeout: 120
  request_timeout: 30
  retry_attempts: 3
  retry_delay: 1.0
