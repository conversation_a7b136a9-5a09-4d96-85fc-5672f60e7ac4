"""
Supabase 및 OpenAI API 설정 관리 모듈
"""
import os
from typing import Optional

from dotenv import load_dotenv

# .env 파일 로드
load_dotenv()

# API 키 설정
OPENAI_API_KEY: Optional[str] = os.getenv("OPENAI_API_KEY")
GEMINI_API_KEY: Optional[str] = os.getenv("GEMINI_API_KEY")

# Supabase 설정
SUPABASE_URL: str = os.getenv(
    "SUPABASE_URL", 
    "https://ffekvlabwetedcdhkulw.supabase.co"
)

# 주의: 실제 프로덕션에서는 환경 변수나 안전한 보관소를 사용하세요
SUPABASE_KEY: str = os.getenv("SUPABASE_KEY", "")

# 추가 설정
DEFAULT_LIMIT: int = 100
DEFAULT_TIMEOUT: int = 30

# 개발 모드에서만 사용할 디버깅 코드
if __name__ == "__main__":
    print(f"OpenAI API Key 설정됨: {bool(OPENAI_API_KEY)}")
    print(f"Supabase URL: {SUPABASE_URL}")
    print(f"Supabase Key 설정됨: {bool(SUPABASE_KEY)}")
    print(f"Gemini API Key 설정됨: {bool(GEMINI_API_KEY)}")