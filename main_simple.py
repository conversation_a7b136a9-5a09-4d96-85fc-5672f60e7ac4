"""
QA Analysis System v2.2 - 간소화된 메인 파일
한국어 이커머스 문의 분석 시스템
"""
import sys
import asyncio
from pathlib import Path
from typing import Dict, List, Any

# 프로젝트 루트를 Python 경로에 추가
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from app.agents.qa_agent import QAAnalysisAgent
from app.config.manager import ConfigManager
from app.core.domain import AnalysisRequest


def display_welcome():
    """환영 메시지"""
    print("=" * 80)
    print("🚀 QA Analysis System v2.2")
    print("=" * 80)
    print("📊 한국어 이커머스 문의 분석 시스템")
    print("🤖 4개 한국어 특화 모델 지원")
    print("⚡ 완전 모듈화 구조")
    print("📁 LLM 개발 표준 구조")
    print("=" * 80)


def display_main_menu():
    """메인 메뉴"""
    print("\n🎯 사용할 기능을 선택하세요:")
    print("-" * 60)
    print("📊 기본 분석:")
    print("  1. 📊 개별 QA 분석")
    print("  2. 📈 배치 QA 분석")
    print("  3. 🧪 모델 테스트")
    print()
    print("🔍 고급 분석:")
    print("  4. 🔍 모델 성능 비교")
    print("  5. 🏷️ 통합 분류 시스템")
    print("  6. 🔄 하이브리드 분류 파이프라인")
    print()
    print("⚙️ 관리:")
    print("  7. 🤖 모델 관리")
    print("  8. 📁 결과 관리")
    print("  9. 🔧 프로필 관리")
    print("  10. 📋 시스템 정보")
    print()
    print("  0. 🚪 종료")
    print("-" * 60)


async def handle_single_analysis():
    """개별 QA 분석"""
    print("\n📊 개별 QA 분석")
    print("=" * 40)
    
    question = input("질문을 입력하세요: ").strip()
    answer = input("답변을 입력하세요: ").strip()
    
    if not question:
        print("❌ 질문이 필요합니다.")
        return
    
    try:
        from config.config import SUPABASE_URL, SUPABASE_KEY
        agent = QAAnalysisAgent(SUPABASE_URL, SUPABASE_KEY)
    except:
        agent = QAAnalysisAgent()
    
    request = AnalysisRequest(
        question_text=question,
        answer_text=answer
    )
    
    print("\n🔍 분석 중...")
    result = await agent.analyze_single_qa(request)
    print_analysis_result(result)


async def handle_batch_analysis():
    """배치 분석"""
    print("\n📈 배치 QA 분석")
    print("=" * 40)
    
    try:
        limit = int(input("분석할 문의 수 (기본: 5, 최대: 1000): ") or "5")
        limit = min(max(1, limit), 1000)
    except ValueError:
        limit = 5
    
    try:
        from config.config import SUPABASE_URL, SUPABASE_KEY
        agent = QAAnalysisAgent(SUPABASE_URL, SUPABASE_KEY)
    except:
        print("⚠️ DB 설정을 찾을 수 없습니다.")
        return
    
    print(f"\n📊 {limit}건의 문의 배치 분석 시작...")
    batch_result = await agent.analyze_sample_data(limit)
    
    print_batch_summary(batch_result)
    agent.export_results(batch_result)


async def handle_classification_system():
    """통합 분류 시스템"""
    print("\n🏷️ 통합 분류 시스템")
    print("=" * 40)
    
    question = input("분류할 문의를 입력하세요: ").strip()
    if not question:
        print("❌ 문의 내용이 필요합니다.")
        return
    
    try:
        from app.services.simple_factory import ServiceFactory
        from app.services.unified_classification import UnifiedClassificationSystem
        
        service_factory = ServiceFactory()
        classification_system = UnifiedClassificationSystem(
            service_factory.get_embedding_model()
        )
        
        print("\n🔍 분류 중...")
        result = await classification_system.classify_inquiry(question)
        print_classification_result(result)
        
    except Exception as e:
        print(f"❌ 분류 실패: {e}")


async def handle_hybrid_pipeline():
    """하이브리드 분류 파이프라인"""
    print("\n🔄 하이브리드 분류 파이프라인")
    print("=" * 40)
    
    text = input("분류할 텍스트를 입력하세요: ").strip()
    if not text:
        print("❌ 텍스트가 필요합니다.")
        return
    
    try:
        from app.services.simple_factory import ServiceFactory
        from app.services.hybrid_pipeline import HybridClassificationPipeline
        
        service_factory = ServiceFactory()
        pipeline = HybridClassificationPipeline(service_factory)
        
        print("\n🔍 하이브리드 분류 중...")
        result = await pipeline.classify_single(text, use_hybrid=True)
        print_hybrid_result(result)
        
    except Exception as e:
        print(f"❌ 하이브리드 분류 실패: {e}")


def handle_profile_management():
    """프로필 관리"""
    config_manager = ConfigManager()
    
    while True:
        print("\n🔧 프로필 관리")
        print("=" * 40)
        print("1. 현재 프로필 보기")
        print("2. 프로필 변경")
        print("3. 모든 프로필 목록")
        print("0. 돌아가기")
        
        try:
            choice = int(input("\n선택 (0-3): "))
        except ValueError:
            continue
        
        if choice == 1:
            config_manager.print_current_config()
        elif choice == 2:
            profiles = config_manager.get_available_profiles()
            current = config_manager.get_current_profile()
            
            print(f"\n📋 사용 가능한 프로필:")
            for i, profile in enumerate(profiles, 1):
                marker = "✅" if profile == current else "  "
                info = config_manager.get_profile_info(profile)
                print(f"{marker} {i}. {profile}")
                print(f"     {info.get('description', '')}")
            
            try:
                choice = int(input(f"\n변경할 프로필 번호 (1-{len(profiles)}): "))
                if 1 <= choice <= len(profiles):
                    new_profile = profiles[choice - 1]
                    config_manager.set_profile(new_profile)
            except ValueError:
                print("올바른 번호를 입력하세요.")
        elif choice == 3:
            config_manager.list_all_profiles()
        elif choice == 0:
            break


def handle_model_management():
    """모델 관리"""
    print("\n🤖 모델 관리")
    print("=" * 40)

    while True:
        print("\n모델 관리 메뉴:")
        print("1. 현재 모델 확인")
        print("2. 사용 가능한 모델 목록")
        print("3. 모델 성능 정보")
        print("4. 모델 캐시 초기화")
        print("0. 돌아가기")

        try:
            choice = int(input("\n선택 (0-4): "))
        except ValueError:
            print("올바른 번호를 입력하세요.")
            continue

        if choice == 1:
            # 현재 모델 확인
            try:
                from app.services.simple_factory import ServiceFactory
                factory = ServiceFactory()
                config_manager = factory.get_config_manager()

                profile_info = config_manager.get_profile_info()
                print(f"\n📋 현재 모델 설정:")
                print(f"  • 프로필: {config_manager.get_current_profile()}")
                print(f"  • 임베딩 모델: {profile_info.get('embedding_model', 'N/A')}")
                print(f"  • LLM 모델: {profile_info.get('llm_model', 'N/A')}")
                print(f"  • 분류 모델: {profile_info.get('classification_model', 'N/A')}")

                service_info = factory.get_service_info()
                print(f"\n🔧 서비스 상태:")
                print(f"  • 캐시된 서비스: {len(service_info['cached_services'])}개")
                print(f"  • 팩토리 초기화: {service_info['factory_initialized']}")

            except Exception as e:
                print(f"❌ 모델 정보 조회 실패: {e}")

        elif choice == 2:
            # 사용 가능한 모델 목록
            print(f"\n📋 사용 가능한 모델:")
            print(f"\n🔤 임베딩 모델:")
            embedding_models = [
                "snunlp/KR-SBERT-V40K-klueNLI-augSTS",
                "xlm-roberta-base",
                "beomi/KcELECTRA-base",
                "monologg/koelectra-base-v3-discriminator"
            ]
            for i, model in enumerate(embedding_models, 1):
                print(f"  {i}. {model}")

            print(f"\n🧠 LLM 모델:")
            llm_models = [
                "gpt-4.1-mini"
            ]
            for i, model in enumerate(llm_models, 1):
                print(f"  {i}. {model}")

            print(f"\n💰 LLM 비용 정보 (GPT-4.1-mini):")
            print(f"  • 입력: $0.40/1M 토큰 (₩560/1M 토큰)")
            print(f"  • 캐시된 입력: $0.10/1M 토큰 (₩140/1M 토큰)")
            print(f"  • 출력: $1.60/1M 토큰 (₩2,240/1M 토큰)")
            print(f"  • 환율: 1달러 = 1,400원")

        elif choice == 3:
            # 모델 성능 정보
            print(f"\n📊 모델 성능 정보:")
            print(f"\n🔤 임베딩 모델 성능:")
            print(f"  • KR-SBERT: 한국어 특화, 빠른 속도")
            print(f"  • KcELECTRA: 높은 정확도, 중간 속도")
            print(f"  • KoELECTRA: 균형잡힌 성능")
            print(f"  • KLUE-RoBERTa: 표준 성능")

            print(f"\n🧠 LLM 모델 성능:")
            print(f"  • GPT-4o-mini: 높은 정확도, 빠른 속도")
            print(f"  • GPT-3.5-turbo: 균형잡힌 성능")
            print(f"  • Claude-3-haiku: 빠른 응답")
            print(f"  • Gemini-pro: 다양한 기능")

        elif choice == 4:
            # 모델 캐시 초기화
            try:
                from app.services.simple_factory import ServiceFactory
                factory = ServiceFactory()
                factory.clear_cache()
                print("✅ 모델 캐시가 초기화되었습니다.")
            except Exception as e:
                print(f"❌ 캐시 초기화 실패: {e}")

        elif choice == 0:
            break
        else:
            print("올바른 메뉴를 선택하세요 (0-4).")


def handle_result_management():
    """결과 관리"""
    print("\n📁 결과 관리")
    print("=" * 40)

    while True:
        print("\n결과 관리 메뉴:")
        print("1. 저장된 결과 목록")
        print("2. 결과 검색")
        print("3. 결과 삭제")
        print("4. 저장소 정보")
        print("0. 돌아가기")

        try:
            choice = int(input("\n선택 (0-4): "))
        except ValueError:
            print("올바른 번호를 입력하세요.")
            continue

        if choice == 1:
            # 저장된 결과 목록
            import os
            outputs_dir = "outputs"
            if os.path.exists(outputs_dir):
                files = [f for f in os.listdir(outputs_dir) if f.endswith('.json')]
                if files:
                    print(f"\n📋 저장된 결과 파일 ({len(files)}개):")
                    for i, file in enumerate(files[-10:], 1):  # 최근 10개만
                        print(f"  {i}. {file}")
                else:
                    print("📭 저장된 결과가 없습니다.")
            else:
                print("📁 outputs 폴더가 없습니다.")

        elif choice == 2:
            # 결과 검색
            search_term = input("검색어를 입력하세요: ").strip()
            if search_term:
                print(f"🔍 '{search_term}' 검색 기능은 개발 중입니다.")
            else:
                print("검색어를 입력해주세요.")

        elif choice == 3:
            # 결과 삭제
            filename = input("삭제할 파일명을 입력하세요: ").strip()
            if filename:
                import os
                filepath = os.path.join("outputs", filename)
                if os.path.exists(filepath):
                    try:
                        os.remove(filepath)
                        print(f"✅ {filename} 삭제 완료")
                    except Exception as e:
                        print(f"❌ 삭제 실패: {e}")
                else:
                    print(f"❌ 파일을 찾을 수 없습니다: {filename}")

        elif choice == 4:
            # 저장소 정보
            import os
            outputs_dir = "outputs"
            if os.path.exists(outputs_dir):
                files = os.listdir(outputs_dir)
                total_files = len(files)
                json_files = len([f for f in files if f.endswith('.json')])

                print(f"\n📁 저장소 정보:")
                print(f"  • 경로: {os.path.abspath(outputs_dir)}")
                print(f"  • 전체 파일: {total_files}개")
                print(f"  • JSON 결과: {json_files}개")

                # 디스크 사용량
                total_size = sum(os.path.getsize(os.path.join(outputs_dir, f))
                               for f in files if os.path.isfile(os.path.join(outputs_dir, f)))
                print(f"  • 사용 용량: {total_size / 1024:.1f} KB")
            else:
                print("📁 outputs 폴더가 없습니다.")

        elif choice == 0:
            break
        else:
            print("올바른 메뉴를 선택하세요 (0-4).")


def handle_system_info():
    """시스템 정보"""
    config_manager = ConfigManager()

    print("\n📋 시스템 정보")
    print("=" * 40)
    print("🚀 QA Analysis System v2.2")
    print("🏗️ LLM 개발 표준 구조")
    print("🇰🇷 한국어 특화 모델 지원")
    print("📁 간결한 폴더 구조")

    profile_info = config_manager.get_profile_info()
    print(f"\n⚙️ 현재 설정:")
    print(f"  • 프로필: {config_manager.get_current_profile()}")
    print(f"  • 설명: {profile_info.get('description', '')}")
    print(f"  • 임베딩: {profile_info.get('embedding_model', '')}")
    print(f"  • LLM: {profile_info.get('llm_model', '')}")

    print(f"\n📁 폴더 구조:")
    print(f"  • app/: 메인 애플리케이션")
    print(f"  • data/: 데이터 관리")
    print(f"  • outputs/: 결과 저장")
    print(f"  • tests/: 테스트 코드")
    print(f"  • archive/: 기존 파일 백업")


@staticmethod
def print_analysis_result(result):
    """분석 결과 출력"""
    print("\n" + "=" * 60)
    print("📊 QA 분석 결과")
    print("=" * 60)
    
    print(f"🆔 문의 ID: {result.id}")
    print(f"❓ 질문: {result.question[:100]}...")
    print(f"💬 답변: {result.answer[:100]}..." if result.answer else "💬 답변: (없음)")
    
    print(f"\n📈 분석 점수:")
    print(f"  • 의미적 유사도: {result.semantic_similarity:.3f}")
    print(f"  • 주제 관련성: {result.topic_relevance:.3f}")
    print(f"  • 키워드 겹침: {result.keyword_overlap:.3f}")
    print(f"  • 답변 완성도: {result.answer_completeness:.3f}")
    print(f"  • 답변 정확성: {result.answer_accuracy:.3f}")
    print(f"  • 답변 도움도: {result.answer_helpfulness:.3f}")
    
    print(f"\n🎯 종합 결과:")
    print(f"  • 전체 점수: {result.overall_score:.3f}")
    print(f"  • 등급: {result.grade.value}")
    print(f"  • 통과 여부: {'✅ 통과' if result.pass_threshold else '❌ 미통과'}")
    
    if result.strengths:
        print(f"\n💪 강점:")
        for strength in result.strengths:
            print(f"  • {strength}")
    
    if result.weaknesses:
        print(f"\n⚠️ 약점:")
        for weakness in result.weaknesses:
            print(f"  • {weakness}")
    
    if result.recommendations:
        print(f"\n💡 개선사항:")
        for rec in result.recommendations:
            print(f"  • {rec}")
    
    print(f"\n⏱️ 처리 시간: {result.processing_time_ms:.2f}ms")
    print("=" * 60)


def print_batch_summary(batch_result):
    """배치 분석 결과 요약"""
    print("\n" + "=" * 60)
    print("📊 배치 분석 결과 요약")
    print("=" * 60)
    print(f"📈 총 분석: {batch_result.total_count}건")
    print(f"✅ 통과: {batch_result.pass_count}건 ({batch_result.pass_count/batch_result.total_count*100:.1f}%)")
    print(f"📊 평균 점수: {batch_result.average_score:.3f}")
    
    print(f"\n🏆 등급 분포:")
    for grade in ['A', 'B', 'C', 'D', 'F']:
        count = batch_result.grade_distribution.get(grade, 0)
        if count > 0:
            print(f"  • {grade}등급: {count}건")
    
    print(f"\n⏱️ 처리 시간: {batch_result.processing_time_seconds:.2f}초")
    print("=" * 60)


def print_classification_result(result: Dict[str, Any]):
    """분류 결과 출력"""
    print("\n" + "=" * 60)
    print("🏷️ 분류 결과")
    print("=" * 60)
    
    classification = result.get("classification", {})
    sentiment = result.get("sentiment_analysis", {})
    urgency = result.get("urgency_analysis", {})
    
    print(f"📂 카테고리: {classification.get('category', 'N/A')} "
          f"(신뢰도: {classification.get('confidence', 0):.3f})")
    print(f"😊 감정: {sentiment.get('sentiment', 'N/A')} "
          f"(점수: {sentiment.get('score', 0):.3f})")
    print(f"⚡ 긴급도: {urgency.get('urgency', 'N/A')} "
          f"(점수: {urgency.get('score', 0):.3f})")
    print(f"🎯 우선순위: {result.get('priority_level', 'N/A')} "
          f"(점수: {result.get('priority_score', 0):.3f})")
    
    routing = result.get("routing_recommendation", {})
    if routing:
        print(f"🏢 추천 부서: {routing.get('department', 'N/A')}")
    
    action_items = result.get("action_items", [])
    if action_items:
        print(f"\n📋 액션 아이템:")
        for item in action_items:
            print(f"  • {item}")
    
    print(f"\n⏱️ 처리 시간: {result.get('processing_time_ms', 0):.2f}ms")
    print("=" * 60)


def print_hybrid_result(result: Dict[str, Any]):
    """하이브리드 결과 출력"""
    print("\n" + "=" * 60)
    print("🔄 하이브리드 분류 결과")
    print("=" * 60)
    
    print(f"📂 카테고리: {result.get('category', 'N/A')}")
    print(f"🎯 신뢰도: {result.get('confidence', 0):.3f}")
    print(f"🔄 파이프라인 단계: {result.get('pipeline_stage', 'N/A')}")
    print(f"⏱️ 처리 시간: {result.get('processing_time_ms', 0):.2f}ms")
    
    if "error" in result:
        print(f"❌ 오류: {result['error']}")
    
    print("=" * 60)


async def main():
    """메인 함수"""
    display_welcome()
    
    # 설정 초기화
    config_manager = ConfigManager()
    config_manager.validate_current_config()
    
    while True:
        display_main_menu()
        
        try:
            choice = int(input("선택 (0-10): "))
        except ValueError:
            print("숫자를 입력하세요.")
            continue
        
        try:
            if choice == 1:
                await handle_single_analysis()
            elif choice == 2:
                await handle_batch_analysis()
            elif choice == 3:
                await handle_single_analysis()  # 모델 테스트는 단일 분석과 동일
            elif choice == 4:
                print("🔄 모델 성능 비교 기능은 개발 중입니다.")
            elif choice == 5:
                await handle_classification_system()
            elif choice == 6:
                await handle_hybrid_pipeline()
            elif choice == 7:
                handle_model_management()
            elif choice == 8:
                handle_result_management()
            elif choice == 9:
                handle_profile_management()
            elif choice == 10:
                handle_system_info()
            elif choice == 0:
                print("👋 프로그램을 종료합니다.")
                return 0
            else:
                print("올바른 메뉴를 선택하세요 (0-10).")
        except KeyboardInterrupt:
            print("\n👋 프로그램을 종료합니다.")
            return 0
        except Exception as e:
            print(f"❌ 오류가 발생했습니다: {e}")
    
    return 0


if __name__ == "__main__":
    sys.exit(asyncio.run(main()))
