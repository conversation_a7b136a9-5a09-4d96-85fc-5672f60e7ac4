# 이커머스 문의 분류 시스템 구현 계획

## 1단계: 기초 설정 및 데이터 준비 (1-2주)

- [x] 프로젝트 구조 설계
- [ ] 필요 라이브러리 설치 및 환경 설정
- [ ] Supabase 테이블 스키마 설계
- [ ] 각 카테고리별 3-5개 대표 예시 수집
- [ ] 테스트 데이터셋 구성

## 2단계: 분류 모델 개발 (2-3주)

- [ ] 한국어 임베딩 모델 선택 및 테스트
  - KoBERT, KoSBERT, KoSimCSE 등 검토
  - 이커머스 도메인 특화 파인튜닝
- [ ] LLM 백업 시스템 구현
  - OpenAI API 연동
  - 프롬프트 엔지니어링 및 최적화
- [ ] 하이브리드 분류 파이프라인 구현
- [ ] 성능 평가 및 최적화

## 3단계: API 및 서비스 개발 (2주)

- [ ] FastAPI 기반 REST API 구현
- [ ] 비동기 처리 최적화
- [ ] 결과 저장 기능 구현 (JSON, CSV, Supabase)
- [ ] 에러 처리 및 로깅 시스템 구축

## 4단계: 자동화 및 통합 (1-2주)

- [ ] n8n 워크플로우 설계
- [ ] 배치 처리 자동화
- [ ] 모니터링 대시보드 구현
- [ ] 성능 지표 수집 및 분석 시스템

## 5단계: 테스트 및 최적화 (1-2주)

- [ ] 대규모 데이터셋 테스트
- [ ] 성능 벤치마크 측정
- [ ] 병목 지점 식별 및 최적화
- [ ] 사용자 피드백 수집 및 반영

## 6단계: 배포 및 운영 (1주)

- [ ] 도커 컨테이너화
- [ ] CI/CD 파이프라인 구축
- [ ] 운영 환경 배포
- [ ] 모니터링 및 유지보수 계획 수립