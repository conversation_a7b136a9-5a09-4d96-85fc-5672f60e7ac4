# 문의 데이터 처리 시스템

이 시스템은 Supabase 데이터베이스를 활용하여 온라인 쇼핑몰의 고객 문의 데이터를 효율적으로 처리, 분류, 관리하는 Python 기반 애플리케이션입니다. 비동기 처리 방식을 적용하여 대규모 데이터를 빠르게 처리하며, AI 기반 문의 분류 기능을 통해 셀러와 구매자의 문의를 자동으로 분류합니다.

## 🚀 주요 기능

1. **기본 문의 데이터 조회 (동기식)**: 
   - 일반적인 동기 방식으로 문의 데이터를 조회하고 요약 정보 제공
   - 간단하고 직관적인 인터페이스

2. **비동기 문의 데이터 처리**:
   - `asyncio`를 활용한 비동기 처리로 I/O 작업 효율성 극대화
   - 다수의 문의를 병렬로 처리하여 속도 향상

3. **대규모 문의 데이터 일괄 처리**:
   - 수백~수천 개의 문의 데이터를 효율적으로 처리
   - 배치 처리 및 작업 분할을 통한 성능 최적화

4. **여러 테이블 통합 처리**:
   - 다양한 데이터 소스(테이블)의 문의 데이터 통합 처리
   - 일관된 형식으로 변환 및 표시

5. **한국어 AI 분류 시스템**:
   - 임베딩 모델과 LLM을 활용한 문의 내용 자동 분류
   - 카테고리별(배송, 반품, 교환, 상품 정보 등) 문의 분류


## 📋 프로젝트 구조

```
project_root/
├── config/                  # 설정 관련 파일
│   └── config.py            # Supabase 및 API 키 설정
├── database/                # 데이터베이스 관련 모듈
├── src/                     # 소스 코드
│   ├── models/              # 데이터 모델
│   │   └── inquiry.py       # 문의 데이터 모델 정의
│   ├── services/            # 비즈니스 로직 서비스
│   │   ├── async_inquiry_runner.py      # 비동기 실행 모듈
│   │   ├── async_inquiry_service.py     # 비동기 문의 서비스
│   │   ├── batch_service.py             # 배치 처리 서비스
│   │   ├── bulk_processing_service.py   # 대량 데이터 처리
│   │   ├── embedding_model_service.py   # 임베딩 모델 서비스
│   │   ├── inquiry_classifier_service.py # 문의 분류 서비스
│   │   ├── inquiry_service.py            # 기본 문의 서비스
│   │   ├── llm_service.py                # LLM 활용 서비스
│   │   └── multi_table_inquiry_service.py # 다중 테이블 서비스
│   └── utils/               # 유틸리티 함수
│       └── async_utils.py   # 비동기 유틸리티
└── main.py                  # 메인 실행 파일
```

## 💻 기술 스택

- **Python 3.11+**: 타입 힌팅과 최신 비동기 기능 활용
- **Supabase**: PostgreSQL 기반 데이터베이스 및 인증 시스템
- **asyncio**: 비동기 I/O 작업을 위한 라이브러리
- **Pydantic**: 데이터 유효성 검증 및 설정 관리
- **OpenAI API** (선택적): LLM 기반 문의 분류에 활용


## 🔍 데이터 모델

### 문의 모델 (Inquiry)

```python
class Inquiry(BaseModel):
    id: str                              # 문의 ID
    post_id: str                         # 게시물 ID  
    user_id: Optional[str] = None        # 사용자 ID
    content: str                         # 문의 내용
    subject: Optional[str] = None        # 제목
    created_at: datetime                 # 작성일
    is_answered: bool = False            # 답변 여부
    answer_content: Optional[str] = None # 답변 내용
    answered_at: Optional[datetime] = None # 답변일
    metadata: Optional[Dict] = None      # 추가 메타데이터
```

## ⚙️ 설치 및 설정

1. 저장소 클론 및 가상환경 설정:

```bash
git clone <repository-url>
cd POC
python -m venv .venv
source .venv/bin/activate  # Windows: .venv\Scripts\activate
pip install -r requirements.txt
```

2. Supabase 설정:
   - `.env` 파일 생성 (또는 `config/config.py` 수정):

```
SUPABASE_URL=your_supabase_url
SUPABASE_KEY=your_supabase_key
OPENAI_API_KEY=your_openai_key  # 선택사항
```

3. 실행:

```bash
python main.py
```

## 🧩 주요 구성 요소 상세 설명

### 1. 비동기 데이터 처리

비동기 처리 시스템은 `asyncio` 라이브러리를 활용하여 I/O 작업을 효율적으로 관리합니다. 특히 Supabase API와의 통신에서 성능 향상이 두드러집니다.

핵심 컴포넌트:
- `async_timed()` 데코레이터: 비동기 함수 실행 시간 측정
- `run_async_in_thread()`: 비동기 코드를 동기 환경에서 실행하도록 돕는 유틸리티
- `batch_process()`: 대량의 항목을 배치로 처리하는 함수

```python
# 비동기 실행 예시
async def process_inquiries():
    service = AsyncInquiryService(SUPABASE_URL, SUPABASE_KEY)
    inquiries = await service.get_all_inquiries()
    # 필터링, 처리, 답변 작성 등의 작업 수행
    return inquiries
```

### 2. 대규모 데이터 처리

대량의 문의 데이터를 효율적으로 처리할 수 있도록 분할 처리 및 병렬화 기법을 적용합니다.

주요 기능:
- 데이터 배치 처리로 메모리 사용 최적화
- 병렬 처리를 통한 속도 향상 
- 처리 진행 상태 모니터링
- 오류 발생 시 자동 재시도 메커니즘

### 3. AI 기반 문의 분류

두 가지 방식의 AI 분류 시스템을 제공합니다:

1. **임베딩 기반 분류**:
   - 문의 데이터를 벡터로 변환하여 유사도 기반 분류
   - 사전 정의된 카테고리와 비교하여 가장 유사한 카테고리 할당
   - 가벼운 CPU 기반 처리로 빠른 분류 가능

2. **LLM 기반 분류** (OpenAI API 필요):
   - GPT 모델을 활용한 고정밀 문의 내용 이해 및 분류
   - 복잡한 문의도 정확하게 분류 가능
   - 분류 신뢰도 점수 제공

분류 카테고리:
- 배송 관련 문의
- 반품/교환 문의
- 상품 정보 문의
- 결제 관련 문의
- 취소 문의
- 기타 문의

## 🔄 워크플로우

1. 시스템 시작 및 메뉴 선택
2. 문의 데이터 로드 (동기 또는 비동기)
3. 문의 내용 분석 및 필요시 AI 분류
4. 분류된 문의에 대한 통계 및 요약 정보 제공
5. 필요시 샘플 답변 생성

## 📊 성능 및 통계

시스템은 다음과 같은 통계 정보를 제공합니다:
- 총 문의 수
- 답변 완료/미답변 문의 수 및 답변률
- 카테고리별 문의 분포
- 평균 처리 시간
- 사용자별 문의 빈도

## 🔧 문제 해결

### 알려진 이슈

1. **날짜 파싱 오류**:
   - 데이터에 `nan` 값이 포함된 경우 파싱 오류가 발생할 수 있습니다.
   - 해결: `math.isnan()` 함수를 사용하여 `nan` 값을 처리합니다.

2. **비동기 처리 관련 이슈**:
   - 이벤트 루프가 이미 실행 중인 환경에서 오류가 발생할 수 있습니다.
   - 해결: `run_async_in_thread()` 유틸리티를 사용하여 새로운 스레드에서 비동기 코드를 실행합니다.

## 📝 라이센스

이 프로젝트는 MIT 라이센스 하에 배포됩니다.

## 👥 기여

버그 리포트 및 풀 리퀘스트는 환영합니다. 중요한 변경 사항은 먼저 이슈를 열어 논의해 주세요.
