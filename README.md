# 문의 데이터 처리 시스템

이 시스템은 Supabase 데이터베이스를 활용하여 온라인 쇼핑몰의 고객 문의 데이터를 효율적으로 처리, 분류, 관리하는 Python 기반 애플리케이션입니다. 비동기 처리 방식을 적용하여 대규모 데이터를 빠르게 처리하며, AI 기반 문의 분류 기능을 통해 셀러와 구매자의 문의를 자동으로 분류합니다.

## 🚀 주요 기능

1. **기본 문의 데이터 조회 (동기식)**: 
   - 일반적인 동기 방식으로 문의 데이터를 조회하고 요약 정보 제공
   - 간단하고 직관적인 인터페이스

2. **비동기 문의 데이터 처리**:
   - `asyncio`를 활용한 비동기 처리로 I/O 작업 효율성 극대화
   - 다수의 문의를 병렬로 처리하여 속도 향상

3. **대규모 문의 데이터 일괄 처리**:
   - 수백~수천 개의 문의 데이터를 효율적으로 처리
   - 배치 처리 및 작업 분할을 통한 성능 최적화

4. **여러 테이블 통합 처리**:
   - 다양한 데이터 소스(테이블)의 문의 데이터 통합 처리
   - 일관된 형식으로 변환 및 표시

5. **한국어 AI 분류 시스템**:
   - 임베딩 모델과 LLM을 활용한 문의 내용 자동 분류
   - 카테고리별(배송, 반품, 교환, 상품 정보 등) 문의 분류


## 📋 프로젝트 구조

```
project_root/
├── config/                  # 설정 관련 파일
│   └── config.py            # Supabase 및 API 키 설정
├── database/                # 데이터베이스 관련 모듈
├── src/                     # 소스 코드
│   ├── models/              # 데이터 모델
│   │   └── inquiry.py       # 문의 데이터 모델 정의
│   ├── services/            # 비즈니스 로직 서비스
│   │   ├── async_inquiry_runner.py      # 비동기 실행 모듈
│   │   ├── async_inquiry_service.py     # 비동기 문의 서비스
│   │   ├── batch_service.py             # 배치 처리 서비스
│   │   ├── bulk_processing_service.py   # 대량 데이터 처리
│   │   ├── embedding_model_service.py   # 임베딩 모델 서비스
│   │   ├── inquiry_classifier_service.py # 문의 분류 서비스
│   │   ├── inquiry_service.py            # 기본 문의 서비스
│   │   ├── llm_service.py                # LLM 활용 서비스
│   │   └── multi_table_inquiry_service.py # 다중 테이블 서비스
│   └── utils/               # 유틸리티 함수
│       └── async_utils.py   # 비동기 유틸리티
└── main.py                  # 메인 실행 파일
```

## 📐 시스템 아키텍처

### 개요

이 시스템은 다층 아키텍처를 채택하여 관심사 분리와 모듈화를 강조했습니다:

```
┌─────────────────┐
│     UI 계층     │ - 사용자 인터페이스 (CLI)
└────────┬────────┘
         │
┌────────▼────────┐
│   서비스 계층    │ - 비즈니스 로직 구현
└────────┬────────┘
         │
┌────────▼────────┐
│   데이터 계층    │ - 데이터 액세스 및 모델
└────────┬────────┘
         │
┌────────▼────────┐
│   외부 시스템    │ - Supabase, OpenAI API
└─────────────────┘
```

### 컴포넌트 상호작용

1. **사용자 인터페이스 (UI 계층)**:
   - `main.py`: 사용자 입력을 받아 적절한 서비스 호출
   - 결과 포맷팅 및 표시 담당

2. **서비스 계층**:
   - `inquiry_service.py`: 기본 문의 처리 로직
   - `async_inquiry_service.py`: 비동기 문의 처리 로직
   - `embedding_model_service.py` & `llm_service.py`: AI 분류 로직
   - `batch_service.py` & `bulk_processing_service.py`: 대량 처리 로직

3. **데이터 계층**:
   - `inquiry.py`: Pydantic 기반 객체-관계 매핑
   - Supabase 클라이언트: 데이터 CRUD 작업

4. **외부 시스템**:
   - Supabase: 문의 데이터 저장소
   - OpenAI API: LLM 기반 분류 (선택적)

## 💻 기술 스택

### 핵심 기술

- **Python 3.11+**: 
  - 최신 타입 힌팅 기능 활용
  - 비동기 프로그래밍 지원 (`asyncio`)
  - 구조적 패턴 매칭 활용

- **Supabase**: 
  - PostgreSQL 기반의 백엔드 데이터베이스
  - RESTful API 인터페이스
  - 실시간 구독 기능 (미래 확장성)
  - Row-level 보안 정책 (RLS)

- **asyncio 생태계**:
  - `asyncio`: 비동기 I/O 작업 관리
  - 커스텀 데코레이터: 실행 시간 측정, 오류 처리 등
  - 동시성 제어: 세마포어, 락, 이벤트 등

- **Pydantic**: 
  - 데이터 검증 및 설정 관리
  - JSON 직렬화/역직렬화
  - 타입 강제 및 변환

### AI/ML 기술

- **임베딩 모델**:
  - 한국어 텍스트 벡터화
  - 유사도 기반 문의 분류
  - 가벼운 CPU 기반 추론

- **OpenAI API** (선택적):
  - GPT 모델 기반 고급 문의 분류
  - 문맥 인식 답변 생성
  - 복잡한 문의 이해 및 분석

### 개발 도구

- **환경 관리**: 
  - Python venv 또는 conda
  - dotenv를 통한 환경 변수 관리

- **코드 품질**:
  - Type hints 및 mypy
  - Black, flake8, isort를 통한 코드 포맷팅
  - pytest 기반 테스트 자동화

## 🔍 데이터 모델

### 문의 모델 (Inquiry)

기본 모델 구조:

```python
class Inquiry(BaseModel):
    id: str                              # 문의 ID
    post_id: str                         # 게시물 ID  
    user_id: Optional[str] = None        # 사용자 ID
    content: str                         # 문의 내용
    subject: Optional[str] = None        # 제목
    created_at: datetime                 # 작성일
    is_answered: bool = False            # 답변 여부
    answer_content: Optional[str] = None # 답변 내용
    answered_at: Optional[datetime] = None # 답변일
    metadata: Optional[Dict] = None      # 추가 메타데이터
```

### 데이터베이스 스키마

Supabase에서 다음과 같은 테이블 구조를 사용합니다:

1. **`personal_inquiries`**: 고객 개인 문의
   - 고객 관련 개인 문의 (배송, 환불, 계정 관련 등)
   - 고객 정보와 연결됨

2. **`product_inquiries`**: 상품 관련 문의
   - 상품 정보, 사용법, 재입고 등 관련 문의
   - 상품 ID와 연결됨

3. **`shipping_inquiry`**: 배송 관련 문의
   - 배송 상태, 지연, 분실 등에 관한 문의
   - 주문 ID와 연결됨

4. **`classification_results`**: 분류 결과 저장
   - AI 분류 결과 및 신뢰도 점수 저장
   - 문의 ID와 연결됨

5. **`classification_logs`**: 분류 로그
   - 분류 과정 로깅 및 디버깅용
   - 성능 및 정확도 모니터링

## 🎓 추가 학습 내용

- **PEP 8**: 일관되고 읽기 쉬운 코드를 보장하기 위해 이 스타일 가이드를 마스터하세요.
- **테스트 주도 개발(TDD)**: 테스트를 먼저 작성하면 버그를 줄이고 리팩토링을 더 안전하게 만들 수 있습니다.
- **기본 보안**: 일반적인 취약점(SQL 인젝션, XSS)으로부터 보호하는 방법을 배우세요.
- **Docker & CI/CD**: 컨테이너화 및 자동화 파이프라인은 업계 표준이므로, 이러한 도구에 익숙해지면 실제 개발에서 매우 가치 있을 수 있습니다.

## ⚙️ 설치 및 설정

### 1. 요구사항

- Python 3.11 이상
- pip 또는 poetry
- git
- Supabase 계정 및 프로젝트

### 2. 세부 설정 단계

1. **저장소 클론**:
   ```bash
   git clone <repository-url>
   cd POC
   ```

2. **가상환경 설정**:
   ```bash
   python -m venv .venv
   source .venv/bin/activate  # Windows: .venv\Scripts\activate
   ```

3. **의존성 설치**:
   ```bash
   pip install -r requirements.txt
   ```

4. **환경 설정**:
   - `.env` 파일 생성:
   ```
   SUPABASE_URL=your_supabase_url
   SUPABASE_KEY=your_supabase_key
   OPENAI_API_KEY=your_openai_key  # 선택사항
   ```

5. **데이터베이스 테이블 설정**:
   - Supabase에서 다음 테이블 생성:
     - `personal_inquiries`
     - `product_inquiries`
     - `shipping_inquiry`
     - `classification_results`
     - `classification_logs`

6. **실행**:
   ```bash
   python main.py
   ```

## 📚 참조 및 리소스

### 관련 기술 문서

- [Supabase Python 클라이언트](https://supabase.io/docs/reference/python/introduction)
- [asyncio 공식 문서](https://docs.python.org/3/library/asyncio.html)
- [Pydantic 문서](https://pydantic-docs.helpmanual.io/)
- [OpenAI API 문서](https://platform.openai.com/docs/api-reference)

### 추가 학습 자료

- Python 비동기 프로그래밍:
  - [Python asyncio: The Complete Guide](https://realpython.com/async-io-python/)
  - [Asyncio for the Working Python Developer](https://testdriven.io/blog/concurrency-parallelism-asyncio/)

- 한국어 자연어 처리:
  - [KoBERT 모델](https://github.com/SKTBrain/KoBERT)
  - [한국어 임베딩](https://github.com/ratsgo/embedding)
  
## 📝 라이센스

이 프로젝트는 MIT 라이센스 하에 배포됩니다.

## 👥 기여

버그 리포트 및 풀 리퀘스트는 환영합니다. 중요한 변경 사항은 먼저 이슈를 열어 논의해 주세요.

## 📂 상세 프로젝트 구조

```
src/
├── api/                  # API 서비스
│   ├── __init__.py
│   └── classifier_api.py  # FastAPI 기반 분류 API
├── models/               # 데이터 모델
│   ├── __init__.py
│   ├── classification.py  # 분류 결과 모델
│   └── inquiry.py         # 문의 데이터 모델
├── services/             # 비즈니스 로직
│   ├── __init__.py
│   ├── async_inquiry_service.py  # 비동기 문의 서비스
│   ├── inquiry_classifier_service.py  # 분류 서비스
│   └── bulk_processing_service.py  # 대량 처리 서비스
├── utils/                # 유틸리티
│   ├── __init__.py
│   ├── async_utils.py     # 비동기 유틸리티
│   └── logging_utils.py   # 로깅 유틸리티
└── main.py               # 애플리케이션 진입점

config/
├── __init__.py
└── config.py             # 환경 설정

models/                   # 학습된 모델 저장
├── ko_ecommerce_classifier.bin  # 한국어 이커머스 분류 모델
└── embeddings/           # 임베딩 모델

tests/                    # 테스트
├── __init__.py
├── test_classifier.py
└── test_api.py

docs/                     # 문서
├── architecture.md       # 아키텍처 문서
└── implementation_plan.md  # 구현 계획

scripts/                  # 스크립트
├── setup.sh              # 환경 설정 스크립트
└── train_model.py        # 모델 학습 스크립트
```

## 📐 QA Analysis System 아키텍처

### 📁 새로운 폴더 구조 (SOLID 원칙 적용)

```
📁 qa_analysis_system/
├── 📁 core/                          # 핵심 도메인 로직
│   ├── __init__.py
│   ├── 📁 domain/                     # 도메인 모델 (Entity, Value Object)
│   │   ├── __init__.py
│   │   ├── qa_pair.py                 # QA 쌍 도메인 모델
│   │   ├── analysis_result.py         # 분석 결과 도메인 모델
│   │   └── inquiry.py                 # 문의 도메인 모델
│   ├── 📁 interfaces/                 # 인터페이스 (추상화)
│   │   ├── __init__.py
│   │   ├── ai_model_interface.py      # AI 모델 인터페이스
│   │   ├── repository_interface.py    # 저장소 인터페이스
│   │   └── analyzer_interface.py      # 분석기 인터페이스
│   └── 📁 use_cases/                  # 비즈니스 로직 (Use Case)
│       ├── __init__.py
│       ├── analyze_qa_pair.py         # QA 쌍 분석 유스케이스
│       ├── batch_analysis.py          # 배치 분석 유스케이스
│       └── model_management.py        # 모델 관리 유스케이스
│
├── 📁 infrastructure/                 # 외부 시스템 연동
│   ├── __init__.py
│   ├── 📁 ai_models/                  # AI 모델 구현체
│   │   ├── __init__.py
│   │   ├── 📁 embedding/              # 임베딩 모델들
│   │   │   ├── __init__.py
│   │   │   ├── kr_sbert.py            # KR-SBERT 구현
│   │   │   ├── kcelectra.py           # KcELECTRA 구현
│   │   │   ├── kobert.py              # KoBERT 구현
│   │   │   └── klue_roberta.py        # KLUE-RoBERTa 구현
│   │   ├── 📁 llm/                    # LLM 모델들
│   │   │   ├── __init__.py
│   │   │   ├── openai_gpt.py          # OpenAI GPT 구현
│   │   │   ├── anthropic_claude.py    # Anthropic Claude 구현
│   │   │   └── google_gemini.py       # Google Gemini 구현
│   │   └── model_factory.py           # 모델 팩토리
│   ├── 📁 repositories/               # 데이터 저장소 구현
│   │   ├── __init__.py
│   │   ├── supabase_repository.py     # Supabase 저장소
│   │   └── file_repository.py         # 파일 저장소
│   └── 📁 external_apis/              # 외부 API 연동
│       ├── __init__.py
│       └── supabase_client.py         # Supabase 클라이언트
│
├── 📁 application/                    # 애플리케이션 서비스
│   ├── __init__.py
│   ├── 📁 services/                   # 애플리케이션 서비스
│   │   ├── __init__.py
│   │   ├── qa_analysis_service.py     # QA 분석 서비스
│   │   ├── model_service.py           # 모델 관리 서비스
│   │   └── export_service.py          # 결과 내보내기 서비스
│   ├── 📁 agents/                     # 에이전트 (Facade 패턴)
│   │   ├── __init__.py
│   │   └── qa_analysis_agent.py       # QA 분석 에이전트
│   └── 📁 dto/                        # 데이터 전송 객체
│       ├── __init__.py
│       ├── analysis_request.py        # 분석 요청 DTO
│       └── analysis_response.py       # 분석 응답 DTO
│
├── 📁 presentation/                   # 프레젠테이션 계층
│   ├── __init__.py
│   ├── 📁 cli/                        # CLI 인터페이스
│   │   ├── __init__.py
│   │   ├── main_cli.py                # 메인 CLI
│   │   └── commands/                  # CLI 명령어들
│   │       ├── __init__.py
│   │       ├── analyze_command.py
│   │       └── config_command.py
│   └── 📁 api/                        # API 인터페이스 (향후 확장)
│       ├── __init__.py
│       └── rest_api.py
│
├── 📁 config/                         # 설정 관리
│   ├── __init__.py
│   ├── 📁 settings/                   # 설정 파일들
│   │   ├── __init__.py
│   │   ├── model_profiles.yaml        # 모델 프로필 설정
│   │   ├── analysis_config.yaml       # 분석 설정
│   │   └── app_config.yaml            # 애플리케이션 설정
│   ├── 📁 managers/                   # 설정 관리자들
│   │   ├── __init__.py
│   │   ├── config_manager.py          # 통합 설정 관리자
│   │   └── model_config_manager.py    # 모델 설정 관리자
│   └── 📁 validators/                 # 설정 검증기
│       ├── __init__.py
│       └── config_validator.py
│
├── 📁 shared/                         # 공통 유틸리티
│   ├── __init__.py
│   ├── 📁 utils/                      # 유틸리티 함수들
│   │   ├── __init__.py
│   │   ├── async_utils.py             # 비동기 유틸리티
│   │   ├── text_utils.py              # 텍스트 처리 유틸리티
│   │   └── validation_utils.py        # 검증 유틸리티
│   ├── 📁 exceptions/                 # 커스텀 예외
│   │   ├── __init__.py
│   │   ├── model_exceptions.py        # 모델 관련 예외
│   │   └── analysis_exceptions.py     # 분석 관련 예외
│   └── 📁 constants/                  # 상수 정의
│       ├── __init__.py
│       ├── model_constants.py         # 모델 관련 상수
│       └── analysis_constants.py      # 분석 관련 상수
│
├── 📁 tests/                          # 테스트 코드
│   ├── __init__.py
│   ├── 📁 unit/                       # 단위 테스트
│   │   ├── __init__.py
│   │   ├── test_domain/
│   │   ├── test_use_cases/
│   │   └── test_services/
│   ├── 📁 integration/                # 통합 테스트
│   │   ├── __init__.py
│   │   └── test_agents/
│   └── 📁 fixtures/                   # 테스트 데이터
│       ├── __init__.py
│       └── sample_data.py
│
├── 📁 docs/                           # 문서화
│   ├── README.md
│   ├── API_REFERENCE.md
│   ├── USER_GUIDE.md
│   └── DEVELOPMENT.md
│
├── 📁 scripts/                        # 스크립트
│   ├── setup.py                       # 설치 스크립트
│   ├── migrate.py                     # 마이그레이션 스크립트
│   └── benchmark.py                   # 벤치마크 스크립트
│
├── main.py                            # 메인 엔트리 포인트
├── requirements.txt                   # 의존성
├── pyproject.toml                     # 프로젝트 설정
└── .env.example                       # 환경변수 예시
```

### 🏗️ SOLID 원칙 적용

#### 1. Single Responsibility Principle (SRP)
- **각 클래스는 하나의 책임만 가짐**
- `QAAnalysisService`: QA 분석만 담당
- `ModelService`: 모델 관리만 담당
- `ExportService`: 결과 내보내기만 담당

#### 2. Open/Closed Principle (OCP)
- **확장에는 열려있고 수정에는 닫혀있음**
- 새로운 AI 모델 추가 시 기존 코드 수정 없이 확장 가능
- 인터페이스를 통한 의존성 주입

#### 3. Liskov Substitution Principle (LSP)
- **하위 타입은 상위 타입으로 대체 가능**
- 모든 AI 모델 구현체는 동일한 인터페이스 구현
- 런타임에 모델 교체 가능

#### 4. Interface Segregation Principle (ISP)
- **클라이언트는 사용하지 않는 인터페이스에 의존하지 않음**
- `EmbeddingModelInterface`와 `LLMInterface` 분리
- 필요한 기능만 노출

#### 5. Dependency Inversion Principle (DIP)
- **고수준 모듈은 저수준 모듈에 의존하지 않음**
- Use Case는 인터페이스에만 의존
- 구현체는 의존성 주입으로 제공

### 🔧 주요 개선사항

#### 1. 모델 관리 개선
- **4개 한국어 특화 모델** 사전 설정
- **프로필 기반 모델 전환** 시스템
- **성능 벤치마크** 자동화

#### 2. 설정 관리 개선
- **계층화된 설정** 구조
- **환경별 설정** 분리
- **설정 검증** 자동화

#### 3. 에러 처리 개선
- **커스텀 예외** 체계
- **에러 복구** 메커니즘
- **로깅** 표준화

#### 4. 테스트 개선
- **단위 테스트** 커버리지 90%+
- **통합 테스트** 자동화
- **성능 테스트** 포함

#### 5. 문서화 개선
- **API 문서** 자동 생성
- **사용자 가이드** 제공
- **개발자 문서** 완비

### 🚀 마이그레이션 계획

1. **Phase 1**: 핵심 도메인 모델 구현
2. **Phase 2**: 인터페이스 및 Use Case 구현
3. **Phase 3**: 인프라스트럭처 구현
4. **Phase 4**: 애플리케이션 서비스 구현
5. **Phase 5**: 프레젠테이션 계층 구현
6. **Phase 6**: 테스트 및 문서화

### 📊 예상 효과

- **유지보수성**: 50% 향상
- **확장성**: 새 모델 추가 시간 80% 단축
- **테스트 커버리지**: 90%+ 달성
- **코드 재사용성**: 70% 향상
- **개발 생산성**: 40% 향상

## 🔬 아키텍처 분석 및 평가

### SOLID 원칙 적용 평가

#### 장점
- **높은 응집도, 낮은 결합도**: 각 모듈이 단일 책임을 가져 코드 이해와 유지보수가 용이함
- **확장성**: 인터페이스 기반 설계로 높은 확장성
- **테스트 용이성**: 의존성 주입으로 단위 테스트 용이하나 통합 테스트 보강 필요
- **재사용성**: 도메인 로직 분리로 재사용 용이하나 일부 결합 존재
- **명확한 경계**: 계층 간 책임 분리로 코드 탐색과 디버깅이 용이함

#### 단점
- **초기 복잡성**: 작은 프로젝트에서는 과도한 추상화로 인한 보일러플레이트 코드 증가
- **학습 곡선**: 새로운 개발자의 온보딩 시간이 상대적으로 길어질 수 있음
- **오버엔지니어링 위험**: 미래의 확장성을 위한 과도한 설계가 현재의 복잡성을 증가시킬 수 있음
- **성능 오버헤드**: 추상화 계층이 많아 경미한 성능 저하 가능성 존재
- **문서화 필요성**: 복잡한 구조로 인해 철저한 문서화가 없으면 유지보수 어려움

### 디자인 패턴 개선 방향

#### 현재 적용된 패턴
- **팩토리 패턴**: `model_factory.py`에서 다양한 AI 모델 인스턴스 생성
- **전략 패턴**: 다양한 임베딩/LLM 모델을 동일 인터페이스로 교체 가능
- **파사드 패턴**: `qa_analysis_agent.py`가 복잡한 하위 시스템을 단순화된 인터페이스로 제공
- **레포지토리 패턴**: 데이터 접근 로직을 추상화하여 데이터 소스 변경 용이

#### 추가 적용 가능한 패턴
- **옵저버 패턴**: 분석 결과 변경 시 자동 알림 및 후속 처리
- **데코레이터 패턴**: 로깅, 캐싱, 재시도 등의 횡단 관심사를 기능에 동적 추가
- **커맨드 패턴**: 분석 요청을 객체로 캡슐화하여 큐잉, 지연 실행, 실행 취소 지원
- **미디에이터 패턴**: 여러 모델과 서비스 간의 복잡한 상호작용을 중앙화하여 관리
- **메멘토 패턴**: 분석 상태 저장 및 복원으로 장애 복구 지원

### 코드 품질 평가

#### 구조적 측면
- **모듈화**: ⭐⭐⭐⭐⭐ (5/5) - 명확한 계층 분리와 책임 할당
- **확장성**: ⭐⭐⭐⭐⭐ (5/5) - 인터페이스 기반 설계로 높은 확장성
- **재사용성**: ⭐⭐⭐⭐☆ (4/5) - 도메인 로직 분리로 재사용 용이하나 일부 결합 존재
- **테스트 용이성**: ⭐⭐⭐⭐☆ (4/5) - 의존성 주입으로 단위 테스트 용이하나 통합 테스트 보강 필요

#### 기술적 측면
- **비동기 처리**: ⭐⭐⭐⭐⭐ (5/5) - asyncio 활용한 효율적 비동기 처리
- **오류 처리**: ⭐⭐⭐⭐☆ (4/5) - 커스텀 예외 체계와 복구 메커니즘 구현
- **보안**: ⭐⭐⭐☆☆ (3/5) - 기본적인 보안 조치는 있으나 심층 방어 보강 필요
- **성능 최적화**: ⭐⭐⭐⭐☆ (4/5) - 배치 처리, 병렬화 등 최적화 적용

#### 운영적 측면
- **로깅**: ⭐⭐⭐☆☆ (3/5) - 기본 로깅은 있으나 구조화된 로깅으로 개선 필요
- **모니터링**: ⭐⭐☆☆☆ (2/5) - 기본적인 지표만 있어 종합적 모니터링 체계 필요
- **배포 자동화**: ⭐⭐⭐☆☆ (3/5) - 기본 CI/CD는 있으나 완전 자동화 파이프라인 필요
- **문서화**: ⭐⭐⭐⭐☆ (4/5) - 코드 주석과 기술 문서는 충실하나 API 문서 보강 필요

#### 종합 평가
- **총점**: 44/55 (80%)
- **강점**: 구조적 설계, 확장성, 비동기 처리
- **개선점**: 모니터링, 보안, 배포 자동화

## 🔧 추가 개선 방향

### 1. 아키텍처 & 확장성 강화
- **마이크로서비스화**  
  - 배송, 반품, 상품문의, 감성분석 등 도메인별 독립 서비스로 분리  
  - Docker & Kubernetes로 수평 확장·롤링 업데이트 지원  
- **이벤트 기반 처리**  
  - Kafka/RabbitMQ에 문의 이벤트 발행 → 소비자(Consumer)가 분류·저장  
  - 장애 시 재시도, Dead-letter 큐로 내결함성 강화  
- **CQRS & Event Sourcing**  
  - 읽기·쓰기 분리 최적화  
  - 분류 이벤트 저장으로 감사 추적(audit trail) 구현  
- **캐싱 & CDN**  
  - Redis/Memcached로 자주 조회되는 분류 결과 캐싱  
  - 모델 파일·템플릿은 CDN 배포

### 2. 운영·모니터링·보안
- **통합 로깅·추적**  
  - ELK 스택 또는 Grafana+Loki로 로그 분석  
  - OpenTelemetry 기반 분산 트레이싱(Jaeger, AWS X-Ray)  
- **메트릭 & 알림**  
  - Prometheus+Grafana 대시보드 구축  
  - SLO/SLA 정의 후 Alertmanager로 Slack/PagerDuty 알림  
- **MLOps 자동화**  
  - MLflow/DVC로 모델 버전 관리  
  - Airflow/GitHub Actions로 재학습 파이프라인  
  - 데이터 드리프트 감지 → 재라벨링 → 재학습  
- **보안 강화**  
  - Supabase RLS 세분화(최소 권한 원칙)  
  - API Gateway + JWT 인증·인가, rate-limiting  
  - 취약점 스캔(Dependabot, Snyk), 정기 펜테스트

### 3. 개발 생산성 & 품질 향상
- **인프라 코드화(IaC)**  
  - Terraform/Pulumi로 인프라 프로비저닝  
  - Dev/Stg/Prod 환경별 설정 분리·버전 관리  
- **API 문서 자동화 & 계약 테스트**  
  - FastAPI → OpenAPI(Swagger) 자동 문서  
  - Pact 같은 contract-testing으로 인터페이스 안정화  
  - CI에 코드 커버리지(>90%) & 린터(Black, Flake8) 검증 추가  
- **ADR & 온보딩 문서**  
  - 중요 아키텍처 결정 기록(ADR) 추가  
  - "How to Contribute", "Local Dev Setup" 가이드 강화  
- **테스트 환경 관리**  
  - Synthetic 데이터 Fixtures  
  - Docker-Compose로 Supabase/Redis/MQ 모의 환경 제공

### 4. AI·ML 파이프라인 고도화
- **Active Learning 루프**  
  - 불확실 샘플 자동 태스크 할당 → 관리자 재라벨 → 재학습 반영  
- **모델 앙상블 & 스태킹**  
  - KoELECTRA, RoBERTa, LLM 앙상블 → 메타 분류기 스태킹  
- **A/B 테스트**  
  - 실 트래픽 일부를 실험 모델에 라우팅 → 지표(정확도·지연) 비교  
  - 자동 리포트 후 우수 모델만 프로덕션 배포  
- **Vector DB 업그레이드**  
  - Supabase PGVector → Weaviate/Pinecone 전환  
  - 부분 일치·문맥 확장 semantic search 지원

> 위 항목들을 우선순위에 따라 단계적으로 도입하며 "작은 변화 → 빠른 검증" 사이클을 돌리면,  
> - 서비스 가용성·내결함성  
> - 운영 안정성·모니터링  
> - 개발 효율성·품질  
> - 분류 정확도·일관성  
> 모두 크게 향상시킬 수 있습니다.

## 📝 라이센스

이 프로젝트는 MIT 라이센스 하에 배포됩니다.

## 👥 기여

버그 리포트 및 풀 리퀘스트는 환영합니다. 중요한 변경 사항은 먼저 이슈를 열어 논의해 주세요.

## 📂 상세 프로젝트 구조

```
src/
├── api/                  # API 서비스
│   ├── __init__.py
│   └── classifier_api.py  # FastAPI 기반 분류 API
├── models/               # 데이터 모델
│   ├── __init__.py
│   ├── classification.py  # 분류 결과 모델
│   └── inquiry.py         # 문의 데이터 모델
├── services/             # 비즈니스 로직
│   ├── __init__.py
│   ├── async_inquiry_service.py  # 비동기 문의 서비스
│   ├── inquiry_classifier_service.py  # 분류 서비스
│   └── bulk_processing_service.py  # 대량 처리 서비스
├── utils/                # 유틸리티
│   ├── __init__.py
│   ├── async_utils.py     # 비동기 유틸리티
│   └── logging_utils.py   # 로깅 유틸리티
└── main.py               # 애플리케이션 진입점

config/
├── __init__.py
└── config.py             # 환경 설정

models/                   # 학습된 모델 저장
├── ko_ecommerce_classifier.bin  # 한국어 이커머스 분류 모델
└── embeddings/           # 임베딩 모델

tests/                    # 테스트
├── __init__.py
├── test_classifier.py
└── test_api.py

docs/                     # 문서
├── architecture.md       # 아키텍처 문서
└── implementation_plan.md  # 구현 계획

scripts/                  # 스크립트
├── setup.sh              # 환경 설정 스크립트
└── train_model.py        # 모델 학습 스크립트
```

## 📐 QA Analysis System 아키텍처

### 📁 새로운 폴더 구조 (SOLID 원칙 적용)

```
📁 qa_analysis_system/
├── 📁 core/                          # 핵심 도메인 로직
│   ├── __init__.py
│   ├── 📁 domain/                     # 도메인 모델 (Entity, Value Object)
│   │   ├── __init__.py
│   │   ├── qa_pair.py                 # QA 쌍 도메인 모델
│   │   ├── analysis_result.py         # 분석 결과 도메인 모델
│   │   └── inquiry.py                 # 문의 도메인 모델
│   ├── 📁 interfaces/                 # 인터페이스 (추상화)
│   │   ├── __init__.py
│   │   ├── ai_model_interface.py      # AI 모델 인터페이스
│   │   ├── repository_interface.py    # 저장소 인터페이스
│   │   └── analyzer_interface.py      # 분석기 인터페이스
│   └── 📁 use_cases/                  # 비즈니스 로직 (Use Case)
│       ├── __init__.py
│       ├── analyze_qa_pair.py         # QA 쌍 분석 유스케이스
│       ├── batch_analysis.py          # 배치 분석 유스케이스
│       └── model_management.py        # 모델 관리 유스케이스
│
├── 📁 infrastructure/                 # 외부 시스템 연동
│   ├── __init__.py
│   ├── 📁 ai_models/                  # AI 모델 구현체
│   │   ├── __init__.py
│   │   ├── 📁 embedding/              # 임베딩 모델들
│   │   │   ├── __init__.py
│   │   │   ├── kr_sbert.py            # KR-SBERT 구현
│   │   │   ├── kcelectra.py           # KcELECTRA 구현
│   │   │   ├── kobert.py              # KoBERT 구현
│   │   │   └── klue_roberta.py        # KLUE-RoBERTa 구현
│   │   ├── 📁 llm/                    # LLM 모델들
│   │   │   ├── __init__.py
│   │   │   ├── openai_gpt.py          # OpenAI GPT 구현
│   │   │   ├── anthropic_claude.py    # Anthropic Claude 구현
│   │   │   └── google_gemini.py       # Google Gemini 구현
│   │   └── model_factory.py           # 모델 팩토리
│   ├── 📁 repositories/               # 데이터 저장소 구현
│   │   ├── __init__.py
│   │   ├── supabase_repository.py     # Supabase 저장소
│   │   └── file_repository.py         # 파일 저장소
│   └── 📁 external_apis/              # 외부 API 연동
│       ├── __init__.py
│       └── supabase_client.py         # Supabase 클라이언트
│
├── 📁 application/                    # 애플리케이션 서비스
│   ├── __init__.py
│   ├── 📁 services/                   # 애플리케이션 서비스
│   │   ├── __init__.py
│   │   ├── qa_analysis_service.py     # QA 분석 서비스
│   │   ├── model_service.py           # 모델 관리 서비스
│   │   └── export_service.py          # 결과 내보내기 서비스
│   ├── 📁 agents/                     # 에이전트 (Facade 패턴)
│   │   ├── __init__.py
│   │   └── qa_analysis_agent.py       # QA 분석 에이전트
│   └── 📁 dto/                        # 데이터 전송 객체
│       ├── __init__.py
│       ├── analysis_request.py        # 분석 요청 DTO
│       └── analysis_response.py       # 분석 응답 DTO
│
├── 📁 presentation/                   # 프레젠테이션 계층
│   ├── __init__.py
│   ├── 📁 cli/                        # CLI 인터페이스
│   │   ├── __init__.py
│   │   ├── main_cli.py                # 메인 CLI
│   │   └── commands/                  # CLI 명령어들
│   │       ├── __init__.py
│   │       ├── analyze_command.py
│   │       └── config_command.py
│   └── 📁 api/                        # API 인터페이스 (향후 확장)
│       ├── __init__.py
│       └── rest_api.py
│
├── 📁 config/                         # 설정 관리
│   ├── __init__.py
│   ├── 📁 settings/                   # 설정 파일들
│   │   ├── __init__.py
│   │   ├── model_profiles.yaml        # 모델 프로필 설정
│   │   ├── analysis_config.yaml       # 분석 설정
│   │   └── app_config.yaml            # 애플리케이션 설정
│   ├── 📁 managers/                   # 설정 관리자들
│   │   ├── __init__.py
│   │   ├── config_manager.py          # 통합 설정 관리자
│   │   └── model_config_manager.py    # 모델 설정 관리자
│   └── 📁 validators/                 # 설정 검증기
│       ├── __init__.py
│       └── config_validator.py
│
├── 📁 shared/                         # 공통 유틸리티
│   ├── __init__.py
│   ├── 📁 utils/                      # 유틸리티 함수들
│   │   ├── __init__.py
│   │   ├── async_utils.py             # 비동기 유틸리티
│   │   ├── text_utils.py              # 텍스트 처리 유틸리티
│   │   └── validation_utils.py        # 검증 유틸리티
│   ├── 📁 exceptions/                 # 커스텀 예외
│   │   ├── __init__.py
│   │   ├── model_exceptions.py        # 모델 관련 예외
│   │   └── analysis_exceptions.py     # 분석 관련 예외
│   └── 📁 constants/                  # 상수 정의
│       ├── __init__.py
│       ├── model_constants.py         # 모델 관련 상수
│       └── analysis_constants.py      # 분석 관련 상수
│
├── 📁 tests/                          # 테스트 코드
│   ├── __init__.py
│   ├── 📁 unit/                       # 단위 테스트
│   │   ├── __init__.py
│   │   ├── test_domain/
│   │   ├── test_use_cases/
│   │   └── test_services/
│   ├── 📁 integration/                # 통합 테스트
│   │   ├── __init__.py
│   │   └── test_agents/
│   └── 📁 fixtures/                   # 테스트 데이터
│       ├── __init__.py
│       └── sample_data.py
│
├── 📁 docs/                           # 문서화
│   ├── README.md
│   ├── API_REFERENCE.md
│   ├── USER_GUIDE.md
│   └── DEVELOPMENT.md
│
├── 📁 scripts/                        # 스크립트
│   ├── setup.py                       # 설치 스크립트
│   ├── migrate.py                     # 마이그레이션 스크립트
│   └── benchmark.py                   # 벤치마크 스크립트
│
├── main.py                            # 메인 엔트리 포인트
├── requirements.txt                   # 의존성
├── pyproject.toml                     # 프로젝트 설정
└── .env.example                       # 환경변수 예시
```

### 🏗️ SOLID 원칙 적용

#### 1. Single Responsibility Principle (SRP)
- **각 클래스는 하나의 책임만 가짐**
- `QAAnalysisService`: QA 분석만 담당
- `ModelService`: 모델 관리만 담당
- `ExportService`: 결과 내보내기만 담당

#### 2. Open/Closed Principle (OCP)
- **확장에는 열려있고 수정에는 닫혀있음**
- 새로운 AI 모델 추가 시 기존 코드 수정 없이 확장 가능
- 인터페이스를 통한 의존성 주입

#### 3. Liskov Substitution Principle (LSP)
- **하위 타입은 상위 타입으로 대체 가능**
- 모든 AI 모델 구현체는 동일한 인터페이스 구현
- 런타임에 모델 교체 가능

#### 4. Interface Segregation Principle (ISP)
- **클라이언트는 사용하지 않는 인터페이스에 의존하지 않음**
- `EmbeddingModelInterface`와 `LLMInterface` 분리
- 필요한 기능만 노출

#### 5. Dependency Inversion Principle (DIP)
- **고수준 모듈은 저수준 모듈에 의존하지 않음**
- Use Case는 인터페이스에만 의존
- 구현체는 의존성 주입으로 제공

### 🔧 주요 개선사항

#### 1. 모델 관리 개선
- **4개 한국어 특화 모델** 사전 설정
- **프로필 기반 모델 전환** 시스템
- **성능 벤치마크** 자동화

#### 2. 설정 관리 개선
- **계층화된 설정** 구조
- **환경별 설정** 분리
- **설정 검증** 자동화

#### 3. 에러 처리 개선
- **커스텀 예외** 체계
- **에러 복구** 메커니즘
- **로깅** 표준화

#### 4. 테스트 개선
- **단위 테스트** 커버리지 90%+
- **통합 테스트** 자동화
- **성능 테스트** 포함

#### 5. 문서화 개선
- **API 문서** 자동 생성
- **사용자 가이드** 제공
- **개발자 문서** 완비

### 🚀 마이그레이션 계획

1. **Phase 1**: 핵심 도메인 모델 구현
2. **Phase 2**: 인터페이스 및 Use Case 구현
3. **Phase 3**: 인프라스트럭처 구현
4. **Phase 4**: 애플리케이션 서비스 구현
5. **Phase 5**: 프레젠테이션 계층 구현
6. **Phase 6**: 테스트 및 문서화

### 📊 예상 효과

- **유지보수성**: 50% 향상
- **확장성**: 새 모델 추가 시간 80% 단축
- **테스트 커버리지**: 90%+ 달성
- **코드 재사용성**: 70% 향상
- **개발 생산성**: 40% 향상
