# 문의 데이터 처리 시스템

이 시스템은 Supabase 데이터베이스를 활용하여 온라인 쇼핑몰의 고객 문의 데이터를 효율적으로 처리, 분류, 관리하는 Python 기반 애플리케이션입니다. 비동기 처리 방식을 적용하여 대규모 데이터를 빠르게 처리하며, AI 기반 문의 분류 기능을 통해 셀러와 구매자의 문의를 자동으로 분류합니다.

## 🚀 주요 기능

1. **기본 문의 데이터 조회 (동기식)**: 
   - 일반적인 동기 방식으로 문의 데이터를 조회하고 요약 정보 제공
   - 간단하고 직관적인 인터페이스

2. **비동기 문의 데이터 처리**:
   - `asyncio`를 활용한 비동기 처리로 I/O 작업 효율성 극대화
   - 다수의 문의를 병렬로 처리하여 속도 향상

3. **대규모 문의 데이터 일괄 처리**:
   - 수백~수천 개의 문의 데이터를 효율적으로 처리
   - 배치 처리 및 작업 분할을 통한 성능 최적화

4. **여러 테이블 통합 처리**:
   - 다양한 데이터 소스(테이블)의 문의 데이터 통합 처리
   - 일관된 형식으로 변환 및 표시

5. **한국어 AI 분류 시스템**:
   - 임베딩 모델과 LLM을 활용한 문의 내용 자동 분류
   - 카테고리별(배송, 반품, 교환, 상품 정보 등) 문의 분류


## 📋 프로젝트 구조

```
project_root/
├── config/                  # 설정 관련 파일
│   └── config.py            # Supabase 및 API 키 설정
├── database/                # 데이터베이스 관련 모듈
├── src/                     # 소스 코드
│   ├── models/              # 데이터 모델
│   │   └── inquiry.py       # 문의 데이터 모델 정의
│   ├── services/            # 비즈니스 로직 서비스
│   │   ├── async_inquiry_runner.py      # 비동기 실행 모듈
│   │   ├── async_inquiry_service.py     # 비동기 문의 서비스
│   │   ├── batch_service.py             # 배치 처리 서비스
│   │   ├── bulk_processing_service.py   # 대량 데이터 처리
│   │   ├── embedding_model_service.py   # 임베딩 모델 서비스
│   │   ├── inquiry_classifier_service.py # 문의 분류 서비스
│   │   ├── inquiry_service.py            # 기본 문의 서비스
│   │   ├── llm_service.py                # LLM 활용 서비스
│   │   └── multi_table_inquiry_service.py # 다중 테이블 서비스
│   └── utils/               # 유틸리티 함수
│       └── async_utils.py   # 비동기 유틸리티
└── main.py                  # 메인 실행 파일
```

## 📐 시스템 아키텍처

### 개요

이 시스템은 다층 아키텍처를 채택하여 관심사 분리와 모듈화를 강조했습니다:

```
┌─────────────────┐
│     UI 계층     │ - 사용자 인터페이스 (CLI)
└────────┬────────┘
         │
┌────────▼────────┐
│   서비스 계층    │ - 비즈니스 로직 구현
└────────┬────────┘
         │
┌────────▼────────┐
│   데이터 계층    │ - 데이터 액세스 및 모델
└────────┬────────┘
         │
┌────────▼────────┐
│   외부 시스템    │ - Supabase, OpenAI API
└─────────────────┘
```

### 컴포넌트 상호작용

1. **사용자 인터페이스 (UI 계층)**:
   - `main.py`: 사용자 입력을 받아 적절한 서비스 호출
   - 결과 포맷팅 및 표시 담당

2. **서비스 계층**:
   - `inquiry_service.py`: 기본 문의 처리 로직
   - `async_inquiry_service.py`: 비동기 문의 처리 로직
   - `embedding_model_service.py` & `llm_service.py`: AI 분류 로직
   - `batch_service.py` & `bulk_processing_service.py`: 대량 처리 로직

3. **데이터 계층**:
   - `inquiry.py`: Pydantic 기반 객체-관계 매핑
   - Supabase 클라이언트: 데이터 CRUD 작업

4. **외부 시스템**:
   - Supabase: 문의 데이터 저장소
   - OpenAI API: LLM 기반 분류 (선택적)

## 💻 기술 스택

### 핵심 기술

- **Python 3.11+**: 
  - 최신 타입 힌팅 기능 활용
  - 비동기 프로그래밍 지원 (`asyncio`)
  - 구조적 패턴 매칭 활용

- **Supabase**: 
  - PostgreSQL 기반의 백엔드 데이터베이스
  - RESTful API 인터페이스
  - 실시간 구독 기능 (미래 확장성)
  - Row-level 보안 정책 (RLS)

- **asyncio 생태계**:
  - `asyncio`: 비동기 I/O 작업 관리
  - 커스텀 데코레이터: 실행 시간 측정, 오류 처리 등
  - 동시성 제어: 세마포어, 락, 이벤트 등

- **Pydantic**: 
  - 데이터 검증 및 설정 관리
  - JSON 직렬화/역직렬화
  - 타입 강제 및 변환

### AI/ML 기술

- **임베딩 모델**:
  - 한국어 텍스트 벡터화
  - 유사도 기반 문의 분류
  - 가벼운 CPU 기반 추론

- **OpenAI API** (선택적):
  - GPT 모델 기반 고급 문의 분류
  - 문맥 인식 답변 생성
  - 복잡한 문의 이해 및 분석

### 개발 도구

- **환경 관리**: 
  - Python venv 또는 conda
  - dotenv를 통한 환경 변수 관리

- **코드 품질**:
  - Type hints 및 mypy
  - Black, flake8, isort를 통한 코드 포맷팅
  - pytest 기반 테스트 자동화

## 🔍 데이터 모델

### 문의 모델 (Inquiry)

기본 모델 구조:

```python
class Inquiry(BaseModel):
    id: str                              # 문의 ID
    post_id: str                         # 게시물 ID  
    user_id: Optional[str] = None        # 사용자 ID
    content: str                         # 문의 내용
    subject: Optional[str] = None        # 제목
    created_at: datetime                 # 작성일
    is_answered: bool = False            # 답변 여부
    answer_content: Optional[str] = None # 답변 내용
    answered_at: Optional[datetime] = None # 답변일
    metadata: Optional[Dict] = None      # 추가 메타데이터
```

### 데이터베이스 스키마

Supabase에서 다음과 같은 테이블 구조를 사용합니다:

1. **`personal_inquiries`**: 고객 개인 문의
   - 고객 관련 개인 문의 (배송, 환불, 계정 관련 등)
   - 고객 정보와 연결됨

2. **`product_inquiries`**: 상품 관련 문의
   - 상품 정보, 사용법, 재입고 등 관련 문의
   - 상품 ID와 연결됨

3. **`shipping_inquiry`**: 배송 관련 문의
   - 배송 상태, 지연, 분실 등에 관한 문의
   - 주문 ID와 연결됨

4. **`classification_results`**: 분류 결과 저장
   - AI 분류 결과 및 신뢰도 점수 저장
   - 문의 ID와 연결됨

5. **`classification_logs`**: 분류 로그
   - 분류 과정 로깅 및 디버깅용
   - 성능 및 정확도 모니터링

## ⚙️ 설치 및 설정

1. 저장소 클론 및 가상환경 설정:

```bash
git clone <repository-url>
cd POC
python -m venv .venv
source .venv/bin/activate  # Windows: .venv\Scripts\activate
pip install -r requirements.txt
```

2. Supabase 설정:
   - `.env` 파일 생성 (또는 `config/config.py` 수정):

```
SUPABASE_URL=your_supabase_url
SUPABASE_KEY=your_supabase_key
OPENAI_API_KEY=your_openai_key  # 선택사항
```

3. 실행:

```bash
python main.py
```

## 🧩 핵심 구성 요소 상세 설명

### 1. 비동기 데이터 처리

시스템의 핵심은 `asyncio`를 활용한 효율적인 I/O 처리입니다. 이는 Supabase API 호출과 같은 네트워크 I/O 작업이 많은 경우에 특히 중요합니다.

#### 핵심 비동기 유틸리티

```python
# src/utils/async_utils.py

def async_timed():
    """비동기 함수 실행 시간 측정 데코레이터"""
    def wrapper(func):
        @functools.wraps(func)
        async def wrapped(*args, **kwargs):
            start = time.time()
            try:
                return await func(*args, **kwargs)
            finally:
                end = time.time()
                total = end - start
                print(f"{func.__name__} 완료: {total:.4f}초")
        return wrapped
    return wrapper

def run_async_in_thread(func):
    """비동기 함수를 동기적으로 실행하는 데코레이터"""
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        # 다양한 이벤트 루프 상황 처리 로직
        # ...
    return wrapper

async def batch_process(
    items: List[T],
    process_func: Callable[[T], Any],
    batch_size: int = 10
) -> List[Any]:
    """항목 목록을 배치로 비동기 처리"""
    # 배치 처리 로직
    # ...
```

#### 비동기 서비스 구현

```python
# src/services/async_inquiry_service.py (일부)

class AsyncInquiryService:
    """비동기 문의 서비스"""
    
    @async_timed()
    async def get_all_inquiries(self, table: str = 'product', limit: int = 100) -> List[Dict]:
        """모든 문의 비동기 조회"""
        # Supabase 비동기 쿼리 구성 및 실행
        # ...
```

### 2. 대규모 데이터 처리

대용량 데이터 처리를 위해 다음과 같은 전략을 사용합니다:

#### 배치 처리 설정

```python
# src/services/bulk_processing_service.py (일부)

class BatchProcessConfig:
    """배치 처리 설정"""
    batch_size: int = 30          # 배치당 최대 항목 수
    max_concurrent_batches: int = 5  # 최대 동시 배치 처리 수
    timeout_seconds: float = 60.0  # API 요청 타임아웃
```

#### 병렬 처리 구현

```python
# src/services/batch_service.py (일부)

async def process_bulk_inquiries(self, items, process_func):
    """대량의 문의를 병렬로 처리"""
    
    # 배치 분할
    batches = self._split_into_batches(items, self.config.batch_size)
    
    # 세마포어로 동시 처리 제한
    semaphore = asyncio.Semaphore(self.config.max_concurrent_batches)
    
    # 배치별 처리 태스크 생성
    tasks = []
    for batch in batches:
        task = self._process_batch_with_semaphore(
            semaphore, batch, process_func
        )
        tasks.append(task)
    
    # 모든 태스크 실행 및 결과 수집
    results = await asyncio.gather(*tasks, return_exceptions=True)
    
    # 결과 종합 및 분석
    # ...
```

### 3. AI 기반 문의 분류

두 가지 방식의 AI 분류 시스템을 구현했습니다:

#### 임베딩 기반 분류

```python
# src/services/embedding_model_service.py (구조)

class EmbeddingModelService:
    """임베딩 모델 기반 문의 분류 서비스"""
    
    def __init__(self):
        """임베딩 모델 로드 및 초기화"""
        # 사전 정의된 카테고리 및 임베딩 설정
        self.categories = {
            "배송": ["배송", "배달", "택배", "언제 오나요", "도착"], 
            "교환/환불": ["환불", "교환", "반품", "돌려받고", "맞교환"],
            "상품문의": ["상품", "제품", "사이즈", "색상", "재질"],
            "결제문의": ["결제", "카드", "무통장", "입금", "포인트"],
            "재고문의": ["재고", "품절", "입고", "언제 들어오나요"],
            "취소문의": ["취소", "주문취소", "구매취소"]
        }
        
        # 모델 초기화 (예: KoBERT, sentence-transformers 등)
        # ...
    
    def _compute_embedding(self, text: str) -> List[float]:
        """텍스트를 임베딩 벡터로 변환"""
        # ...
    
    def classify_text(self, text: str) -> Tuple[str, float]:
        """문의 텍스트를 분류하고 신뢰도 점수 반환"""
        # 입력 텍스트 임베딩 계산
        # 사전 정의된 카테고리와 유사도 계산
        # 최고 점수 카테고리 및 신뢰도 반환
        # ...
```

#### LLM 기반 분류

```python
# src/services/llm_service.py (구조)

class LLMService:
    """LLM 기반 문의 분류 서비스"""
    
    def __init__(self):
        """OpenAI API 클라이언트 초기화"""
        # ...
    
    async def classify_inquiry(self, text: str) -> Tuple[str, float]:
        """OpenAI API를 사용하여 문의 분류"""
        # 분류 프롬프트 생성
        prompt = self._create_classification_prompt(text)
        
        # API 호출 및 응답 파싱
        response = await self._call_openai_api(prompt)
        
        # 결과 추출 및 신뢰도 점수 계산
        category, confidence = self._parse_classification_response(response)
        
        return category, confidence
```

### 4. 다중 테이블 통합 처리

다양한 문의 테이블의 데이터를 통합하여 일관된 형식으로 처리합니다:

```python
# src/services/multi_table_inquiry_service.py (일부)

class MultiTableInquiryService:
    """여러 테이블의 문의 데이터 통합 처리 서비스"""
    
    async def get_unified_inquiry_list(self, limit_per_table: int = 20) -> List[Dict]:
        """여러 테이블에서 문의 데이터를 통합 조회"""
        
        # 각 테이블에서 데이터 병렬 조회
        tasks = []
        for table_key in ["personal", "product", "shipping"]:
            task = self._get_inquiries_from_table(table_key, limit_per_table)
            tasks.append(task)
        
        results = await asyncio.gather(*tasks)
        
        # 결과 통합 및 표준화
        unified_inquiries = []
        for table_results in results:
            for item in table_results:
                # 표준 형식으로 변환
                standardized = self._standardize_inquiry(item)
                unified_inquiries.append(standardized)
        
        # 날짜 기준 정렬
        unified_inquiries.sort(
            key=lambda x: x.get("created_at", ""), reverse=True
        )
        
        return unified_inquiries
```

## 📊 성능 및 최적화

### 성능 메트릭

시스템 성능을 다음과 같은 메트릭으로 측정합니다:

1. **처리 시간**:
   - 전체 처리 소요 시간
   - 문의 항목당 평균 처리 시간 (ms)

2. **처리량**:
   - 초당 처리 문의 수 (QPS)
   - 배치 크기에 따른 확장성

3. **메모리 사용량**:
   - 대규모 처리 시 메모리 사용 패턴
   - 메모리 누수 모니터링

4. **분류 정확도**:
   - AI 분류 모델의 정확도 (%)
   - 오분류 비율 및 패턴

### 최적화 전략

1. **연결 풀링**:
   - Supabase 클라이언트 연결 재사용
   - 세션 관리 최적화

2. **데이터 캐싱**:
   - 자주 조회하는 데이터 메모리 캐싱
   - 중복 요청 감소

3. **배치 크기 튜닝**:
   - 워크로드에 맞는 최적 배치 크기 결정
   - 동시성 수준 조정

4. **비동기 최적화**:
   - 적절한 비동기 패턴 적용
   - I/O 바운드 작업 병렬화

## 🔧 오류 처리 및 복원력

### 구현된 오류 처리 메커니즘

1. **재시도 로직**:
   ```python
   async def retry_async(
       func: Callable,
       max_retries: int = 3,
       delay: float = 1.0,
       backoff_factor: float = 2.0
   ):
       """비동기 함수 재시도 실행"""
       last_exception = None
       current_delay = delay

       for attempt in range(max_retries + 1):
           try:
               return await func()
           except Exception as e:
               last_exception = e
               if attempt < max_retries:
                   print(f"시도 {attempt + 1} 실패: {e}, {current_delay}초 후 재시도...")
                   await asyncio.sleep(current_delay)
                   current_delay *= backoff_factor
               else:
                   print(f"모든 재시도 실패. 마지막 오류: {e}")
                   raise last_exception
   ```

2. **타임아웃 처리**:
   ```python
   async def run_with_timeout(coro, timeout_seconds: float = 30.0):
       """타임아웃과 함께 코루틴 실행"""
       try:
           return await asyncio.wait_for(coro, timeout=timeout_seconds)
       except asyncio.TimeoutError:
           print(f"작업이 {timeout_seconds}초 내에 완료되지 않았습니다.")
           raise
   ```

3. **부분 실패 허용**:
   - 개별 항목 실패가 전체 처리를 중단하지 않음
   - 오류 항목 로깅 및 보고

## 🔮 개선 방향 및 로드맵

### 단기 개선 사항

1. **데이터 검증 강화**:
   - 입력 값 검증 및 오류 메시지 개선
   - 데이터 불일치 자동 감지 및 수정

2. **로깅 시스템 강화**:
   - 구조화된 로깅 구현
   - 오류 추적 및 디버깅 용이성 개선

3. **테스트 커버리지 확대**:
   - 단위 테스트 추가
   - 통합 테스트 시나리오 구현

### 중기 개선 사항

1. **웹 인터페이스 개발**:
   - FastAPI 또는 Flask 기반 웹 API
   - React 또는 Vue 기반 관리 대시보드

2. **실시간 알림 구현**:
   - 중요 문의 실시간 알림
   - Supabase 실시간 구독 활용

3. **한국어 임베딩 모델 개선**:
   - 도메인 특화 데이터로 미세조정
   - 분류 정확도 향상

### 장기 비전

1. **자동 답변 생성**:
   - 반복적인 문의에 대한 템플릿 기반 답변
   - LLM 활용 맞춤형 답변 생성

2. **고객 감정 분석**:
   - 문의 텍스트에서 감정 추출
   - 우선순위 지정 및 대응 전략 수립

3. **예측 모델 도입**:
   - 문의 추세 예측
   - 피크 시간대 자원 배분 최적화

## ⚙️ 설치 및 설정

### 1. 요구사항

- Python 3.11 이상
- pip 또는 poetry
- git
- Supabase 계정 및 프로젝트

### 2. 세부 설정 단계

1. **저장소 클론**:
   ```bash
   git clone <repository-url>
   cd POC
   ```

2. **가상환경 설정**:
   ```bash
   python -m venv .venv
   source .venv/bin/activate  # Windows: .venv\Scripts\activate
   ```

3. **의존성 설치**:
   ```bash
   pip install -r requirements.txt
   ```

4. **환경 설정**:
   - `.env` 파일 생성:
   ```
   SUPABASE_URL=your_supabase_url
   SUPABASE_KEY=your_supabase_key
   OPENAI_API_KEY=your_openai_key  # 선택사항
   ```

5. **데이터베이스 테이블 설정**:
   - Supabase에서 다음 테이블 생성:
     - `personal_inquiries`
     - `product_inquiries`
     - `shipping_inquiry`
     - `classification_results`
     - `classification_logs`

6. **실행**:
   ```bash
   python main.py
   ```

## 📚 참조 및 리소스

### 관련 기술 문서

- [Supabase Python 클라이언트](https://supabase.io/docs/reference/python/introduction)
- [asyncio 공식 문서](https://docs.python.org/3/library/asyncio.html)
- [Pydantic 문서](https://pydantic-docs.helpmanual.io/)
- [OpenAI API 문서](https://platform.openai.com/docs/api-reference)

### 추가 학습 자료

- Python 비동기 프로그래밍:
  - [Python asyncio: The Complete Guide](https://realpython.com/async-io-python/)
  - [Asyncio for the Working Python Developer](https://testdriven.io/blog/concurrency-parallelism-asyncio/)

- 한국어 자연어 처리:
  - [KoBERT 모델](https://github.com/SKTBrain/KoBERT)
  - [한국어 임베딩](https://github.com/ratsgo/embedding)

## 📝 라이센스

이 프로젝트는 MIT 라이센스 하에 배포됩니다.

## 👥 기여

버그 리포트 및 풀 리퀘스트는 환영합니다. 중요한 변경 사항은 먼저 이슈를 열어 논의해 주세요.
