"""
QA Analysis System v2.0 - 새로운 모듈화 구조
SOLID 원칙 적용, 한국어 특화 모델 4개 지원, GPT-4o-mini 적용
"""
import sys
import asyncio
from pathlib import Path

# 프로젝트 루트를 Python 경로에 추가
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from qa_analysis_system.config.managers.config_manager import get_config_manager
from qa_analysis_system.infrastructure.ai_models.model_factory import get_model_factory


def display_welcome():
    """환영 메시지 출력"""
    print("=" * 80)
    print("🚀 QA Analysis System v2.0")
    print("=" * 80)
    print("📊 한국어 이커머스 문의 분석 시스템")
    print("🤖 4개 한국어 특화 모델 + GPT-4o-mini 지원")
    print("⚡ SOLID 원칙 적용 모듈화 구조")
    print("=" * 80)


def display_main_menu():
    """메인 메뉴 출력"""
    print("\n🎯 사용할 기능을 선택하세요:")
    print("-" * 50)
    print("1. 📊 QA 쌍 분석 (개별)")
    print("2. 📈 QA 배치 분석")
    print("3. 🏷️ 질문-답변 태깅")
    print("4. ⚙️ 모델 설정 관리")
    print("5. 🔧 프로필 관리")
    print("6. 📋 시스템 정보")
    print("7. 🧪 모델 테스트")
    print("0. 🚪 종료")
    print("-" * 50)


def handle_qa_analysis():
    """QA 쌍 분석 처리"""
    print("\n📊 QA 쌍 분석")
    print("=" * 40)
    
    # 분석 타입 선택
    print("분석 타입을 선택하세요:")
    print("1. 전체 분석 (유사도 + 품질)")
    print("2. 유사도 분석만")
    print("3. 품질 분석만")
    
    try:
        analysis_type_choice = int(input("선택 (1-3): "))
        analysis_types = {1: "full", 2: "similarity_only", 3: "quality_only"}
        analysis_type = analysis_types.get(analysis_type_choice, "full")
    except ValueError:
        analysis_type = "full"
    
    # 입력 방식 선택
    print("\n입력 방식을 선택하세요:")
    print("1. 문의 ID로 분석")
    print("2. 직접 텍스트 입력")
    
    try:
        input_choice = int(input("선택 (1-2): "))
    except ValueError:
        input_choice = 1
    
    if input_choice == 1:
        inquiry_id = input("문의 ID를 입력하세요: ").strip()
        if inquiry_id:
            return run_qa_analysis_by_id(inquiry_id, analysis_type)
    else:
        question = input("질문을 입력하세요: ").strip()
        answer = input("답변을 입력하세요: ").strip()
        if question:
            return run_qa_analysis_by_text(question, answer, analysis_type)
    
    print("❌ 올바른 입력이 필요합니다.")
    return 1


def run_qa_analysis_by_id(inquiry_id: str, analysis_type: str):
    """ID로 QA 분석 실행"""
    try:
        from qa_analysis_system.application.agents.qa_analysis_agent import QAAnalysisAgent
        from qa_analysis_system.application.dto.analysis_request import AnalysisRequest
        from config.config import SUPABASE_URL, SUPABASE_KEY
        
        async def analyze():
            print(f"🔍 문의 ID '{inquiry_id}' 분석 중...")
            
            # 에이전트 생성
            agent = QAAnalysisAgent(
                config_manager=get_config_manager(),
                supabase_url=SUPABASE_URL,
                supabase_key=SUPABASE_KEY
            )
            
            # 분석 요청
            request = AnalysisRequest(
                inquiry_id=inquiry_id,
                analysis_type=analysis_type,
                include_feedback=True
            )
            
            # 분석 실행
            result = await agent.analyze_qa_pair(request)
            
            # 결과 출력
            print_analysis_result(result)
            
            # JSON 저장
            agent.export_results([result], f"qa_analysis_{inquiry_id}.json")
            
            return 0
        
        return asyncio.run(analyze())
        
    except Exception as e:
        print(f"❌ 분석 중 오류: {e}")
        return 1


def run_qa_analysis_by_text(question: str, answer: str, analysis_type: str):
    """텍스트로 QA 분석 실행"""
    try:
        from qa_analysis_system.application.agents.qa_analysis_agent import QAAnalysisAgent
        from qa_analysis_system.application.dto.analysis_request import AnalysisRequest
        import uuid
        
        async def analyze():
            print(f"🔍 텍스트 분석 중...")
            
            # 에이전트 생성
            agent = QAAnalysisAgent(config_manager=get_config_manager())
            
            # 분석 요청
            request = AnalysisRequest(
                inquiry_id=str(uuid.uuid4()),
                question_text=question,
                answer_text=answer,
                analysis_type=analysis_type,
                include_feedback=True
            )
            
            # 분석 실행
            result = await agent.analyze_qa_pair(request)
            
            # 결과 출력
            print_analysis_result(result)
            
            return 0
        
        return asyncio.run(analyze())
        
    except Exception as e:
        print(f"❌ 분석 중 오류: {e}")
        return 1


def print_analysis_result(result):
    """분석 결과 출력"""
    print("\n" + "=" * 60)
    print("📊 QA 분석 결과")
    print("=" * 60)
    
    print(f"🆔 문의 ID: {result.inquiry_id}")
    print(f"❓ 질문: {result.question_text[:100]}...")
    print(f"💬 답변: {result.answer_text[:100]}..." if result.answer_text else "💬 답변: (없음)")
    
    print(f"\n📈 분석 점수:")
    print(f"  • 의미적 유사도: {result.semantic_similarity:.3f}")
    print(f"  • 주제 관련성: {result.topic_relevance:.3f}")
    print(f"  • 키워드 겹침: {result.keyword_overlap:.3f}")
    print(f"  • 답변 완성도: {result.answer_completeness:.3f}")
    print(f"  • 답변 정확성: {result.answer_accuracy:.3f}")
    print(f"  • 답변 도움도: {result.answer_helpfulness:.3f}")
    
    print(f"\n🎯 종합 결과:")
    print(f"  • 전체 점수: {result.overall_score:.3f}")
    print(f"  • 등급: {result.grade}")
    print(f"  • 통과 여부: {'✅ 통과' if result.pass_threshold else '❌ 미통과'}")
    
    if result.strengths:
        print(f"\n💪 강점:")
        for strength in result.strengths:
            print(f"  • {strength}")
    
    if result.weaknesses:
        print(f"\n⚠️ 약점:")
        for weakness in result.weaknesses:
            print(f"  • {weakness}")
    
    if result.recommendations:
        print(f"\n💡 개선사항:")
        for rec in result.recommendations:
            print(f"  • {rec}")
    
    print(f"\n⏱️ 처리 시간: {result.processing_time_ms:.2f}ms")
    print(f"🤖 사용 모델: {result.model_info}")
    print("=" * 60)


def handle_batch_analysis():
    """배치 분석 처리"""
    print("\n📈 QA 배치 분석")
    print("=" * 40)
    
    try:
        limit = int(input("분석할 문의 수를 입력하세요 (기본: 20): ") or "20")
    except ValueError:
        limit = 20
    
    return run_batch_analysis(limit)


def run_batch_analysis(limit: int):
    """배치 분석 실행"""
    try:
        from qa_analysis_system.application.agents.qa_analysis_agent import QAAnalysisAgent
        from qa_analysis_system.application.dto.analysis_request import AnalysisRequest
        from config.config import SUPABASE_URL, SUPABASE_KEY
        
        async def analyze():
            print(f"📊 {limit}건의 문의 배치 분석 중...")
            
            # 에이전트 생성
            agent = QAAnalysisAgent(
                config_manager=get_config_manager(),
                supabase_url=SUPABASE_URL,
                supabase_key=SUPABASE_KEY
            )
            
            # 샘플 ID들 (실제로는 DB에서 조회)
            sample_ids = [
                "b958b301-7aba-4521-999c-019e13b24403",
                "68d308a7-69ea-4c95-b71f-0ab6b0de5452",
                "91db3f8d-2294-4804-8581-dab0079520cc",
                "33d84df6-14c7-47f7-9025-cd4a7e4d0c7a",
                "3484f661-2025-44ca-a766-aeceaa9619f9"
            ]
            
            # 요청 생성
            requests = [
                AnalysisRequest(
                    inquiry_id=inquiry_id,
                    analysis_type="full",
                    include_feedback=True
                )
                for inquiry_id in sample_ids[:limit]
            ]
            
            # 배치 분석 실행
            results = await agent.analyze_multiple_pairs(requests)
            
            # 결과 요약 출력
            print_batch_summary(results)
            
            # JSON 저장
            agent.export_results(results, "batch_analysis_results.json")
            
            return 0
        
        return asyncio.run(analyze())
        
    except Exception as e:
        print(f"❌ 배치 분석 중 오류: {e}")
        return 1


def print_batch_summary(results):
    """배치 분석 결과 요약 출력"""
    if not results:
        print("❌ 분석 결과가 없습니다.")
        return
    
    total = len(results)
    passed = sum(1 for r in results if r.pass_threshold)
    avg_score = sum(r.overall_score for r in results) / total
    
    print("\n" + "=" * 60)
    print("📊 배치 분석 결과 요약")
    print("=" * 60)
    print(f"📈 총 분석: {total}건")
    print(f"✅ 통과: {passed}건 ({passed/total*100:.1f}%)")
    print(f"📊 평균 점수: {avg_score:.3f}")
    
    # 등급별 분포
    grade_counts = {}
    for result in results:
        grade_counts[result.grade] = grade_counts.get(result.grade, 0) + 1
    
    print(f"\n🏆 등급 분포:")
    for grade in ['A', 'B', 'C', 'D', 'F']:
        count = grade_counts.get(grade, 0)
        if count > 0:
            print(f"  • {grade}등급: {count}건")
    
    print("=" * 60)


def handle_model_config():
    """모델 설정 관리"""
    config_manager = get_config_manager()
    
    while True:
        print("\n⚙️ 모델 설정 관리")
        print("=" * 40)
        print("1. 현재 설정 보기")
        print("2. 프로필 변경")
        print("3. 사용 가능한 모델 목록")
        print("4. 설정 검증")
        print("0. 돌아가기")
        
        try:
            choice = int(input("\n선택 (0-4): "))
        except ValueError:
            print("숫자를 입력하세요.")
            continue
        
        if choice == 1:
            config_manager.print_current_config()
        elif choice == 2:
            handle_profile_change(config_manager)
        elif choice == 3:
            show_available_models()
        elif choice == 4:
            if config_manager.validate_current_config():
                print("✅ 설정이 올바릅니다.")
            else:
                print("❌ 설정에 문제가 있습니다.")
        elif choice == 0:
            break
        else:
            print("올바른 메뉴를 선택하세요 (0-4).")


def handle_profile_change(config_manager):
    """프로필 변경 처리"""
    profiles = config_manager.get_available_profiles()
    current = config_manager.get_current_profile()
    
    print(f"\n📋 사용 가능한 프로필:")
    for i, profile in enumerate(profiles, 1):
        marker = "✅" if profile == current else "  "
        try:
            info = config_manager.get_profile_info(profile)
            print(f"{marker} {i}. {profile}")
            print(f"     {info.description}")
        except:
            print(f"{marker} {i}. {profile} (설정 오류)")
    
    try:
        choice = int(input(f"\n변경할 프로필 번호 (1-{len(profiles)}): "))
        if 1 <= choice <= len(profiles):
            new_profile = profiles[choice - 1]
            if config_manager.set_profile(new_profile):
                print(f"✅ 프로필이 '{new_profile}'로 변경되었습니다.")
            else:
                print("❌ 프로필 변경에 실패했습니다.")
        else:
            print("올바른 번호를 입력하세요.")
    except ValueError:
        print("숫자를 입력하세요.")


def show_available_models():
    """사용 가능한 모델 목록 출력"""
    factory = get_model_factory()
    model_info = factory.get_model_info()
    
    print(f"\n🤖 지원되는 모델:")
    print("-" * 40)
    
    print("📊 임베딩 모델:")
    for provider, models in model_info["embedding_models"].items():
        print(f"  • {provider}:")
        for model in models:
            print(f"    - {model}")
    
    print("\n🤖 LLM 모델:")
    for provider, models in model_info["llm_models"].items():
        print(f"  • {provider}:")
        for model in models:
            print(f"    - {model}")
    
    print("\n📈 분류 모델:")
    for provider, models in model_info["classification_models"].items():
        print(f"  • {provider}:")
        for model in models:
            print(f"    - {model}")


def handle_system_info():
    """시스템 정보 출력"""
    config_manager = get_config_manager()
    factory = get_model_factory()
    
    print("\n📋 시스템 정보")
    print("=" * 40)
    print("🚀 QA Analysis System v2.0")
    print("🏗️ SOLID 원칙 적용 모듈화 구조")
    print("🇰🇷 한국어 특화 모델 4개 지원")
    print("🤖 GPT-4o-mini 적용")
    
    print(f"\n⚙️ 현재 설정:")
    profile_info = config_manager.get_profile_info()
    print(f"  • 프로필: {config_manager.get_current_profile()}")
    print(f"  • 설명: {profile_info.description}")
    print(f"  • 임베딩: {profile_info.embedding_model}")
    print(f"  • LLM: {profile_info.llm_model}")
    
    available = factory.list_available_models()
    print(f"\n🔧 지원 모델 수:")
    print(f"  • 임베딩: {len(available['embedding'])}개")
    print(f"  • LLM: {len(available['llm'])}개")
    print(f"  • 분류: {len(available['classification'])}개")


def handle_model_test():
    """모델 테스트"""
    print("\n🧪 모델 테스트")
    print("=" * 40)
    print("간단한 텍스트로 현재 모델을 테스트합니다.")
    
    question = input("테스트 질문을 입력하세요: ").strip()
    answer = input("테스트 답변을 입력하세요: ").strip()
    
    if question and answer:
        return run_qa_analysis_by_text(question, answer, "full")
    else:
        print("❌ 질문과 답변을 모두 입력해주세요.")
        return 1


def main():
    """메인 함수"""
    display_welcome()
    
    # 설정 초기화
    try:
        config_manager = get_config_manager()
        if not config_manager.validate_current_config():
            print("⚠️ 설정에 문제가 있습니다. 기본 설정을 사용합니다.")
    except Exception as e:
        print(f"❌ 초기화 실패: {e}")
        return 1
    
    while True:
        display_main_menu()
        
        try:
            choice = int(input("선택 (0-7): "))
        except ValueError:
            print("숫자를 입력하세요.")
            continue
        
        if choice == 1:
            handle_qa_analysis()
        elif choice == 2:
            handle_batch_analysis()
        elif choice == 3:
            print("🏷️ 태깅 기능은 개발 중입니다.")
        elif choice == 4:
            handle_model_config()
        elif choice == 5:
            config_manager.list_all_profiles()
        elif choice == 6:
            handle_system_info()
        elif choice == 7:
            handle_model_test()
        elif choice == 0:
            print("👋 프로그램을 종료합니다.")
            return 0
        else:
            print("올바른 메뉴를 선택하세요 (0-7).")
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
