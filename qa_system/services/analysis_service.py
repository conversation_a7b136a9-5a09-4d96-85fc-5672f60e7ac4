"""
QA 분석 서비스 - 핵심 분석 로직
"""
import re
import time
from typing import Tuple, List
from datetime import datetime

from qa_system.core.domain import QAPair, Grade
from qa_system.models.kr_sbert import KRSBERTModel


class QAAnalysisService:
    """QA 분석 서비스"""
    
    def __init__(self):
        self.embedding_model = KRSBERTModel()
        
        # 분석 설정
        self.pass_threshold = 0.6
        self.grade_thresholds = {
            'A': 0.9, 'B': 0.8, 'C': 0.7, 'D': 0.6, 'F': 0.0
        }
        self.score_weights = {
            'semantic_similarity': 0.25,
            'topic_relevance': 0.25,
            'keyword_overlap': 0.15,
            'answer_completeness': 0.15,
            'answer_accuracy': 0.10,
            'answer_helpfulness': 0.10
        }
        
        # 한국어 불용어
        self.stopwords = {
            '은', '는', '이', '가', '을', '를', '에', '의', '와', '과', '도', '만',
            '부터', '까지', '로', '으로', '에서', '께서', '한테', '안녕하세요',
            '감사합니다', '죄송합니다', '고객님', '문의', '요청', '부탁드립니다'
        }
    
    def _calculate_semantic_similarity(self, question: str, answer: str) -> float:
        """의미적 유사도 계산"""
        if not question or not answer or answer.strip().lower() in ["", "nan", "null"]:
            return 0.0
        
        try:
            return self.embedding_model.calculate_similarity(question, answer)
        except Exception as e:
            print(f"⚠️ 유사도 계산 실패: {e}")
            return 0.0
    
    def _extract_keywords(self, text: str) -> List[str]:
        """키워드 추출"""
        if not text:
            return []
        
        # 한글, 영문, 숫자만 추출
        words = re.findall(r'[가-힣a-zA-Z0-9]+', text.lower())
        
        # 불용어 제거 및 길이 필터링
        keywords = [word for word in words 
                   if len(word) > 1 and word not in self.stopwords]
        
        return list(set(keywords))  # 중복 제거
    
    def _calculate_topic_relevance(self, question: str, answer: str) -> float:
        """주제 관련성 계산"""
        if not question or not answer:
            return 0.0
        
        question_keywords = self._extract_keywords(question)
        answer_keywords = self._extract_keywords(answer)
        
        if not question_keywords:
            return 0.0
        
        # 주제어 매칭 점수
        matched_keywords = set(question_keywords) & set(answer_keywords)
        relevance_score = len(matched_keywords) / len(question_keywords)
        
        # 특정 주제별 가중치 적용
        topic_weights = {
            '배송': 1.2, '택배': 1.2, '발송': 1.2,
            '환불': 1.3, '취소': 1.3, '반품': 1.3,
            '상품': 1.1, '제품': 1.1, '품질': 1.1,
            '결제': 1.2, '카드': 1.2, '계좌': 1.2
        }
        
        # 가중치 적용
        weighted_score = relevance_score
        for keyword in matched_keywords:
            if keyword in topic_weights:
                weighted_score *= topic_weights[keyword]
        
        return min(1.0, weighted_score)
    
    def _calculate_keyword_overlap(self, question: str, answer: str) -> float:
        """키워드 겹침도 계산"""
        question_keywords = set(self._extract_keywords(question))
        answer_keywords = set(self._extract_keywords(answer))
        
        if not question_keywords and not answer_keywords:
            return 0.0
        
        overlap = len(question_keywords & answer_keywords)
        total = len(question_keywords | answer_keywords)
        
        return overlap / total if total > 0 else 0.0
    
    def _evaluate_answer_quality(self, question: str, answer: str) -> Tuple[float, float, float]:
        """답변 품질 평가 (완성도, 정확성, 도움도)"""
        if not answer or answer.strip().lower() in ["", "nan", "null"]:
            return 0.0, 0.0, 0.0
        
        # 완성도 평가
        completeness = min(1.0, len(answer) / 200)  # 200자 기준
        
        # 정중함 보너스
        if any(word in answer for word in ["안녕하세요", "감사합니다", "죄송합니다"]):
            completeness += 0.1
        
        # 구체적 정보 보너스
        if any(word in answer for word in ["주문번호", "운송장", "연락드리겠습니다", "처리"]):
            completeness += 0.2
        
        completeness = min(1.0, completeness)
        
        # 정확성 평가 (키워드 기반)
        accuracy = self._calculate_topic_relevance(question, answer)
        
        # 도움도 평가
        helpfulness = 0.5  # 기본 점수
        
        # 해결 지향적 표현
        if any(word in answer for word in ["해결", "처리", "완료", "안내", "확인"]):
            helpfulness += 0.3
        
        # 추가 지원 제안
        if any(word in answer for word in ["고객센터", "문의", "연락", "게시판"]):
            helpfulness += 0.2
        
        helpfulness = min(1.0, helpfulness)
        
        return completeness, accuracy, helpfulness
    
    def _determine_grade(self, score: float) -> Grade:
        """점수에 따른 등급 결정"""
        for grade, threshold in self.grade_thresholds.items():
            if score >= threshold:
                return Grade(grade)
        return Grade.F
    
    def _generate_feedback(self, question: str, answer: str, scores: dict) -> Tuple[List[str], List[str], List[str]]:
        """피드백 생성 (강점, 약점, 개선사항)"""
        strengths = []
        weaknesses = []
        recommendations = []
        
        # 강점 분석
        if scores['semantic_similarity'] > 0.7:
            strengths.append("질문과 답변의 의미적 연관성이 높음")
        
        if scores['answer_completeness'] > 0.8:
            strengths.append("답변이 충분히 상세함")
        
        if "안녕하세요" in answer and "감사합니다" in answer:
            strengths.append("정중하고 친절한 답변 톤")
        
        if any(word in answer for word in ["주문번호", "운송장", "처리"]):
            strengths.append("구체적인 정보 제공")
        
        # 약점 분석
        if scores['semantic_similarity'] < 0.5:
            weaknesses.append("질문과 답변의 연관성이 낮음")
        
        if scores['answer_completeness'] < 0.5:
            weaknesses.append("답변이 너무 간단함")
        
        if scores['topic_relevance'] < 0.6:
            weaknesses.append("질문의 핵심 주제를 충분히 다루지 못함")
        
        if not answer or answer.strip().lower() in ["", "nan", "null"]:
            weaknesses.append("답변이 없음")
        
        # 개선사항 제안
        if scores['semantic_similarity'] < 0.6:
            recommendations.append("질문의 핵심 키워드를 답변에 더 많이 포함")
        
        if scores['answer_completeness'] < 0.7:
            recommendations.append("더 상세하고 구체적인 답변 제공")
        
        if scores['answer_helpfulness'] < 0.7:
            recommendations.append("고객이 다음에 취할 수 있는 구체적 행동 안내")
        
        if not any(word in answer for word in ["고객센터", "문의"]):
            recommendations.append("추가 문의 채널 안내 추가")
        
        return strengths, weaknesses, recommendations
    
    def analyze_qa_pair(self, qa_id: str, question: str, answer: str) -> QAPair:
        """QA 쌍 분석 - 메인 메서드"""
        start_time = time.time()
        
        try:
            # 점수 계산
            semantic_similarity = self._calculate_semantic_similarity(question, answer)
            topic_relevance = self._calculate_topic_relevance(question, answer)
            keyword_overlap = self._calculate_keyword_overlap(question, answer)
            
            completeness, accuracy, helpfulness = self._evaluate_answer_quality(question, answer)
            
            # 종합 점수 계산 (가중평균)
            scores = {
                'semantic_similarity': semantic_similarity,
                'topic_relevance': topic_relevance,
                'keyword_overlap': keyword_overlap,
                'answer_completeness': completeness,
                'answer_accuracy': accuracy,
                'answer_helpfulness': helpfulness
            }
            
            overall_score = sum(scores[key] * self.score_weights[key] for key in scores.keys())
            
            # 통과 여부 및 등급
            pass_threshold = overall_score >= self.pass_threshold
            grade = self._determine_grade(overall_score)
            
            # 피드백 생성
            strengths, weaknesses, recommendations = self._generate_feedback(question, answer, scores)
            
            # 처리 시간 계산
            processing_time = (time.time() - start_time) * 1000
            
            # QAPair 객체 생성
            qa_pair = QAPair(
                id=qa_id,
                question=question,
                answer=answer,
                semantic_similarity=semantic_similarity,
                topic_relevance=topic_relevance,
                keyword_overlap=keyword_overlap,
                answer_completeness=completeness,
                answer_accuracy=accuracy,
                answer_helpfulness=helpfulness,
                overall_score=overall_score,
                grade=grade,
                pass_threshold=pass_threshold,
                strengths=strengths,
                weaknesses=weaknesses,
                recommendations=recommendations,
                analysis_timestamp=datetime.now(),
                processing_time_ms=processing_time,
                model_info=self.embedding_model.get_model_info()
            )
            
            return qa_pair
            
        except Exception as e:
            print(f"❌ 분석 중 오류: {e}")
            # 오류 발생 시 기본 QAPair 반환
            return QAPair(
                id=qa_id,
                question=question,
                answer=answer,
                weaknesses=["분석 실패"],
                recommendations=["다시 시도해주세요"],
                analysis_timestamp=datetime.now(),
                processing_time_ms=(time.time() - start_time) * 1000,
                model_info={"error": "analysis_failed"}
            )
