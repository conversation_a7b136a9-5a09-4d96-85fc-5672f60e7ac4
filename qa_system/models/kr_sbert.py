"""
KR-SBERT 모델 - 간소화된 버전
"""
from typing import List
import numpy as np
from sklearn.metrics.pairwise import cosine_similarity


class KRSBERTModel:
    """KR-SBERT 임베딩 모델"""
    
    def __init__(self, model_name: str = "snunlp/KR-SBERT-V40K-klueNLI-augSTS"):
        self.model_name = model_name
        self.model = None
        self._is_loaded = False
    
    def load_model(self) -> None:
        """모델 로드"""
        if self._is_loaded:
            return
        
        try:
            from sentence_transformers import SentenceTransformer
            print(f"🔤 KR-SBERT 모델 로딩 중: {self.model_name}")
            self.model = SentenceTransformer(self.model_name)
            self._is_loaded = True
            print(f"✅ KR-SBERT 모델 로드 완료: {self.model_name}")
        except ImportError:
            raise Exception("sentence-transformers library not installed. Run: pip install sentence-transformers")
        except Exception as e:
            raise Exception(f"Model loading failed: {str(e)}")
    
    def encode_texts(self, texts: List[str]) -> List[List[float]]:
        """텍스트 리스트를 임베딩으로 변환"""
        if not self._is_loaded:
            self.load_model()
        
        try:
            # 빈 텍스트 처리
            processed_texts = [text if text.strip() else " " for text in texts]
            
            # 임베딩 생성
            embeddings = self.model.encode(
                processed_texts,
                normalize_embeddings=True,
                show_progress_bar=False
            )
            
            return embeddings.tolist()
            
        except Exception as e:
            print(f"⚠️ 임베딩 생성 실패: {e}")
            return [[0.0] * 768 for _ in texts]  # 기본 차원으로 빈 임베딩 반환
    
    def calculate_similarity(self, text1: str, text2: str) -> float:
        """두 텍스트 간 코사인 유사도 계산"""
        if not text1.strip() or not text2.strip():
            return 0.0
        
        try:
            embeddings = self.encode_texts([text1, text2])
            similarity = cosine_similarity([embeddings[0]], [embeddings[1]])[0][0]
            return float(similarity)
        except Exception as e:
            print(f"⚠️ 유사도 계산 실패: {e}")
            return 0.0
    
    def is_loaded(self) -> bool:
        """모델 로드 상태 확인"""
        return self._is_loaded
    
    def get_model_info(self) -> dict:
        """모델 정보 반환"""
        return {
            "name": self.model_name,
            "type": "embedding",
            "provider": "huggingface",
            "is_loaded": self._is_loaded
        }
