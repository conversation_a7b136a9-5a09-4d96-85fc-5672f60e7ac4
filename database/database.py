from abc import ABC, abstractmethod
from typing import Any, Dict, Generic, List, TypeVar

from supabase import Client, create_client

T = TypeVar('T')


class DatabaseRepository(ABC, Generic[T]):
    """데이터베이스 리포지토리의 추상 기본 클래스 (Interface Segregation Principle)"""
    
    @abstractmethod
    def get_all(self) -> List[T]:
        """모든 레코드 조회"""
        pass
    
    @abstractmethod
    def get_by_id(self, id: str) -> T:
        """ID로 단일 레코드 조회"""
        pass
    
    @abstractmethod
    def create(self, data: Dict[str, Any]) -> T:
        """새 레코드 생성"""
        pass
    
    @abstractmethod
    def update(self, id: str, data: Dict[str, Any]) -> T:
        """기존 레코드 업데이트"""
        pass
    
    @abstractmethod
    def delete(self, id: str) -> bool:
        """레코드 삭제"""
        pass


class SupabaseDB:
    """Supabase 연결 및 초기화 담당 (Single Responsibility Principle)"""
    
    def __init__(self, url: str, key: str):
        self.client: Client = create_client(url, key)
    
    def get_client(self) -> Client:
        """Supabase 클라이언트 반환"""
        return self.client


class SupabaseRepository(DatabaseRepository[Dict[str, Any]]):
    """Supabase 기본 리포지토리 구현 (Open/Closed Principle)"""
    
    def __init__(self, db: SupabaseDB, table_name: str):
        self.client = db.get_client()
        self.table_name = table_name
    
    def get_all(self) -> List[Dict[str, Any]]:
        return self.client.table(self.table_name).select('*').execute().data
    
    def get_by_id(self, id: str) -> Dict[str, Any]:
        result = (
            self.client.table(self.table_name)
            .select('*')
            .eq('id', id)
            .single()
            .execute()
        )
        return result.data
    
    def create(self, data: Dict[str, Any]) -> Dict[str, Any]:
        result = self.client.table(self.table_name).insert(data).execute()
        return result.data[0]
    
    def update(self, id: str, data: Dict[str, Any]) -> Dict[str, Any]:
        result = (
            self.client.table(self.table_name)
            .update(data)
            .eq('id', id)
            .execute()
        )
        return result.data[0]
    
    def delete(self, id: str) -> bool:
        result = (
            self.client.table(self.table_name)
            .delete()
            .eq('id', id)
            .execute()
        )
        return len(result.data) > 0
    
    def filter(self, column: str, value: Any) -> List[Dict[str, Any]]:
        result = (
            self.client.table(self.table_name)
            .select('*')
            .eq(column, value)
            .execute()
        )
        return result.data


# 특정 테이블 리포지토리 클래스들 (Dependency Inversion Principle)
class ProductRepository(SupabaseRepository):
    """상품 테이블 리포지토리"""
    
    def __init__(self, db: SupabaseDB):
        super().__init__(db, 'product')
    
    def get_by_name(self, name: str) -> List[Dict[str, Any]]:
        """상품명으로 검색"""
        return self.filter('name', name)


class OrderRepository(SupabaseRepository):
    """주문 테이블 리포지토리"""
    
    def __init__(self, db: SupabaseDB):
        super().__init__(db, 'order')
    
    def get_by_user_id(self, user_id: str) -> List[Dict[str, Any]]:
        """사용자 ID로 주문 조회"""
        return self.filter('user_id', user_id)
    
    def get_by_status(self, status: str) -> List[Dict[str, Any]]:
        """주문 상태로 조회"""
        return self.filter('order_status', status)


class InquiryRepository(SupabaseRepository):
    """문의 테이블 리포지토리"""
    
    def __init__(self, db: SupabaseDB, inquiry_type: str):
        """
        문의 유형별 리포지토리 초기화
        
        Args:
            db: 데이터베이스 연결
            inquiry_type: 문의 테이블 유형(product_inquiries 등)
        """
        super().__init__(db, inquiry_type)
    
    def get_by_document(self, document: str) -> List[Dict[str, Any]]:
        """문서 ID로 문의 조회"""
        return self.filter('document', document)
    
    def get_by_customer(self, customer: str) -> List[Dict[str, Any]]:
        """고객명으로 문의 조회"""
        return self.filter('customer', customer)


# 팩토리 클래스 - 필요한 리포지토리 생성 (Dependency Inversion)
class RepositoryFactory:
    """리포지토리 생성 팩토리"""
    
    def __init__(self, db: SupabaseDB):
        self.db = db
    
    def create_product_repository(self) -> ProductRepository:
        return ProductRepository(self.db)
    
    def create_order_repository(self) -> OrderRepository:
        return OrderRepository(self.db)
    
    def create_product_inquiry_repository(self) -> InquiryRepository:
        return InquiryRepository(self.db, 'product_inquiries')
    
    def create_personal_inquiry_repository(self) -> InquiryRepository:
        return InquiryRepository(self.db, 'personal_inquiries')
    
    def create_shipping_inquiry_repository(self) -> InquiryRepository:
        return InquiryRepository(self.db, 'shipping_inquiry')
    
    def create_repository(self, table_name: str) -> SupabaseRepository:
        """일반 테이블 리포지토리 생성"""
        return SupabaseRepository(self.db, table_name) 