-- 문의 데이터 테이블
CREATE TABLE product_inquiries (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    post_id TEXT NOT NULL,
    user_id TEXT,
    content TEXT NOT NULL,
    subject TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_answered BO<PERSON>EAN DEFAULT FALSE,
    answer_content TEXT,
    answered_at TIMESTAMP WITH TIME ZONE,
    metadata JSONB
);

-- 분류 결과 테이블
CREATE TABLE classification_results (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    post_id TEXT NOT NULL,
    brand TEXT,
    original_content TEXT NOT NULL,
    category TEXT NOT NULL,
    confidence FLOAT NOT NULL,
    is_buyer BOOLEAN DEFAULT TRUE,
    inquiry_timing TEXT,
    tone TEXT,
    handling_difficulty TEXT,
    classified_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(post_id)
);

-- 분류 성능 로그 테이블
CREATE TABLE classification_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    batch_id TEXT,
    total_processed INTEGER,
    embedding_classified INTEGER,
    llm_classified INTEGER,
    avg_confidence FLOAT,
    processing_time FLOAT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE playauto_qna (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  document TEXT,
  company_code TEXT,
  mall_name TEXT,
  inquiry_at TEXT, 
  inquiry_content TEXT, #고객 문의
  answered_at TEXT,
  answer_content TEXT # 셀러 답변
);