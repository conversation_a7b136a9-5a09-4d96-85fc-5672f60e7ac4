"""
데이터베이스 서비스 - Supabase 연동
"""
import asyncio
from typing import List, Dict, Any, Optional


class DatabaseService:
    """데이터베이스 서비스"""
    
    def __init__(self, supabase_url: str = None, supabase_key: str = None):
        self.supabase_url = supabase_url
        self.supabase_key = supabase_key
        self._service = None
        self._is_connected = False
    
    async def connect(self) -> bool:
        """DB 연결"""
        if not self.supabase_url or not self.supabase_key:
            print("⚠️ DB 연결 정보가 없습니다")
            return False
        
        try:
            from src.services.async_inquiry_service import AsyncInquiryService
            self._service = AsyncInquiryService(self.supabase_url, self.supabase_key)
            self._is_connected = True
            print("✅ DB 서비스 연결 완료")
            return True
        except Exception as e:
            print(f"⚠️ DB 연결 실패: {e}")
            return False
    
    async def get_inquiry_by_id(self, inquiry_id: str) -> Optional[Dict[str, Any]]:
        """ID로 문의 조회"""
        if not self._is_connected:
            await self.connect()
        
        if not self._service:
            return None
        
        try:
            inquiries = await self._service.get_all_inquiries(
                table='personal',
                limit=1000
            )
            
            for inquiry in inquiries:
                if inquiry.get('id') == inquiry_id:
                    return inquiry
            
            return None
            
        except Exception as e:
            print(f"⚠️ 문의 조회 실패: {e}")
            return None
    
    async def get_sample_inquiries(self, limit: int = 20) -> List[Dict[str, Any]]:
        """샘플 문의 목록 조회"""
        if not self._is_connected:
            await self.connect()
        
        if not self._service:
            return []
        
        try:
            inquiries = await self._service.get_all_inquiries(
                table='personal',
                limit=limit
            )
            return inquiries[:limit]
            
        except Exception as e:
            print(f"⚠️ 샘플 문의 조회 실패: {e}")
            return []
    
    def is_connected(self) -> bool:
        """연결 상태 확인"""
        return self._is_connected
