"""
설정 관리자 - 간소화된 버전
"""
from typing import Dict, List, Any


class ConfigManager:
    """설정 관리자"""
    
    def __init__(self):
        """설정 초기화"""
        self.current_profile = "korean_ecommerce_optimized"
        
        # 프로필 정의
        self.profiles = {
            "korean_ecommerce_optimized": {
                "name": "한국어 이커머스 최적화",
                "description": "한국어 쇼핑몰 문의 분석에 최적화된 모델 조합",
                "embedding_model": "snunlp/KR-SBERT-V40K-klueNLI-augSTS",
                "llm_model": "gpt-4o-mini",
                "classification_model": "beomi/KcELECTRA-base"
            },
            "high_performance": {
                "name": "고성능 분석",
                "description": "최고 정확도를 위한 대형 모델 조합",
                "embedding_model": "klue/roberta-base",
                "llm_model": "gpt-4o",
                "classification_model": "klue/roberta-base"
            },
            "lightweight_fast": {
                "name": "경량 고속",
                "description": "빠른 처리를 위한 경량 모델 조합",
                "embedding_model": "sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2",
                "llm_model": "gpt-3.5-turbo",
                "classification_model": "beomi/KcELECTRA-base"
            },
            "sentiment_specialized": {
                "name": "감성 분석 특화",
                "description": "고객 감정 분석에 특화된 모델 조합",
                "embedding_model": "skt/kobert-base-v1",
                "llm_model": "gpt-4o-mini",
                "classification_model": "beomi/KcELECTRA-base"
            },
            "experimental": {
                "name": "실험용",
                "description": "새로운 모델 조합 테스트용",
                "embedding_model": "klue/roberta-base",
                "llm_model": "claude-3-haiku-20240307",
                "classification_model": "skt/kobert-base-v1"
            }
        }
        
        print(f"✅ 설정 로드 완료 (프로필: {self.current_profile})")
    
    def get_available_profiles(self) -> List[str]:
        """사용 가능한 프로필 목록"""
        return list(self.profiles.keys())
    
    def get_current_profile(self) -> str:
        """현재 프로필"""
        return self.current_profile
    
    def set_profile(self, profile_name: str) -> bool:
        """프로필 변경"""
        if profile_name not in self.profiles:
            print(f"❌ 존재하지 않는 프로필: {profile_name}")
            return False
        
        self.current_profile = profile_name
        print(f"✅ 프로필 변경: {profile_name}")
        return True
    
    def get_profile_info(self, profile_name: str = None) -> Dict[str, Any]:
        """프로필 정보"""
        profile = profile_name or self.current_profile
        return self.profiles.get(profile, {})
    
    def print_current_config(self) -> None:
        """현재 설정 출력"""
        profile_info = self.get_profile_info()
        
        print(f"\n📋 현재 설정 (프로필: {self.current_profile})")
        print("=" * 60)
        print(f"📝 설명: {profile_info.get('description', '')}")
        print(f"🔤 임베딩 모델: {profile_info.get('embedding_model', '')}")
        print(f"🤖 LLM 모델: {profile_info.get('llm_model', '')}")
        print(f"📊 분류 모델: {profile_info.get('classification_model', '')}")
        print("=" * 60)
    
    def list_all_profiles(self) -> None:
        """모든 프로필 목록 출력"""
        print(f"\n📋 사용 가능한 프로필:")
        print("-" * 50)
        
        for profile_name, profile_info in self.profiles.items():
            marker = "✅" if profile_name == self.current_profile else "  "
            print(f"{marker} {profile_name}")
            print(f"     {profile_info.get('description', '')}")
            print(f"     임베딩: {profile_info.get('embedding_model', '')}")
            print(f"     LLM: {profile_info.get('llm_model', '')}")
            print()
    
    def validate_current_config(self) -> bool:
        """현재 설정 검증"""
        try:
            profile_info = self.get_profile_info()
            
            # 필수 필드 확인
            required_fields = ['embedding_model', 'llm_model']
            for field in required_fields:
                if not profile_info.get(field):
                    print(f"⚠️ 필수 설정 누락: {field}")
                    return False
            
            print("✅ 설정 검증 완료")
            return True
            
        except Exception as e:
            print(f"❌ 설정 검증 실패: {e}")
            return False
