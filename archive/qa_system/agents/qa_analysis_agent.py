"""
QA 분석 에이전트 - 간소화된 버전
"""
import asyncio
import time
import json
import uuid
from typing import List, Optional, Dict, Any
from datetime import datetime

from qa_system.core.domain import QAPair, AnalysisRequest, BatchAnalysisResult
from qa_system.services.analysis_service import QAAnalysisService
from qa_system.services.db_service import DatabaseService


class QAAnalysisAgent:
    """QA 분석 에이전트"""
    
    def __init__(self, supabase_url: str = None, supabase_key: str = None):
        """에이전트 초기화"""
        self.analysis_service = QAAnalysisService()
        self.db_service = DatabaseService(supabase_url, supabase_key)
        
        print("✅ QA 분석 에이전트 v2.1 초기화 완료")
    
    async def analyze_single_qa(self, request: AnalysisRequest) -> QAPair:
        """단일 QA 분석"""
        try:
            # 데이터 준비
            if request.inquiry_id and not request.question_text:
                # DB에서 조회
                inquiry_data = await self.db_service.get_inquiry_by_id(request.inquiry_id)
                if inquiry_data:
                    subject = inquiry_data.get('subject', '') or ''
                    content = inquiry_data.get('content', '') or ''
                    question = f"{subject} {content}".strip()
                    answer = inquiry_data.get('answer', '') or ''
                    qa_id = request.inquiry_id
                else:
                    raise Exception(f"문의 ID {request.inquiry_id}를 찾을 수 없습니다")
            else:
                # 직접 입력
                question = request.question_text or ""
                answer = request.answer_text or ""
                qa_id = request.inquiry_id or str(uuid.uuid4())
            
            # 분석 실행
            result = self.analysis_service.analyze_qa_pair(qa_id, question, answer)
            return result
            
        except Exception as e:
            print(f"❌ 분석 실패: {e}")
            # 오류 시 기본 QAPair 반환
            return QAPair(
                id=request.inquiry_id or str(uuid.uuid4()),
                question=request.question_text or "",
                answer=request.answer_text or "",
                weaknesses=["분석 실패"],
                recommendations=["다시 시도해주세요"],
                analysis_timestamp=datetime.now(),
                model_info={"error": "analysis_failed"}
            )
    
    async def analyze_batch(self, requests: List[AnalysisRequest]) -> BatchAnalysisResult:
        """배치 분석"""
        start_time = time.time()
        results = []
        
        print(f"📊 {len(requests)}건의 QA 쌍 분석 시작...")
        
        for i, request in enumerate(requests, 1):
            try:
                result = await self.analyze_single_qa(request)
                results.append(result)
                print(f"  {i}/{len(requests)} 완료")
            except Exception as e:
                print(f"  {i}/{len(requests)} 실패: {e}")
                continue
        
        # 통계 계산
        total_count = len(results)
        pass_count = sum(1 for r in results if r.pass_threshold)
        average_score = sum(r.overall_score for r in results) / total_count if total_count > 0 else 0.0
        
        # 등급 분포
        grade_distribution = {}
        for result in results:
            grade = result.grade.value
            grade_distribution[grade] = grade_distribution.get(grade, 0) + 1
        
        processing_time = time.time() - start_time
        
        return BatchAnalysisResult(
            total_count=total_count,
            pass_count=pass_count,
            average_score=average_score,
            grade_distribution=grade_distribution,
            results=results,
            processing_time_seconds=processing_time
        )
    
    async def analyze_sample_data(self, limit: int = 20) -> BatchAnalysisResult:
        """샘플 데이터 분석"""
        # DB에서 샘플 데이터 가져오기
        sample_data = await self.db_service.get_sample_inquiries(limit)
        
        if not sample_data:
            # DB 연결 실패 시 하드코딩된 샘플 ID 사용
            sample_ids = [
                "b958b301-7aba-4521-999c-019e13b24403",
                "68d308a7-69ea-4c95-b71f-0ab6b0de5452",
                "91db3f8d-2294-4804-8581-dab0079520cc",
                "33d84df6-14c7-47f7-9025-cd4a7e4d0c7a",
                "3484f661-2025-44ca-a766-aeceaa9619f9"
            ]
            
            requests = [
                AnalysisRequest(inquiry_id=inquiry_id)
                for inquiry_id in sample_ids[:min(limit, len(sample_ids))]
            ]
        else:
            requests = [
                AnalysisRequest(inquiry_id=item.get('id'))
                for item in sample_data[:limit]
                if item.get('id')
            ]
        
        return await self.analyze_batch(requests)
    
    def export_results(self, batch_result: BatchAnalysisResult, filename: str = None) -> None:
        """결과 내보내기"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"outputs/analysis_results/qa_analysis_{timestamp}.json"
        
        # 디렉토리 생성
        import os
        os.makedirs(os.path.dirname(filename), exist_ok=True)
        
        try:
            # JSON 저장 (ensure_ascii 중복 문제 해결)
            data = batch_result.to_dict()
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            
            print(f"📄 결과를 {filename}에 저장했습니다.")
            
        except Exception as e:
            print(f"⚠️ 파일 저장 실패: {e}")
    
    def get_agent_info(self) -> Dict[str, Any]:
        """에이전트 정보"""
        return {
            "agent_type": "QA Analysis Agent v2.1",
            "version": "2.1.0",
            "embedding_model": self.analysis_service.embedding_model.get_model_info(),
            "db_connected": self.db_service.is_connected(),
            "analysis_settings": {
                "pass_threshold": self.analysis_service.pass_threshold,
                "grade_thresholds": self.analysis_service.grade_thresholds,
                "score_weights": self.analysis_service.score_weights
            }
        }
