# QA Analysis System - Model Profiles Configuration
# 한국어 이커머스 특화 모델 프로필 설정

# 기본 프로필
default_profile: "korean_ecommerce_optimized"

# 모델 프로필 정의
profiles:
  
  # 1. 한국어 이커머스 최적화 프로필 (추천)
  korean_ecommerce_optimized:
    name: "한국어 이커머스 최적화"
    description: "한국어 쇼핑몰 문의 분석에 최적화된 모델 조합"
    
    embedding_model:
      provider: "huggingface"
      model_name: "snunlp/KR-SBERT-V40K-klueNLI-augSTS"
      model_type: "embedding"
      device: "cpu"
      additional_params:
        normalize_embeddings: true
        batch_size: 16
    
    llm_model:
      provider: "openai"
      model_name: "gpt-4o-mini"
      model_type: "llm"
      max_tokens: 500
      temperature: 0.1
      additional_params:
        response_format: "json"
        korean_optimized: true
    
    classification_model:
      provider: "huggingface"
      model_name: "beomi/KcELECTRA-base"
      model_type: "classification"
      device: "cpu"
      additional_params:
        sentiment_analysis: true
        ecommerce_categories: true

  # 2. 고성능 프로필 (정확도 우선)
  high_performance:
    name: "고성능 분석"
    description: "최고 정확도를 위한 대형 모델 조합"
    
    embedding_model:
      provider: "huggingface"
      model_name: "klue/roberta-base"
      model_type: "embedding"
      device: "cuda"
      additional_params:
        normalize_embeddings: true
        batch_size: 8
        use_mean_pooling: true
    
    llm_model:
      provider: "openai"
      model_name: "gpt-4o"
      model_type: "llm"
      max_tokens: 800
      temperature: 0.05
      additional_params:
        response_format: "json"
        detailed_analysis: true
    
    classification_model:
      provider: "huggingface"
      model_name: "klue/roberta-base"
      model_type: "classification"
      device: "cuda"
      additional_params:
        fine_tuned: false
        num_labels: 6

  # 3. 경량 프로필 (속도 우선)
  lightweight_fast:
    name: "경량 고속"
    description: "빠른 처리를 위한 경량 모델 조합"
    
    embedding_model:
      provider: "huggingface"
      model_name: "sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2"
      model_type: "embedding"
      device: "cpu"
      additional_params:
        normalize_embeddings: true
        batch_size: 32
    
    llm_model:
      provider: "openai"
      model_name: "gpt-3.5-turbo"
      model_type: "llm"
      max_tokens: 300
      temperature: 0.2
      additional_params:
        response_format: "text"
        fast_mode: true
    
    classification_model:
      provider: "huggingface"
      model_name: "beomi/KcELECTRA-base"
      model_type: "classification"
      device: "cpu"
      additional_params:
        simple_classification: true

  # 4. 감성 분석 특화 프로필
  sentiment_specialized:
    name: "감성 분석 특화"
    description: "고객 감정 분석에 특화된 모델 조합"
    
    embedding_model:
      provider: "huggingface"
      model_name: "skt/kobert-base-v1"
      model_type: "embedding"
      device: "cpu"
      additional_params:
        normalize_embeddings: true
        use_cls_token: true
    
    llm_model:
      provider: "openai"
      model_name: "gpt-4o-mini"
      model_type: "llm"
      max_tokens: 400
      temperature: 0.1
      additional_params:
        sentiment_focus: true
        emotion_analysis: true
    
    classification_model:
      provider: "huggingface"
      model_name: "beomi/KcELECTRA-base"
      model_type: "classification"
      device: "cpu"
      additional_params:
        sentiment_analysis: true
        emotion_categories: ["긍정", "중립", "부정", "긴급", "불만"]

  # 5. 실험용 프로필 (다양한 모델 테스트)
  experimental:
    name: "실험용"
    description: "새로운 모델 조합 테스트용"
    
    embedding_model:
      provider: "huggingface"
      model_name: "klue/roberta-base"
      model_type: "embedding"
      device: "cpu"
      additional_params:
        normalize_embeddings: true
        experimental_features: true
    
    llm_model:
      provider: "anthropic"
      model_name: "claude-3-haiku-20240307"
      model_type: "llm"
      max_tokens: 600
      temperature: 0.1
      additional_params:
        experimental_mode: true
    
    classification_model:
      provider: "huggingface"
      model_name: "skt/kobert-base-v1"
      model_type: "classification"
      device: "cpu"
      additional_params:
        experimental_classification: true

# 모델별 세부 설정
model_specific_settings:
  
  # KR-SBERT 설정
  "snunlp/KR-SBERT-V40K-klueNLI-augSTS":
    description: "한국어 문장 임베딩 특화"
    strengths: ["한국어 의미 유사도", "문장 임베딩"]
    use_cases: ["유사도 계산", "의미 검색"]
    performance:
      accuracy: 0.85
      speed: "fast"
      memory: "low"
  
  # KcELECTRA 설정
  "beomi/KcELECTRA-base":
    description: "한국어 감성 분석 특화"
    strengths: ["감성 분석", "분류 성능"]
    use_cases: ["감정 분석", "카테고리 분류"]
    performance:
      accuracy: 0.88
      speed: "medium"
      memory: "medium"
  
  # KoBERT 설정
  "skt/kobert-base-v1":
    description: "한국어 BERT 기반 다목적"
    strengths: ["범용성", "파인튜닝 용이"]
    use_cases: ["분류", "임베딩", "파인튜닝"]
    performance:
      accuracy: 0.82
      speed: "medium"
      memory: "medium"
  
  # KLUE-RoBERTa 설정
  "klue/roberta-base":
    description: "한국어 자연어 이해 특화"
    strengths: ["자연어 이해", "문맥 파악"]
    use_cases: ["복잡한 분류", "의미 분석"]
    performance:
      accuracy: 0.90
      speed: "slow"
      memory: "high"
  
  # GPT-4o-mini 설정
  "gpt-4o-mini":
    description: "OpenAI 경량 고성능 모델"
    strengths: ["추론 능력", "다국어 지원", "비용 효율"]
    use_cases: ["텍스트 생성", "분석", "분류"]
    performance:
      accuracy: 0.92
      speed: "fast"
      cost: "low"

# 프로필별 추천 사용 시나리오
usage_scenarios:
  
  korean_ecommerce_optimized:
    - "일반적인 한국어 쇼핑몰 문의 분석"
    - "실시간 고객 서비스 지원"
    - "중간 규모 데이터 처리"
  
  high_performance:
    - "높은 정확도가 필요한 분석"
    - "복잡한 문의 내용 분석"
    - "품질 평가 및 벤치마킹"
  
  lightweight_fast:
    - "대용량 데이터 빠른 처리"
    - "실시간 응답이 중요한 서비스"
    - "리소스 제약 환경"
  
  sentiment_specialized:
    - "고객 만족도 분석"
    - "감정 기반 우선순위 설정"
    - "브랜드 평판 모니터링"
  
  experimental:
    - "새로운 모델 성능 테스트"
    - "연구 및 개발 목적"
    - "모델 비교 분석"

# 성능 벤치마크 (참고용)
performance_benchmarks:
  processing_speed:
    korean_ecommerce_optimized: "100 문의/분"
    high_performance: "50 문의/분"
    lightweight_fast: "200 문의/분"
    sentiment_specialized: "80 문의/분"
  
  accuracy_scores:
    korean_ecommerce_optimized: 0.87
    high_performance: 0.92
    lightweight_fast: 0.82
    sentiment_specialized: 0.89
  
  resource_usage:
    korean_ecommerce_optimized: "medium"
    high_performance: "high"
    lightweight_fast: "low"
    sentiment_specialized: "medium"

# 자동 프로필 선택 규칙
auto_selection_rules:
  criteria:
    - name: "데이터 크기"
      small: "lightweight_fast"
      medium: "korean_ecommerce_optimized"
      large: "high_performance"
    
    - name: "정확도 요구사항"
      low: "lightweight_fast"
      medium: "korean_ecommerce_optimized"
      high: "high_performance"
    
    - name: "처리 속도 요구사항"
      slow: "high_performance"
      medium: "korean_ecommerce_optimized"
      fast: "lightweight_fast"
    
    - name: "특수 요구사항"
      sentiment_analysis: "sentiment_specialized"
      experimentation: "experimental"
