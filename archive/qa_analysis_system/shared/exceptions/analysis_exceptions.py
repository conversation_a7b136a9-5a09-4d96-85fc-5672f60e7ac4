"""
분석 관련 예외 클래스들
"""


class AnalysisError(Exception):
    """분석 관련 기본 예외"""
    
    def __init__(self, message: str, error_code: str = None, details: dict = None):
        super().__init__(message)
        self.message = message
        self.error_code = error_code or "ANALYSIS_ERROR"
        self.details = details or {}
    
    def __str__(self) -> str:
        return f"[{self.error_code}] {self.message}"


class InvalidQAPairError(AnalysisError):
    """잘못된 QA 쌍 데이터 예외"""
    
    def __init__(self, message: str, qa_pair_id: str = None):
        super().__init__(
            message=message,
            error_code="INVALID_QA_PAIR",
            details={"qa_pair_id": qa_pair_id}
        )


class AnalysisTimeoutError(AnalysisError):
    """분석 시간 초과 예외"""
    
    def __init__(self, timeout_seconds: float, qa_pair_id: str = None):
        super().__init__(
            message=f"Analysis timed out after {timeout_seconds} seconds",
            error_code="ANALYSIS_TIMEOUT",
            details={
                "timeout_seconds": timeout_seconds,
                "qa_pair_id": qa_pair_id
            }
        )


class InsufficientDataError(AnalysisError):
    """데이터 부족 예외"""
    
    def __init__(self, required_fields: list, missing_fields: list):
        super().__init__(
            message=f"Insufficient data. Missing fields: {missing_fields}",
            error_code="INSUFFICIENT_DATA",
            details={
                "required_fields": required_fields,
                "missing_fields": missing_fields
            }
        )


class AnalysisConfigError(AnalysisError):
    """분석 설정 오류 예외"""
    
    def __init__(self, config_key: str, message: str):
        super().__init__(
            message=f"Analysis config error for '{config_key}': {message}",
            error_code="ANALYSIS_CONFIG_ERROR",
            details={"config_key": config_key}
        )
