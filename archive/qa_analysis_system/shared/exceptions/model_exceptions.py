"""
모델 관련 예외 클래스들
"""


class ModelError(Exception):
    """모델 관련 기본 예외"""
    
    def __init__(self, message: str, error_code: str = None, model_name: str = None):
        super().__init__(message)
        self.message = message
        self.error_code = error_code or "MODEL_ERROR"
        self.model_name = model_name
    
    def __str__(self) -> str:
        model_info = f" (Model: {self.model_name})" if self.model_name else ""
        return f"[{self.error_code}] {self.message}{model_info}"


class ModelNotFoundError(ModelError):
    """모델을 찾을 수 없음 예외"""
    
    def __init__(self, model_name: str, provider: str = None):
        message = f"Model '{model_name}' not found"
        if provider:
            message += f" in provider '{provider}'"
        
        super().__init__(
            message=message,
            error_code="MODEL_NOT_FOUND",
            model_name=model_name
        )
        self.provider = provider


class ModelLoadError(ModelError):
    """모델 로드 실패 예외"""
    
    def __init__(self, model_name: str, reason: str):
        super().__init__(
            message=f"Failed to load model '{model_name}': {reason}",
            error_code="MODEL_LOAD_ERROR",
            model_name=model_name
        )
        self.reason = reason


class ModelConfigError(ModelError):
    """모델 설정 오류 예외"""
    
    def __init__(self, model_name: str, config_issue: str):
        super().__init__(
            message=f"Model config error for '{model_name}': {config_issue}",
            error_code="MODEL_CONFIG_ERROR",
            model_name=model_name
        )
        self.config_issue = config_issue


class UnsupportedModelError(ModelError):
    """지원하지 않는 모델 예외"""
    
    def __init__(self, model_name: str, supported_models: list = None):
        message = f"Unsupported model: '{model_name}'"
        if supported_models:
            message += f". Supported models: {supported_models}"
        
        super().__init__(
            message=message,
            error_code="UNSUPPORTED_MODEL",
            model_name=model_name
        )
        self.supported_models = supported_models or []


class ModelAPIError(ModelError):
    """모델 API 호출 오류 예외"""
    
    def __init__(self, model_name: str, api_error: str, status_code: int = None):
        super().__init__(
            message=f"API error for model '{model_name}': {api_error}",
            error_code="MODEL_API_ERROR",
            model_name=model_name
        )
        self.api_error = api_error
        self.status_code = status_code
