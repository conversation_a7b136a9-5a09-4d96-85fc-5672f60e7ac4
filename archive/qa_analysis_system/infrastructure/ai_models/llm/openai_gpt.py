"""
OpenAI GPT 모델 구현 (GPT-4o-mini 포함)
"""
from typing import List, Tu<PERSON>, Dict, Any
import asyncio
import json
from openai import AsyncOpenAI

from qa_analysis_system.core.interfaces.ai_model_interface import (
    BaseLLMModel, ModelInfo, ModelConfig
)
from qa_analysis_system.core.domain.qa_pair import QAPair, AnalysisScores
from qa_analysis_system.shared.exceptions.model_exceptions import ModelLoadError, ModelAPIError


class OpenAIGPTModel(BaseLLMModel):
    """OpenAI GPT 모델 (GPT-4o-mini, GPT-3.5-turbo, GPT-4 지원)"""
    
    def __init__(self, config: ModelConfig):
        super().__init__(config)
        self.model_name = config.name or "gpt-4o-mini"
        self.max_tokens = config.max_tokens or 500
        self.temperature = config.temperature or 0.1
        
        # 지원되는 모델 목록
        self.supported_models = [
            "gpt-4o-mini",
            "gpt-4o", 
            "gpt-4-turbo",
            "gpt-4",
            "gpt-3.5-turbo"
        ]
        
        if self.model_name not in self.supported_models:
            print(f"⚠️ 지원하지 않는 모델: {self.model_name}, gpt-4o-mini로 변경")
            self.model_name = "gpt-4o-mini"
    
    async def _initialize_client_impl(self) -> AsyncOpenAI:
        """OpenAI 클라이언트 초기화"""
        try:
            if not self.config.api_key:
                raise ModelLoadError(self.model_name, "OpenAI API key not provided")
            
            client = AsyncOpenAI(api_key=self.config.api_key)
            
            # 연결 테스트
            await self._test_connection(client)
            
            print(f"✅ OpenAI {self.model_name} 모델 초기화 완료")
            return client
            
        except Exception as e:
            raise ModelLoadError(self.model_name, f"OpenAI client initialization failed: {str(e)}")
    
    async def _test_connection(self, client: AsyncOpenAI) -> None:
        """연결 테스트"""
        try:
            response = await client.chat.completions.create(
                model=self.model_name,
                messages=[{"role": "user", "content": "Hello"}],
                max_tokens=5
            )
            if not response.choices:
                raise Exception("No response from OpenAI API")
        except Exception as e:
            raise ModelAPIError(self.model_name, f"Connection test failed: {str(e)}")
    
    async def generate_response(self, prompt: str, **kwargs) -> str:
        """응답 생성"""
        if not self._is_initialized:
            await self.initialize()
        
        try:
            response = await self._client.chat.completions.create(
                model=self.model_name,
                messages=[{"role": "user", "content": prompt}],
                max_tokens=kwargs.get('max_tokens', self.max_tokens),
                temperature=kwargs.get('temperature', self.temperature),
                response_format=kwargs.get('response_format', {"type": "text"})
            )
            
            return response.choices[0].message.content.strip()
            
        except Exception as e:
            raise ModelAPIError(self.model_name, f"Response generation failed: {str(e)}")
    
    async def classify_text(self, text: str, categories: List[str]) -> Tuple[str, float]:
        """텍스트 분류"""
        if not text.strip():
            return "기타", 0.5
        
        categories_str = ", ".join(categories)
        
        prompt = f"""
다음 텍스트를 주어진 카테고리 중 하나로 분류해주세요.

텍스트: {text}

카테고리: {categories_str}

응답은 다음 JSON 형식으로만 답변해주세요:
{{
    "category": "선택된_카테고리",
    "confidence": 0.85
}}
"""
        
        try:
            response = await self.generate_response(
                prompt,
                response_format={"type": "json_object"}
            )
            
            result = json.loads(response)
            category = result.get("category", "기타")
            confidence = float(result.get("confidence", 0.5))
            
            # 카테고리 검증
            if category not in categories:
                category = "기타"
                confidence = 0.5
            
            return category, confidence
            
        except Exception as e:
            print(f"⚠️ GPT 분류 실패: {e}")
            return "기타", 0.5
    
    async def analyze_sentiment(self, text: str) -> Tuple[str, float]:
        """감정 분석"""
        if not text.strip():
            return "중립", 0.5
        
        prompt = f"""
다음 텍스트의 감정을 분석해주세요.

텍스트: {text}

감정 카테고리: 긍정, 중립, 부정, 긴급

응답은 다음 JSON 형식으로만 답변해주세요:
{{
    "sentiment": "감정_카테고리",
    "confidence": 0.85
}}
"""
        
        try:
            response = await self.generate_response(
                prompt,
                response_format={"type": "json_object"}
            )
            
            result = json.loads(response)
            sentiment = result.get("sentiment", "중립")
            confidence = float(result.get("confidence", 0.5))
            
            return sentiment, confidence
            
        except Exception as e:
            print(f"⚠️ GPT 감정 분석 실패: {e}")
            return "중립", 0.5
    
    async def analyze_qa_pair_detailed(self, qa_pair: QAPair) -> Dict[str, Any]:
        """QA 쌍 상세 분석"""
        question = qa_pair.question.full_text
        answer = qa_pair.answer.content
        
        if not question.strip():
            return {"error": "Empty question"}
        
        prompt = f"""
다음 고객 질문과 답변을 분석해주세요.

질문: {question}
답변: {answer if answer and answer.strip() else "답변 없음"}

다음 항목들을 0.0-1.0 점수로 평가해주세요:
1. 관련성 (질문과 답변이 얼마나 관련있는가)
2. 완성도 (답변이 얼마나 완전한가)
3. 정확성 (답변이 얼마나 정확한가)
4. 도움도 (답변이 고객에게 얼마나 도움이 되는가)
5. 친절도 (답변이 얼마나 친절한가)

응답은 다음 JSON 형식으로만 답변해주세요:
{{
    "relevance": 0.85,
    "completeness": 0.90,
    "accuracy": 0.80,
    "helpfulness": 0.75,
    "politeness": 0.95,
    "overall_assessment": "전반적인 평가 한 줄",
    "strengths": ["강점1", "강점2"],
    "weaknesses": ["약점1", "약점2"],
    "recommendations": ["개선사항1", "개선사항2"]
}}
"""
        
        try:
            response = await self.generate_response(
                prompt,
                max_tokens=800,
                response_format={"type": "json_object"}
            )
            
            result = json.loads(response)
            
            # 점수 검증 및 기본값 설정
            scores = {}
            for key in ["relevance", "completeness", "accuracy", "helpfulness", "politeness"]:
                value = result.get(key, 0.5)
                scores[key] = max(0.0, min(1.0, float(value)))
            
            # 리스트 필드 검증
            for key in ["strengths", "weaknesses", "recommendations"]:
                if key not in result or not isinstance(result[key], list):
                    result[key] = []
            
            result.update(scores)
            return result
            
        except Exception as e:
            print(f"⚠️ GPT QA 분석 실패: {e}")
            return {
                "relevance": 0.5,
                "completeness": 0.5,
                "accuracy": 0.5,
                "helpfulness": 0.5,
                "politeness": 0.5,
                "overall_assessment": "분석 실패",
                "strengths": [],
                "weaknesses": ["분석 중 오류 발생"],
                "recommendations": ["다시 시도해주세요"]
            }
    
    async def generate_response_suggestion(self, question: str) -> str:
        """질문에 대한 답변 제안 생성"""
        if not question.strip():
            return "질문이 명확하지 않습니다."
        
        prompt = f"""
다음은 고객의 문의입니다. 친절하고 도움이 되는 답변을 작성해주세요.

고객 문의: {question}

답변 작성 가이드라인:
1. 정중하고 친절한 톤 사용
2. 구체적이고 실용적인 정보 제공
3. 추가 문의 채널 안내
4. 200자 내외로 작성

답변:
"""
        
        try:
            response = await self.generate_response(prompt, max_tokens=300)
            return response
            
        except Exception as e:
            print(f"⚠️ GPT 답변 제안 실패: {e}")
            return "죄송합니다. 답변 생성 중 오류가 발생했습니다. 고객센터로 문의해주세요."
    
    async def extract_keywords(self, text: str, max_keywords: int = 10) -> List[str]:
        """키워드 추출"""
        if not text.strip():
            return []
        
        prompt = f"""
다음 텍스트에서 중요한 키워드를 추출해주세요.

텍스트: {text}

최대 {max_keywords}개의 키워드를 중요도 순으로 추출해주세요.

응답은 다음 JSON 형식으로만 답변해주세요:
{{
    "keywords": ["키워드1", "키워드2", "키워드3"]
}}
"""
        
        try:
            response = await self.generate_response(
                prompt,
                response_format={"type": "json_object"}
            )
            
            result = json.loads(response)
            keywords = result.get("keywords", [])
            
            # 리스트 검증
            if isinstance(keywords, list):
                return keywords[:max_keywords]
            else:
                return []
                
        except Exception as e:
            print(f"⚠️ GPT 키워드 추출 실패: {e}")
            return []
    
    def _get_model_info_impl(self) -> ModelInfo:
        """모델 정보 반환"""
        return ModelInfo(
            name=self.model_name,
            provider="openai",
            version="1.0",
            description=f"OpenAI {self.model_name} for advanced language understanding and generation",
            supported_languages=["ko", "en", "ja", "zh", "es", "fr", "de"],
            max_input_length=self._get_max_input_length(),
            is_loaded=self._is_initialized
        )
    
    def _get_max_input_length(self) -> int:
        """모델별 최대 입력 길이"""
        model_limits = {
            "gpt-4o-mini": 128000,
            "gpt-4o": 128000,
            "gpt-4-turbo": 128000,
            "gpt-4": 8192,
            "gpt-3.5-turbo": 16385
        }
        return model_limits.get(self.model_name, 4096)
    
    def get_model_capabilities(self) -> Dict[str, bool]:
        """모델 기능 지원 여부"""
        return {
            "text_generation": True,
            "classification": True,
            "sentiment_analysis": True,
            "qa_analysis": True,
            "keyword_extraction": True,
            "json_output": True,
            "korean_language": True,
            "function_calling": self.model_name in ["gpt-4o-mini", "gpt-4o", "gpt-4-turbo"]
        }


def create_openai_gpt_model(
    model_name: str = "gpt-4o-mini",
    api_key: str = None,
    max_tokens: int = 500,
    temperature: float = 0.1
) -> OpenAIGPTModel:
    """OpenAI GPT 모델 생성 헬퍼 함수"""
    from qa_analysis_system.core.interfaces.ai_model_interface import ModelConfig, ModelProvider, ModelType
    
    config = ModelConfig(
        name=model_name,
        provider=ModelProvider.OPENAI,
        model_type=ModelType.LLM,
        api_key=api_key,
        max_tokens=max_tokens,
        temperature=temperature
    )
    
    return OpenAIGPTModel(config)
