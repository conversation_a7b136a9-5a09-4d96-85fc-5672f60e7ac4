"""
분석 요청 DTO (Data Transfer Object)
"""
from dataclasses import dataclass
from typing import Optional, Dict, Any
from enum import Enum


class AnalysisType(Enum):
    """분석 타입"""
    FULL = "full"
    SIMILARITY_ONLY = "similarity_only"
    QUALITY_ONLY = "quality_only"


@dataclass
class AnalysisRequest:
    """QA 분석 요청 DTO"""
    
    # 식별자 (둘 중 하나는 필수)
    inquiry_id: Optional[str] = None
    
    # 직접 텍스트 입력
    question_text: Optional[str] = None
    answer_text: Optional[str] = None
    
    # 분석 옵션
    analysis_type: str = "full"  # full, similarity_only, quality_only
    include_feedback: bool = True
    include_recommendations: bool = True
    
    # 메타데이터
    source: str = "manual"
    priority: str = "normal"  # low, normal, high, urgent
    
    # 추가 설정
    custom_weights: Optional[Dict[str, float]] = None
    custom_thresholds: Optional[Dict[str, float]] = None
    
    def __post_init__(self):
        """초기화 후 검증"""
        if not self.inquiry_id and not self.question_text:
            raise ValueError("Either inquiry_id or question_text must be provided")
        
        # 분석 타입 검증
        valid_types = ["full", "similarity_only", "quality_only"]
        if self.analysis_type not in valid_types:
            self.analysis_type = "full"
        
        # 우선순위 검증
        valid_priorities = ["low", "normal", "high", "urgent"]
        if self.priority not in valid_priorities:
            self.priority = "normal"
    
    def is_db_based(self) -> bool:
        """DB 기반 요청 여부"""
        return self.inquiry_id is not None
    
    def is_text_based(self) -> bool:
        """텍스트 기반 요청 여부"""
        return self.question_text is not None
    
    def to_dict(self) -> Dict[str, Any]:
        """딕셔너리 변환"""
        return {
            "inquiry_id": self.inquiry_id,
            "question_text": self.question_text,
            "answer_text": self.answer_text,
            "analysis_type": self.analysis_type,
            "include_feedback": self.include_feedback,
            "include_recommendations": self.include_recommendations,
            "source": self.source,
            "priority": self.priority,
            "custom_weights": self.custom_weights,
            "custom_thresholds": self.custom_thresholds
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'AnalysisRequest':
        """딕셔너리에서 생성"""
        return cls(
            inquiry_id=data.get("inquiry_id"),
            question_text=data.get("question_text"),
            answer_text=data.get("answer_text"),
            analysis_type=data.get("analysis_type", "full"),
            include_feedback=data.get("include_feedback", True),
            include_recommendations=data.get("include_recommendations", True),
            source=data.get("source", "manual"),
            priority=data.get("priority", "normal"),
            custom_weights=data.get("custom_weights"),
            custom_thresholds=data.get("custom_thresholds")
        )
    
    @classmethod
    def create_by_id(
        cls, 
        inquiry_id: str, 
        analysis_type: str = "full",
        include_feedback: bool = True
    ) -> 'AnalysisRequest':
        """ID 기반 요청 생성"""
        return cls(
            inquiry_id=inquiry_id,
            analysis_type=analysis_type,
            include_feedback=include_feedback,
            source="database"
        )
    
    @classmethod
    def create_by_text(
        cls,
        question_text: str,
        answer_text: str = "",
        analysis_type: str = "full",
        include_feedback: bool = True
    ) -> 'AnalysisRequest':
        """텍스트 기반 요청 생성"""
        return cls(
            question_text=question_text,
            answer_text=answer_text,
            analysis_type=analysis_type,
            include_feedback=include_feedback,
            source="manual"
        )


@dataclass
class BatchAnalysisRequest:
    """배치 분석 요청 DTO"""
    
    # 요청 목록
    requests: list[AnalysisRequest]
    
    # 배치 설정
    batch_size: int = 10
    max_workers: int = 4
    timeout_seconds: int = 300
    
    # 출력 설정
    export_format: str = "json"  # json, csv, excel
    export_filename: Optional[str] = None
    include_summary: bool = True
    
    # 필터링 설정
    min_confidence: float = 0.0
    only_failed: bool = False
    
    def __post_init__(self):
        """초기화 후 검증"""
        if not self.requests:
            raise ValueError("At least one request must be provided")
        
        # 배치 크기 검증
        if self.batch_size <= 0:
            self.batch_size = 10
        
        # 워커 수 검증
        if self.max_workers <= 0:
            self.max_workers = 4
    
    def get_total_requests(self) -> int:
        """총 요청 수"""
        return len(self.requests)
    
    def get_estimated_time(self) -> float:
        """예상 처리 시간 (초)"""
        # 요청당 평균 2초로 추정
        avg_time_per_request = 2.0
        return (self.get_total_requests() * avg_time_per_request) / self.max_workers
    
    def to_dict(self) -> Dict[str, Any]:
        """딕셔너리 변환"""
        return {
            "requests": [req.to_dict() for req in self.requests],
            "batch_size": self.batch_size,
            "max_workers": self.max_workers,
            "timeout_seconds": self.timeout_seconds,
            "export_format": self.export_format,
            "export_filename": self.export_filename,
            "include_summary": self.include_summary,
            "min_confidence": self.min_confidence,
            "only_failed": self.only_failed
        }


# 편의 함수들
def create_single_analysis_request(
    inquiry_id: str = None,
    question: str = None,
    answer: str = None,
    analysis_type: str = "full"
) -> AnalysisRequest:
    """단일 분석 요청 생성 편의 함수"""
    if inquiry_id:
        return AnalysisRequest.create_by_id(inquiry_id, analysis_type)
    elif question:
        return AnalysisRequest.create_by_text(question, answer or "", analysis_type)
    else:
        raise ValueError("Either inquiry_id or question must be provided")


def create_batch_analysis_request(
    inquiry_ids: list[str] = None,
    text_pairs: list[tuple[str, str]] = None,
    analysis_type: str = "full",
    batch_size: int = 10
) -> BatchAnalysisRequest:
    """배치 분석 요청 생성 편의 함수"""
    requests = []
    
    if inquiry_ids:
        requests.extend([
            AnalysisRequest.create_by_id(inquiry_id, analysis_type)
            for inquiry_id in inquiry_ids
        ])
    
    if text_pairs:
        requests.extend([
            AnalysisRequest.create_by_text(question, answer, analysis_type)
            for question, answer in text_pairs
        ])
    
    if not requests:
        raise ValueError("Either inquiry_ids or text_pairs must be provided")
    
    return BatchAnalysisRequest(
        requests=requests,
        batch_size=batch_size
    )
