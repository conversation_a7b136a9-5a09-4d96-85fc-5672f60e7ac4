"""
AI 모델 추상화 인터페이스
"""
from abc import ABC, abstractmethod
from typing import List, Tuple, Dict, Any, Optional
from dataclasses import dataclass
from enum import Enum


class ModelType(Enum):
    """모델 타입"""
    EMBEDDING = "embedding"
    LLM = "llm"
    CLASSIFICATION = "classification"


@dataclass
class ModelConfig:
    """모델 설정"""
    model_name: str
    model_type: ModelType
    provider: str  # huggingface, openai, anthropic, google, etc.
    model_path: Optional[str] = None
    api_key: Optional[str] = None
    max_tokens: Optional[int] = None
    temperature: Optional[float] = None
    additional_params: Optional[Dict[str, Any]] = None


class BaseEmbeddingModel(ABC):
    """임베딩 모델 기본 인터페이스"""
    
    def __init__(self, config: ModelConfig):
        self.config = config
        self.model = None
    
    @abstractmethod
    def load_model(self) -> None:
        """모델 로드"""
        pass
    
    @abstractmethod
    def encode(self, texts: List[str]) -> List[List[float]]:
        """텍스트를 임베딩으로 변환"""
        pass
    
    @abstractmethod
    def encode_single(self, text: str) -> List[float]:
        """단일 텍스트를 임베딩으로 변환"""
        pass
    
    @abstractmethod
    def calculate_similarity(self, text1: str, text2: str) -> float:
        """두 텍스트 간 유사도 계산"""
        pass
    
    @abstractmethod
    def classify_text(self, text: str) -> Tuple[str, float]:
        """텍스트 분류 (카테고리, 신뢰도)"""
        pass


class BaseLLMModel(ABC):
    """LLM 모델 기본 인터페이스"""
    
    def __init__(self, config: ModelConfig):
        self.config = config
        self.client = None
    
    @abstractmethod
    def initialize(self) -> None:
        """모델 초기화"""
        pass
    
    @abstractmethod
    async def generate_response(self, prompt: str, **kwargs) -> str:
        """응답 생성"""
        pass
    
    @abstractmethod
    async def classify_inquiry(self, content: str) -> Tuple[str, float]:
        """문의 분류"""
        pass
    
    @abstractmethod
    async def analyze_qa_pair(self, question: str, answer: str) -> Dict[str, Any]:
        """질문-답변 쌍 분석"""
        pass


class BaseClassificationModel(ABC):
    """분류 모델 기본 인터페이스"""
    
    def __init__(self, config: ModelConfig):
        self.config = config
        self.model = None
    
    @abstractmethod
    def load_model(self) -> None:
        """모델 로드"""
        pass
    
    @abstractmethod
    def predict(self, text: str) -> Tuple[str, float]:
        """텍스트 분류 예측"""
        pass
    
    @abstractmethod
    def predict_batch(self, texts: List[str]) -> List[Tuple[str, float]]:
        """배치 분류 예측"""
        pass


# 구체적인 구현 클래스들

class HuggingFaceEmbeddingModel(BaseEmbeddingModel):
    """HuggingFace 임베딩 모델"""
    
    def load_model(self) -> None:
        """SentenceTransformer 모델 로드"""
        try:
            from sentence_transformers import SentenceTransformer
            self.model = SentenceTransformer(self.config.model_name)
            print(f"✅ HuggingFace 모델 로드 완료: {self.config.model_name}")
        except Exception as e:
            raise Exception(f"HuggingFace 모델 로드 실패: {e}")
    
    def encode(self, texts: List[str]) -> List[List[float]]:
        """텍스트 리스트를 임베딩으로 변환"""
        if not self.model:
            self.load_model()
        return self.model.encode(texts).tolist()
    
    def encode_single(self, text: str) -> List[float]:
        """단일 텍스트를 임베딩으로 변환"""
        return self.encode([text])[0]
    
    def calculate_similarity(self, text1: str, text2: str) -> float:
        """코사인 유사도 계산"""
        from sklearn.metrics.pairwise import cosine_similarity
        import numpy as np
        
        embeddings = self.encode([text1, text2])
        similarity = cosine_similarity([embeddings[0]], [embeddings[1]])[0][0]
        return float(similarity)
    
    def classify_text(self, text: str) -> Tuple[str, float]:
        """간단한 키워드 기반 분류 (임시)"""
        # 실제로는 더 정교한 분류 로직 필요
        categories = {
            "배송": ["배송", "택배", "발송", "도착", "언제"],
            "환불": ["환불", "취소", "반품", "교환"],
            "상품": ["상품", "제품", "품질", "사이즈"],
            "결제": ["결제", "카드", "계좌", "입금"],
            "기타": []
        }
        
        text_lower = text.lower()
        for category, keywords in categories.items():
            if any(keyword in text_lower for keyword in keywords):
                confidence = len([k for k in keywords if k in text_lower]) / max(len(keywords), 1)
                return category, min(confidence * 2, 1.0)
        
        return "기타", 0.5


class OpenAILLMModel(BaseLLMModel):
    """OpenAI LLM 모델"""
    
    def initialize(self) -> None:
        """OpenAI 클라이언트 초기화"""
        try:
            from openai import AsyncOpenAI
            self.client = AsyncOpenAI(api_key=self.config.api_key)
            print(f"✅ OpenAI 모델 초기화 완료: {self.config.model_name}")
        except Exception as e:
            raise Exception(f"OpenAI 모델 초기화 실패: {e}")
    
    async def generate_response(self, prompt: str, **kwargs) -> str:
        """응답 생성"""
        if not self.client:
            self.initialize()
        
        try:
            response = await self.client.chat.completions.create(
                model=self.config.model_name,
                messages=[{"role": "user", "content": prompt}],
                max_tokens=kwargs.get('max_tokens', self.config.max_tokens or 150),
                temperature=kwargs.get('temperature', self.config.temperature or 0.1)
            )
            return response.choices[0].message.content.strip()
        except Exception as e:
            raise Exception(f"OpenAI 응답 생성 실패: {e}")
    
    async def classify_inquiry(self, content: str) -> Tuple[str, float]:
        """문의 분류"""
        prompt = f"""
다음 고객 문의를 카테고리로 분류해주세요:

문의: {content}

카테고리: 배송/환불/상품/결제/기타 중 하나
신뢰도: 0.0-1.0

응답 형식:
카테고리: [카테고리]
신뢰도: [숫자]
"""
        
        try:
            response = await self.generate_response(prompt)
            # 간단한 파싱 로직
            lines = response.split('\n')
            category = "기타"
            confidence = 0.5
            
            for line in lines:
                if line.startswith('카테고리:'):
                    category = line.split(':')[1].strip()
                elif line.startswith('신뢰도:'):
                    try:
                        confidence = float(line.split(':')[1].strip())
                    except:
                        confidence = 0.5
            
            return category, confidence
        except Exception as e:
            print(f"LLM 분류 실패: {e}")
            return "기타", 0.5
    
    async def analyze_qa_pair(self, question: str, answer: str) -> Dict[str, Any]:
        """질문-답변 쌍 분석"""
        prompt = f"""
다음 질문-답변 쌍을 분석해주세요:

질문: {question}
답변: {answer}

다음 항목들을 0.0-1.0 점수로 평가해주세요:
1. 관련성 (질문과 답변이 얼마나 관련있는가)
2. 완성도 (답변이 얼마나 완전한가)
3. 도움도 (답변이 얼마나 도움이 되는가)

응답 형식:
관련성: [점수]
완성도: [점수]
도움도: [점수]
"""
        
        try:
            response = await self.generate_response(prompt)
            # 파싱 로직
            result = {"relevance": 0.5, "completeness": 0.5, "helpfulness": 0.5}
            
            lines = response.split('\n')
            for line in lines:
                if '관련성:' in line:
                    try:
                        result["relevance"] = float(line.split(':')[1].strip())
                    except:
                        pass
                elif '완성도:' in line:
                    try:
                        result["completeness"] = float(line.split(':')[1].strip())
                    except:
                        pass
                elif '도움도:' in line:
                    try:
                        result["helpfulness"] = float(line.split(':')[1].strip())
                    except:
                        pass
            
            return result
        except Exception as e:
            print(f"LLM QA 분석 실패: {e}")
            return {"relevance": 0.5, "completeness": 0.5, "helpfulness": 0.5}


class AnthropicLLMModel(BaseLLMModel):
    """Anthropic Claude 모델"""
    
    def initialize(self) -> None:
        """Anthropic 클라이언트 초기화"""
        try:
            import anthropic
            self.client = anthropic.AsyncAnthropic(api_key=self.config.api_key)
            print(f"✅ Anthropic 모델 초기화 완료: {self.config.model_name}")
        except Exception as e:
            raise Exception(f"Anthropic 모델 초기화 실패: {e}")
    
    async def generate_response(self, prompt: str, **kwargs) -> str:
        """응답 생성"""
        if not self.client:
            self.initialize()
        
        try:
            response = await self.client.messages.create(
                model=self.config.model_name,
                max_tokens=kwargs.get('max_tokens', self.config.max_tokens or 150),
                temperature=kwargs.get('temperature', self.config.temperature or 0.1),
                messages=[{"role": "user", "content": prompt}]
            )
            return response.content[0].text.strip()
        except Exception as e:
            raise Exception(f"Anthropic 응답 생성 실패: {e}")
    
    async def classify_inquiry(self, content: str) -> Tuple[str, float]:
        """문의 분류 (OpenAI와 동일한 로직)"""
        # OpenAI와 동일한 구현
        return await OpenAILLMModel.classify_inquiry(self, content)
    
    async def analyze_qa_pair(self, question: str, answer: str) -> Dict[str, Any]:
        """질문-답변 쌍 분석 (OpenAI와 동일한 로직)"""
        # OpenAI와 동일한 구현
        return await OpenAILLMModel.analyze_qa_pair(self, question, answer)


class GoogleEmbeddingModel(BaseEmbeddingModel):
    """Google Universal Sentence Encoder"""
    
    def load_model(self) -> None:
        """Google USE 모델 로드"""
        try:
            import tensorflow_hub as hub
            self.model = hub.load(self.config.model_name)
            print(f"✅ Google 모델 로드 완료: {self.config.model_name}")
        except Exception as e:
            raise Exception(f"Google 모델 로드 실패: {e}")
    
    def encode(self, texts: List[str]) -> List[List[float]]:
        """텍스트 리스트를 임베딩으로 변환"""
        if not self.model:
            self.load_model()
        embeddings = self.model(texts)
        return embeddings.numpy().tolist()
    
    def encode_single(self, text: str) -> List[float]:
        """단일 텍스트를 임베딩으로 변환"""
        return self.encode([text])[0]
    
    def calculate_similarity(self, text1: str, text2: str) -> float:
        """코사인 유사도 계산"""
        from sklearn.metrics.pairwise import cosine_similarity
        embeddings = self.encode([text1, text2])
        similarity = cosine_similarity([embeddings[0]], [embeddings[1]])[0][0]
        return float(similarity)
    
    def classify_text(self, text: str) -> Tuple[str, float]:
        """키워드 기반 분류 (HuggingFace와 동일)"""
        return HuggingFaceEmbeddingModel.classify_text(self, text)


# 모델 팩토리
class ModelFactory:
    """모델 팩토리"""
    
    _embedding_models = {
        "huggingface": HuggingFaceEmbeddingModel,
        "google": GoogleEmbeddingModel,
    }
    
    _llm_models = {
        "openai": OpenAILLMModel,
        "anthropic": AnthropicLLMModel,
    }
    
    @classmethod
    def create_embedding_model(cls, config: ModelConfig) -> BaseEmbeddingModel:
        """임베딩 모델 생성"""
        if config.provider not in cls._embedding_models:
            raise ValueError(f"지원하지 않는 임베딩 모델 제공자: {config.provider}")
        
        model_class = cls._embedding_models[config.provider]
        return model_class(config)
    
    @classmethod
    def create_llm_model(cls, config: ModelConfig) -> BaseLLMModel:
        """LLM 모델 생성"""
        if config.provider not in cls._llm_models:
            raise ValueError(f"지원하지 않는 LLM 모델 제공자: {config.provider}")
        
        model_class = cls._llm_models[config.provider]
        return model_class(config)
    
    @classmethod
    def register_embedding_model(cls, provider: str, model_class: type):
        """새로운 임베딩 모델 등록"""
        cls._embedding_models[provider] = model_class
    
    @classmethod
    def register_llm_model(cls, provider: str, model_class: type):
        """새로운 LLM 모델 등록"""
        cls._llm_models[provider] = model_class
    
    @classmethod
    def list_available_models(cls) -> Dict[str, List[str]]:
        """사용 가능한 모델 목록"""
        return {
            "embedding": list(cls._embedding_models.keys()),
            "llm": list(cls._llm_models.keys())
        }
