"""
개인 문의 데이터를 처리하기 위한 서비스 모듈
"""
from typing import Any, Dict, List
import math

from database.database import RepositoryFactory, SupabaseDB


class PersonalInquiryService:
    """개인 문의 테이블 처리를 위한 서비스 클래스"""

    def __init__(self, url: str, key: str):
        """
        개인 문의 서비스 초기화

        Args:
            url: Supabase URL
            key: Supabase API 키
        """
        # None 체크 추가
        if not url or not key:
            raise ValueError("Supabase URL과 API 키는 필수입니다")

        self.db = SupabaseDB(url, key)
        self.factory = RepositoryFactory(self.db)
        self.repository = self.factory.create_personal_inquiry_repository()

    def get_all_inquiries(self) -> List[Dict[str, Any]]:
        """모든 개인 문의 조회"""
        return self.repository.get_all()

    def get_inquiry_by_id(self, inquiry_id: str) -> Dict[str, Any]:
        """ID로 특정 문의 조회"""
        return self.repository.get_by_id(inquiry_id)

    def get_inquiries_by_customer(self, customer: str) -> List[Dict[str, Any]]:
        """고객명으로 문의 필터링"""
        return self.repository.get_by_customer(customer)

    def get_inquiries_by_document(self, doc_id: str) -> List[Dict[str, Any]]:
        """문서 ID로 문의 필터링"""
        # 문서 ID는 별도의 필드로 저장되어 있다고 가정
        return self.repository.get_by_document(doc_id)

    def get_answered_inquiries(self) -> List[Dict[str, Any]]:
        """답변된 문의만 필터링"""
        all_inquiries = self.repository.get_all()
        return [
            inquiry for inquiry in all_inquiries
            if inquiry.get('answered_at')
        ]

    def get_unanswered_inquiries(self) -> List[Dict[str, Any]]:
        """미답변 문의만 필터링"""
        all_inquiries = self.repository.get_all()
        return [
            inquiry for inquiry in all_inquiries
            if not inquiry.get('answered_at')
        ]
    
    def format_inquiry_for_display(
        self, inquiry: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        출력용으로 문의 데이터 형식 변환

        Args:
            inquiry: 원본 문의 데이터

        Returns:
            포맷팅된 문의 데이터
        """
        # 날짜 형식 변환 예시
        created_at = inquiry.get('created_at', '')

        # 컨텐츠 길이 제한
        content = inquiry.get('content', '')
        if content and len(content) > 100:
            content = content[:100] + '...'

        answer = inquiry.get('answer', '')
        if answer and len(answer) > 100:
            answer = answer[:100] + '...'

        # 문의 요약 정보
        return {
            'id': inquiry.get('id', ''),
            'customer': inquiry.get('customer', '익명'),
            'subject': inquiry.get('subject', '제목 없음'),
            'content': content,
            'created_at': created_at,
            'answered_at': inquiry.get('answered_at', ''),
            'answer': answer,
            'is_answered': bool(inquiry.get('answered_at')),
        }

    def print_inquiry_summary(self, inquiries: List[Dict[str, Any]]) -> None:
        """
        문의 목록 요약 출력

        Args:
            inquiries: 출력할 문의 목록
        """
        print(f"\n[개인 문의 목록] - 총 {len(inquiries)}건")
        print("=" * 80)

        if not inquiries:
            print("데이터가 없습니다.")
            return

        print(f"{'고객명':<10} {'제목':<20} {'작성일':<15} {'답변여부':<10}")
        print("-" * 80)

        for inquiry in inquiries:
            fmt = self.format_inquiry_for_display(inquiry)
            status = "✅" if fmt['is_answered'] else "❌"
            print(
                f"{fmt['customer']:<10} {fmt['subject']:<20} "
                f"{fmt['created_at']:<15} {status:<10}"
            )

        print("=" * 80)


def get_personal_inquiries_summary(url: str, key: str) -> None:
    """
    개인 문의 요약 정보 출력 - 외부 호출용 함수

    Args:
        url: Supabase URL
        key: Supabase API 키
    """
    try:
        service = PersonalInquiryService(url, key)
        inquiries = service.get_all_inquiries()

        # 전체 문의 요약
        service.print_inquiry_summary(inquiries)

        # 답변/미답변 문의 수
        answered = service.get_answered_inquiries()
        unanswered = service.get_unanswered_inquiries()

        print("\n[통계 정보]")
        print(f"- 총 문의: {len(inquiries)}건")
        print(f"- 답변 완료: {len(answered)}건")
        print(f"- 미답변: {len(unanswered)}건")

        # 답변률 계산
        if inquiries and isinstance(inquiries, list) and len(inquiries) > 0:
            answer_rate = len(answered) / len(inquiries) * 100
        else:
            answer_rate = 0

        # NaN, None, 잘못된 타입 모두 방지
        is_valid_rate = (
            answer_rate is not None and
            isinstance(answer_rate, (int, float)) and
            not math.isnan(answer_rate)
        )

        if is_valid_rate:
            print(f"- 답변률: {answer_rate:.1f}%")
        else:
            print("- 답변률: 정보 없음")

    except (ValueError, ConnectionError) as e:
        print(f"개인 문의 조회 중 오류 발생: {e}")
    except Exception as e:
        print(f"예상치 못한 오류 발생: {e}")