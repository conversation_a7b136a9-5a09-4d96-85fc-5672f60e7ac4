"""
개별 질문-답변 쌍 분석 시스템
"""
import asyncio
import json
import numpy as np
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass, asdict
from sklearn.metrics.pairwise import cosine_similarity
import re

from src.services.embedding_model_service import EmbeddingModelService
from src.services.llm_service import LLMService
from src.services.async_inquiry_service import AsyncInquiryService


@dataclass
class QAPairScore:
    """질문-답변 쌍 점수"""
    # 기본 정보
    inquiry_id: str
    question_text: str
    answer_text: str
    
    # 유사도 점수
    semantic_similarity: float  # 의미적 유사도 (0-1)
    topic_relevance: float     # 주제 관련성 (0-1)
    keyword_overlap: float     # 키워드 겹침도 (0-1)
    
    # 품질 점수
    answer_completeness: float  # 답변 완성도 (0-1)
    answer_accuracy: float     # 답변 정확성 (0-1)
    answer_helpfulness: float  # 답변 도움도 (0-1)
    
    # 종합 점수
    overall_score: float       # 전체 점수 (0-1)
    pass_threshold: bool       # 통과 여부
    grade: str                # 등급 (A/B/C/D/F)
    
    # 상세 분석
    strengths: List[str]       # 강점
    weaknesses: List[str]      # 약점
    recommendations: List[str] # 개선 권장사항
    
    def to_dict(self) -> Dict:
        """딕셔너리 변환"""
        return asdict(self)


class QAPairAnalyzer:
    """질문-답변 쌍 분석기"""
    
    def __init__(self, supabase_url: str, supabase_key: str):
        """
        분석기 초기화
        
        Args:
            supabase_url: Supabase URL
            supabase_key: Supabase API 키
        """
        self.async_service = AsyncInquiryService(supabase_url, supabase_key)
        self.embedding_service = None
        self.llm_service = None
        
        # 통과 기준 설정
        self.pass_threshold = 0.6  # 60% 이상 통과
        
        # 등급 기준
        self.grade_thresholds = {
            'A': 0.9,  # 90% 이상
            'B': 0.8,  # 80% 이상
            'C': 0.7,  # 70% 이상
            'D': 0.6,  # 60% 이상
            'F': 0.0   # 60% 미만
        }
        
        # 한국어 불용어
        self.stopwords = {
            '은', '는', '이', '가', '을', '를', '에', '의', '와', '과', '도', 
            '만', '부터', '까지', '로', '으로', '에서', '께서', '한테', '안녕하세요',
            '감사합니다', '죄송합니다', '고객님', '문의', '요청', '부탁드립니다'
        }
    
    def _init_services(self):
        """서비스 초기화"""
        if self.embedding_service is None:
            try:
                self.embedding_service = EmbeddingModelService()
                print("✅ 임베딩 서비스 초기화 완료")
            except Exception as e:
                print(f"⚠️ 임베딩 서비스 초기화 실패: {e}")
        
        if self.llm_service is None:
            try:
                from config.config import OPENAI_API_KEY
                if OPENAI_API_KEY:
                    self.llm_service = LLMService()
                    print("✅ LLM 서비스 초기화 완료")
            except Exception as e:
                print(f"⚠️ LLM 서비스 초기화 실패: {e}")
    
    def _extract_keywords(self, text: str) -> List[str]:
        """텍스트에서 키워드 추출"""
        if not text:
            return []
        
        # 한글, 영문, 숫자만 추출
        words = re.findall(r'[가-힣a-zA-Z0-9]+', text.lower())
        
        # 불용어 제거 및 길이 필터링
        keywords = [word for word in words 
                   if len(word) > 1 and word not in self.stopwords]
        
        return list(set(keywords))  # 중복 제거
    
    def _calculate_semantic_similarity(self, question: str, answer: str) -> float:
        """의미적 유사도 계산"""
        if not self.embedding_service or not question or not answer:
            return 0.0
        
        try:
            # 임베딩 생성
            question_embedding = self.embedding_service.model.encode([question])
            answer_embedding = self.embedding_service.model.encode([answer])
            
            # 코사인 유사도 계산
            similarity = cosine_similarity(question_embedding, answer_embedding)[0][0]
            return float(similarity)
            
        except Exception as e:
            print(f"⚠️ 의미적 유사도 계산 실패: {e}")
            return 0.0
    
    def _calculate_topic_relevance(self, question: str, answer: str) -> float:
        """주제 관련성 계산"""
        if not question or not answer:
            return 0.0
        
        # 질문에서 주요 주제어 추출
        question_keywords = self._extract_keywords(question)
        answer_keywords = self._extract_keywords(answer)
        
        if not question_keywords:
            return 0.0
        
        # 주제어 매칭 점수
        matched_keywords = set(question_keywords) & set(answer_keywords)
        relevance_score = len(matched_keywords) / len(question_keywords)
        
        # 특정 주제별 가중치 적용
        topic_weights = {
            '배송': 1.2, '택배': 1.2, '발송': 1.2,
            '환불': 1.3, '취소': 1.3, '반품': 1.3,
            '상품': 1.1, '제품': 1.1, '품질': 1.1,
            '결제': 1.2, '카드': 1.2, '계좌': 1.2
        }
        
        # 가중치 적용
        weighted_score = relevance_score
        for keyword in matched_keywords:
            if keyword in topic_weights:
                weighted_score *= topic_weights[keyword]
        
        return min(1.0, weighted_score)
    
    def _calculate_keyword_overlap(self, question: str, answer: str) -> float:
        """키워드 겹침도 계산"""
        question_keywords = set(self._extract_keywords(question))
        answer_keywords = set(self._extract_keywords(answer))
        
        if not question_keywords:
            return 0.0
        
        overlap = len(question_keywords & answer_keywords)
        total = len(question_keywords | answer_keywords)
        
        return overlap / total if total > 0 else 0.0
    
    def _evaluate_answer_quality(self, question: str, answer: str) -> Tuple[float, float, float]:
        """답변 품질 평가 (완성도, 정확성, 도움도)"""
        if not answer or answer.strip() == "nan":
            return 0.0, 0.0, 0.0
        
        # 완성도 평가
        completeness = min(1.0, len(answer) / 200)  # 200자 기준
        
        # 정중함 보너스
        if any(word in answer for word in ["안녕하세요", "감사합니다", "죄송합니다"]):
            completeness += 0.1
        
        # 구체적 정보 보너스
        if any(word in answer for word in ["주문번호", "운송장", "연락드리겠습니다", "처리"]):
            completeness += 0.2
        
        completeness = min(1.0, completeness)
        
        # 정확성 평가 (키워드 기반)
        accuracy = self._calculate_topic_relevance(question, answer)
        
        # 도움도 평가
        helpfulness = 0.5  # 기본 점수
        
        # 해결 지향적 표현
        if any(word in answer for word in ["해결", "처리", "완료", "안내", "확인"]):
            helpfulness += 0.3
        
        # 추가 지원 제안
        if any(word in answer for word in ["고객센터", "문의", "연락", "게시판"]):
            helpfulness += 0.2
        
        helpfulness = min(1.0, helpfulness)
        
        return completeness, accuracy, helpfulness
    
    def _determine_grade(self, score: float) -> str:
        """점수에 따른 등급 결정"""
        for grade, threshold in self.grade_thresholds.items():
            if score >= threshold:
                return grade
        return 'F'
    
    def _generate_feedback(
        self, 
        question: str, 
        answer: str, 
        scores: Dict[str, float]
    ) -> Tuple[List[str], List[str], List[str]]:
        """피드백 생성 (강점, 약점, 개선사항)"""
        strengths = []
        weaknesses = []
        recommendations = []
        
        # 강점 분석
        if scores['semantic_similarity'] > 0.7:
            strengths.append("질문과 답변의 의미적 연관성이 높음")
        
        if scores['answer_completeness'] > 0.8:
            strengths.append("답변이 충분히 상세함")
        
        if "안녕하세요" in answer and "감사합니다" in answer:
            strengths.append("정중하고 친절한 답변 톤")
        
        if any(word in answer for word in ["주문번호", "운송장", "처리"]):
            strengths.append("구체적인 정보 제공")
        
        # 약점 분석
        if scores['semantic_similarity'] < 0.5:
            weaknesses.append("질문과 답변의 연관성이 낮음")
        
        if scores['answer_completeness'] < 0.5:
            weaknesses.append("답변이 너무 간단함")
        
        if scores['topic_relevance'] < 0.6:
            weaknesses.append("질문의 핵심 주제를 충분히 다루지 못함")
        
        if not answer or answer.strip() == "nan":
            weaknesses.append("답변이 없음")
        
        # 개선사항 제안
        if scores['semantic_similarity'] < 0.6:
            recommendations.append("질문의 핵심 키워드를 답변에 더 많이 포함")
        
        if scores['answer_completeness'] < 0.7:
            recommendations.append("더 상세하고 구체적인 답변 제공")
        
        if scores['answer_helpfulness'] < 0.7:
            recommendations.append("고객이 다음에 취할 수 있는 구체적 행동 안내")
        
        if not any(word in answer for word in ["고객센터", "문의"]):
            recommendations.append("추가 문의 채널 안내 추가")
        
        return strengths, weaknesses, recommendations
    
    async def analyze_qa_pair(self, inquiry_id: str) -> Optional[QAPairScore]:
        """개별 질문-답변 쌍 분석"""
        self._init_services()
        
        try:
            # 데이터 조회
            inquiries = await self.async_service.get_all_inquiries(
                table='personal',
                limit=1000
            )
            
            # 해당 ID 찾기
            target_inquiry = None
            for inquiry in inquiries:
                if inquiry.get('id') == inquiry_id:
                    target_inquiry = inquiry
                    break
            
            if not target_inquiry:
                print(f"❌ ID {inquiry_id}에 해당하는 문의를 찾을 수 없습니다.")
                return None
            
            # 텍스트 추출
            subject = target_inquiry.get('subject', '') or ''
            content = target_inquiry.get('content', '') or ''
            answer = target_inquiry.get('answer', '') or ''
            
            question_text = f"{subject} {content}".strip()
            
            if not question_text:
                print(f"❌ 질문 내용이 없습니다.")
                return None
            
            print(f"📝 분석 중: {question_text[:50]}...")
            
            # 유사도 점수 계산
            semantic_similarity = self._calculate_semantic_similarity(question_text, answer)
            topic_relevance = self._calculate_topic_relevance(question_text, answer)
            keyword_overlap = self._calculate_keyword_overlap(question_text, answer)
            
            # 품질 점수 계산
            completeness, accuracy, helpfulness = self._evaluate_answer_quality(question_text, answer)
            
            # 종합 점수 계산 (가중평균)
            overall_score = (
                semantic_similarity * 0.25 +
                topic_relevance * 0.25 +
                keyword_overlap * 0.15 +
                completeness * 0.15 +
                accuracy * 0.10 +
                helpfulness * 0.10
            )
            
            # 통과 여부 및 등급
            pass_threshold = overall_score >= self.pass_threshold
            grade = self._determine_grade(overall_score)
            
            # 피드백 생성
            scores_dict = {
                'semantic_similarity': semantic_similarity,
                'topic_relevance': topic_relevance,
                'keyword_overlap': keyword_overlap,
                'answer_completeness': completeness,
                'answer_accuracy': accuracy,
                'answer_helpfulness': helpfulness
            }
            
            strengths, weaknesses, recommendations = self._generate_feedback(
                question_text, answer, scores_dict
            )
            
            return QAPairScore(
                inquiry_id=inquiry_id,
                question_text=question_text,
                answer_text=answer,
                semantic_similarity=semantic_similarity,
                topic_relevance=topic_relevance,
                keyword_overlap=keyword_overlap,
                answer_completeness=completeness,
                answer_accuracy=accuracy,
                answer_helpfulness=helpfulness,
                overall_score=overall_score,
                pass_threshold=pass_threshold,
                grade=grade,
                strengths=strengths,
                weaknesses=weaknesses,
                recommendations=recommendations
            )
            
        except Exception as e:
            print(f"❌ 분석 중 오류: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    async def analyze_multiple_pairs(self, inquiry_ids: List[str]) -> List[QAPairScore]:
        """여러 질문-답변 쌍 분석"""
        results = []
        
        for inquiry_id in inquiry_ids:
            result = await self.analyze_qa_pair(inquiry_id)
            if result:
                results.append(result)
        
        return results
    
    def export_qa_analysis(
        self, 
        results: List[QAPairScore], 
        filename: str = "qa_pair_analysis.json"
    ) -> Dict:
        """분석 결과 JSON 내보내기"""
        if not results:
            return {}
        
        # 통계 계산
        total_count = len(results)
        pass_count = sum(1 for r in results if r.pass_threshold)
        avg_score = sum(r.overall_score for r in results) / total_count
        
        grade_distribution = {}
        for result in results:
            grade_distribution[result.grade] = grade_distribution.get(result.grade, 0) + 1
        
        output = {
            "metadata": {
                "total_pairs": total_count,
                "pass_count": pass_count,
                "pass_rate": (pass_count / total_count * 100) if total_count > 0 else 0,
                "average_score": round(avg_score, 3),
                "analysis_timestamp": __import__('time').strftime("%Y-%m-%d %H:%M:%S")
            },
            "statistics": {
                "grade_distribution": grade_distribution,
                "score_ranges": {
                    "excellent": sum(1 for r in results if r.overall_score >= 0.9),
                    "good": sum(1 for r in results if 0.8 <= r.overall_score < 0.9),
                    "fair": sum(1 for r in results if 0.7 <= r.overall_score < 0.8),
                    "poor": sum(1 for r in results if r.overall_score < 0.7)
                }
            },
            "qa_pairs": [result.to_dict() for result in results]
        }
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(output, f, ensure_ascii=False, indent=2)
            print(f"📄 QA 분석 결과를 {filename}에 저장했습니다.")
        except Exception as e:
            print(f"⚠️ 파일 저장 실패: {e}")
        
        return output
    
    def print_qa_analysis_summary(self, results: List[QAPairScore]):
        """QA 분석 결과 요약 출력"""
        if not results:
            print("분석 결과가 없습니다.")
            return
        
        print("\n" + "="*80)
        print("🔍 개별 질문-답변 쌍 분석 결과")
        print("="*80)
        
        total = len(results)
        passed = sum(1 for r in results if r.pass_threshold)
        avg_score = sum(r.overall_score for r in results) / total
        
        print(f"📊 전체 통계:")
        print(f"  • 총 분석 쌍: {total}개")
        print(f"  • 통과: {passed}개 ({passed/total*100:.1f}%)")
        print(f"  • 평균 점수: {avg_score:.3f}")
        
        # 등급별 분포
        grade_counts = {}
        for result in results:
            grade_counts[result.grade] = grade_counts.get(result.grade, 0) + 1
        
        print(f"\n🏆 등급 분포:")
        for grade in ['A', 'B', 'C', 'D', 'F']:
            count = grade_counts.get(grade, 0)
            if count > 0:
                print(f"  • {grade}등급: {count}개")
        
        # 상위/하위 결과
        sorted_results = sorted(results, key=lambda x: x.overall_score, reverse=True)
        
        print(f"\n🥇 최고 점수 (상위 3개):")
        for i, result in enumerate(sorted_results[:3], 1):
            print(f"  {i}. {result.overall_score:.3f} ({result.grade}) - {result.question_text[:50]}...")
        
        print(f"\n🔻 최저 점수 (하위 3개):")
        for i, result in enumerate(sorted_results[-3:], 1):
            print(f"  {i}. {result.overall_score:.3f} ({result.grade}) - {result.question_text[:50]}...")
        
        print("="*80)


async def run_qa_pair_analysis():
    """QA 쌍 분석 실행"""
    from config.config import SUPABASE_URL, SUPABASE_KEY
    
    print("🔍 개별 질문-답변 쌍 분석 시스템 시작...")
    
    analyzer = QAPairAnalyzer(SUPABASE_URL, SUPABASE_KEY)
    
    try:
        # 샘플 ID들로 테스트 (실제로는 사용자가 지정)
        sample_ids = [
            "b958b301-7aba-4521-999c-019e13b24403",
            "68d308a7-69ea-4c95-b71f-0ab6b0de5452", 
            "91db3f8d-2294-4804-8581-dab0079520cc",
            "33d84df6-14c7-47f7-9025-cd4a7e4d0c7a",
            "3484f661-2025-44ca-a766-aeceaa9619f9"
        ]
        
        print(f"📝 {len(sample_ids)}개 질문-답변 쌍 분석 중...")
        
        # 분석 실행
        results = await analyzer.analyze_multiple_pairs(sample_ids)
        
        # 결과 출력
        analyzer.print_qa_analysis_summary(results)
        
        # JSON 저장
        analyzer.export_qa_analysis(results)
        
        print(f"\n✅ 개별 QA 쌍 분석 완료!")
        
        return results
        
    except Exception as e:
        print(f"❌ QA 쌍 분석 중 오류: {e}")
        import traceback
        traceback.print_exc()
        return None


if __name__ == "__main__":
    asyncio.run(run_qa_pair_analysis())
