"""
여러 테이블 통합 처리 실행 모듈
"""
import asyncio
from typing import Dict, List

from config.config import SUPABASE_KEY, SUPABASE_URL
from src.services.multi_table_inquiry_service import MultiTableInquiryService
from src.services.inquiry_classifier_service import InquiryClassifierService
from src.services.embedding_model_service import EmbeddingModelService
from src.utils.async_utils import async_timed, run_async_in_thread


@async_timed()
async def process_multi_table_inquiries() -> None:
    """
    여러 테이블의 문의를 통합 처리하는 메인 함수
    """
    print("여러 테이블에서 문의 데이터 통합 처리 시작...")
    
    # None 체크
    if not SUPABASE_URL or not SUPABASE_KEY:
        print("오류: Supabase URL 또는 API 키가 설정되지 않았습니다.")
        return
    
    try:
        # 통합 문의 서비스 초기화
        multi_service = MultiTableInquiryService(SUPABASE_URL, SUPABASE_KEY)
        
        # 1. 전체 통계 조회
        print("\n[1단계] 전체 문의 통계 조회 중...")
        stats = await multi_service.get_inquiry_statistics()
        print_statistics(stats)
        
        # 2. 통합 문의 목록 조회
        print("\n[2단계] 통합 문의 목록 조회 중...")
        unified_inquiries = await multi_service.get_unified_inquiry_list(
            limit_per_table=30,
            only_unanswered=False
        )
        
        # 3. 통합 목록 출력
        multi_service.print_unified_summary(unified_inquiries)
        
        # 4. 미답변 문의만 별도 조회
        print("\n[3단계] 미답변 문의 조회 중...")
        unanswered_inquiries = await multi_service.get_unified_inquiry_list(
            limit_per_table=20,
            only_unanswered=True
        )
        
        print(f"\n미답변 문의: {len(unanswered_inquiries)}건")
        if unanswered_inquiries:
            print("최근 미답변 문의 5건:")
            for i, inquiry in enumerate(unanswered_inquiries[:5]):
                print(f"{i+1}. [{inquiry.get('inquiry_type')}] {inquiry.get('subject', inquiry.get('content', ''))[:50]}...")
        
        # 5. 문의 객체 변환 테스트
        print("\n[4단계] 문의 객체 변환 테스트...")
        inquiry_objects = await multi_service.convert_to_inquiry_objects(
            unified_inquiries[:10]
        )
        print(f"성공적으로 변환된 문의 객체: {len(inquiry_objects)}개")
        
        # 6. 분류 테스트 (임베딩 모델 사용)
        if inquiry_objects:
            print("\n[5단계] 문의 분류 테스트...")
            await test_classification(inquiry_objects[:3])
        
        print("\n✅ 여러 테이블 통합 처리 완료!")
        
    except Exception as e:
        print(f"❌ 통합 처리 중 오류 발생: {e}")
        import traceback
        traceback.print_exc()


def print_statistics(stats: Dict) -> None:
    """
    통계 정보 출력
    
    Args:
        stats: 통계 정보 딕셔너리
    """
    print("\n📊 전체 문의 통계")
    print("=" * 60)
    print(f"총 문의: {stats['total_inquiries']}건")
    print(f"답변 완료: {stats['answered_inquiries']}건")
    print(f"미답변: {stats['unanswered_inquiries']}건")
    print(f"답변률: {stats['answer_rate']:.1f}%")
    
    print("\n📋 테이블별 통계")
    print("-" * 60)
    for table, table_stats in stats['by_table'].items():
        print(f"{table}:")
        print(f"  - 총 {table_stats['total']}건 (답변: {table_stats['answered']}, 미답변: {table_stats['unanswered']})")
    
    print("\n📝 타입별 통계")
    print("-" * 60)
    for inquiry_type, type_stats in stats['by_type'].items():
        print(f"{inquiry_type}:")
        print(f"  - 총 {type_stats['total']}건 (답변: {type_stats['answered']}, 미답변: {type_stats['unanswered']})")


@async_timed()
async def test_classification(inquiry_objects: List) -> None:
    """
    문의 분류 테스트
    
    Args:
        inquiry_objects: 테스트할 문의 객체 리스트
    """
    try:
        print("임베딩 모델을 사용한 분류 테스트 중...")
        
        # 임베딩 모델 서비스 초기화
        embedding_service = EmbeddingModelService()
        
        print(f"테스트 대상 문의: {len(inquiry_objects)}개")
        
        for i, inquiry in enumerate(inquiry_objects):
            try:
                # 분류 실행
                category, confidence = embedding_service.classify_text(inquiry.content)
                
                print(f"\n문의 {i+1}:")
                print(f"  내용: {inquiry.content[:100]}...")
                print(f"  분류: {category}")
                print(f"  신뢰도: {confidence:.3f}")
                
            except Exception as e:
                print(f"문의 {i+1} 분류 중 오류: {e}")
                
    except Exception as e:
        print(f"분류 테스트 중 오류: {e}")


@async_timed()
async def test_table_connections() -> None:
    """
    테이블 연결 상태 테스트
    """
    print("테이블 연결 상태 테스트 중...")
    
    if not SUPABASE_URL or not SUPABASE_KEY:
        print("❌ Supabase 설정이 없습니다.")
        return
    
    try:
        multi_service = MultiTableInquiryService(SUPABASE_URL, SUPABASE_KEY)
        
        # 각 테이블별로 연결 테스트
        for table in multi_service.inquiry_tables:
            try:
                table_key = multi_service._get_table_key(table)
                inquiries = await multi_service.async_service.get_all_inquiries(
                    table=table_key,
                    limit=1
                )
                print(f"✅ {table}: 연결 성공 (샘플 데이터 {len(inquiries)}개)")
                
            except Exception as e:
                print(f"❌ {table}: 연결 실패 - {e}")
                
    except Exception as e:
        print(f"❌ 테이블 연결 테스트 실패: {e}")


@run_async_in_thread
async def main() -> None:
    """
    메인 실행 함수
    """
    print("=== 여러 테이블 통합 처리 시스템 ===")
    
    # 1. 테이블 연결 테스트
    await test_table_connections()
    
    print("\n" + "="*50)
    
    # 2. 통합 처리 실행
    await process_multi_table_inquiries()


def run_multi_table_processing():
    """
    외부에서 호출할 수 있는 함수
    """
    return main()


if __name__ == "__main__":
    main()
