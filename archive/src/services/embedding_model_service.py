"""
한국어 임베딩 모델 서비스
"""
import os
import numpy as np
from typing import Dict, List, Tuple
from sentence_transformers import SentenceTransformer
from sklearn.metrics.pairwise import cosine_similarity

from src.models.classification import InquiryCategory


class EmbeddingModelService:
    """한국어 임베딩 모델 서비스"""
    
    def __init__(
        self,
        model_path: str = None,
        model_name: str = "snunlp/KR-SBERT-V40K-klueNLI-augSTS"
    ):
        """
        임베딩 모델 서비스 초기화

        Args:
            model_path: 모델 파일 경로 (없으면 기본 모델 사용)
            model_name: 사용할 모델 이름
                       (기본값: snunlp/KR-SBERT-V40K-klueNLI-augSTS)
        """
        print(f"한국어 임베딩 모델 로딩 중: {model_name}")

        # 모델 로드
        try:
            if model_path and os.path.exists(model_path):
                self.model = SentenceTransformer(model_path)
                print(f"로컬 모델 로드 완료: {model_path}")
            else:
                self.model = SentenceTransformer(model_name)
                print(f"허깅페이스 모델 로드 완료: {model_name}")
        except Exception as e:
            print(f"모델 로드 실패, 백업 모델 사용: {e}")
            # 백업 모델들
            backup_models = [
                "jhgan/ko-sbert-nli",
                "sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2"
            ]
            for backup in backup_models:
                try:
                    self.model = SentenceTransformer(backup)
                    print(f"백업 모델 로드 성공: {backup}")
                    break
                except Exception:
                    continue
            else:
                raise Exception("모든 모델 로드 실패")
        
        # 카테고리 및 예시 문장 설정
        self.categories = {cat.value: [] for cat in InquiryCategory}
        self.category_embeddings = {}
        
        # 카테고리별 예시 문장 추가
        self._add_example_sentences()
        
        # 카테고리 임베딩 계산
        self._compute_category_embeddings()
    
    def _add_example_sentences(self):
        """카테고리별 예시 문장 추가"""
        # 반품 요청 예시
        self.categories["반품 요청"] = [
            "상품을 반품하고 싶어요",
            "물건이 마음에 들지 않아서 반품할게요",
            "반품 절차가 어떻게 되나요?",
            "상품 반품하고 싶은데 어떻게 해야 하나요?",
            "구매한 제품을 반품하고 싶습니다"
        ]
        
        # 교환 요청 예시
        self.categories["교환 요청"] = [
            "상품 교환 원합니다",
            "다른 사이즈로 교환하고 싶어요",
            "불량품이라 교환 원해요",
            "색상이 달라서 교환 원합니다",
            "교환 신청하고 싶어요"
        ]
        
        # 환불 문의 예시
        self.categories["환불 문의"] = [
            "환불 진행 상황이 어떻게 되나요?",
            "환불 계좌를 변경하고 싶어요",
            "환불은 언제 되나요?",
            "카드 취소는 얼마나 걸리나요?",
            "환불 금액이 맞지 않아요"
        ]
        
        # 배송 지연/오류 예시
        self.categories["배송 지연/오류"] = [
            "배송이 언제 오나요?",
            "배송이 너무 늦어요",
            "다른 주소로 배송됐어요",
            "배송 조회가 안 돼요",
            "배송 상태가 변경되지 않아요"
        ]
        
        # 상품 정보 문의 예시
        self.categories["상품 정보 문의"] = [
            "이 상품 재질이 뭔가요?",
            "사이즈가 어떻게 되나요?",
            "색상 차이가 있나요?",
            "세탁 방법이 어떻게 되나요?",
            "상세 스펙이 궁금해요"
        ]
        
        # 주문 변경/취소 예시
        self.categories["주문 변경/취소"] = [
            "주문 취소하고 싶어요",
            "주문 내역을 변경할 수 있나요?",
            "결제 수단을 바꾸고 싶어요",
            "주문 수량을 변경하고 싶어요",
            "주문 취소 후 재주문 가능한가요?"
        ]
        
        # 회원/계정 관련 예시
        self.categories["회원/계정 관련"] = [
            "비밀번호를 변경하고 싶어요",
            "회원 탈퇴는 어떻게 하나요?",
            "계정 정보 수정이 안 돼요",
            "로그인이 안 돼요",
            "회원 등급은 어떻게 올리나요?"
        ]
        
        # 기타/불명확 예시
        self.categories["기타/불명확"] = [
            "안녕하세요",
            "문의 드립니다",
            "확인 부탁드립니다",
            "연락 주세요",
            "감사합니다"
        ]
    
    def _compute_category_embeddings(self):
        """카테고리별 임베딩 계산"""
        for category, examples in self.categories.items():
            # 예시 문장 임베딩 계산
            embeddings = self.model.encode(examples)
            
            # 평균 임베딩 계산
            self.category_embeddings[category] = np.mean(embeddings, axis=0)
    
    def classify_text(self, text: str) -> Tuple[str, float]:
        """
        텍스트 분류
        
        Args:
            text: 분류할 텍스트
            
        Returns:
            (카테고리, 신뢰도) 튜플
        """
        # 텍스트 임베딩 계산
        text_embedding = self.model.encode([text])[0]
        
        # 각 카테고리와 유사도 계산
        similarities = {}
        for category, embedding in self.category_embeddings.items():
            similarity = cosine_similarity(
                text_embedding.reshape(1, -1),
                embedding.reshape(1, -1)
            )[0][0]
            similarities[category] = similarity
        
        # 가장 유사한 카테고리 선택
        best_category = max(similarities, key=similarities.get)
        confidence = similarities[best_category]
        
        return best_category, confidence
    
    def get_embedding(self, text: str) -> np.ndarray:
        """
        텍스트 임베딩 계산
        
        Args:
            text: 임베딩할 텍스트
            
        Returns:
            임베딩 벡터
        """
        return self.model.encode(text)