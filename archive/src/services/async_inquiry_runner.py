"""
Supabase personal_inquiries 테이블에 비동기적으로 접근하는 실행 모듈
"""
import asyncio

from config.config import SUPABASE_KEY, SUPABASE_URL
from src.models.inquiry import Inquiry
from src.services.async_inquiry_service import AsyncInquiryService
from src.utils.async_utils import async_timed, run_async_in_thread


async def print_inquiry_summary(inquiries: list[Inquiry]) -> None:
    """
    문의 목록 요약 비동기 출력
    
    Args:
        inquiries: 출력할 문의 목록
    """
    print(f"\n[개인 문의 목록] - 총 {len(inquiries)}건")
    print("=" * 80)
    
    if not inquiries:
        print("데이터가 없습니다.")
        return
    
    print(f"{'고객명':<10} {'제목':<20} {'작성일':<15} {'답변여부':<10}")
    print("-" * 80)
    
    for inq in inquiries:
        fmt = inq.format_for_display()
        status = "✅" if fmt['is_answered'] else "❌"
        print(
            f"{fmt['customer']:<10} {fmt['subject']:<20} "
            f"{fmt['created_at']:<15} {status:<10}"
        )
    
    print("=" * 80)


@async_timed()
async def get_inquiry_stats(service: AsyncInquiryService) -> dict:
    """
    문의 통계 정보 비동기 조회
    
    Args:
        service: 문의 서비스 인스턴스
        
    Returns:
        문의 통계 정보
    """
    return await service.get_inquiries_stats()


@async_timed()
async def process_sample_inquiries(
    service: AsyncInquiryService
) -> list[Inquiry]:
    """
    샘플 문의 처리
    
    Args:
        service: 문의 서비스 인스턴스
        
    Returns:
        처리된 문의 목록
    """
    # 모든 문의 조회
    inquiries = await service.get_all_inquiries()
    
    # 필터링 예시: 고객명 검색
    if inquiries:
        # 첫 번째 문의의 고객명으로 필터링
        customer = inquiries[0].customer
        filtered = await service.get_inquiries_by_customer(customer)
        print(f"고객 '{customer}'의 문의 {len(filtered)}건 발견")
    
    # 미답변 문의 중 일부에 답변 작성
    tasks = []
    sample_answers = [
        "문의주신 내용 확인하였습니다. 곧 처리해 드리겠습니다.",
        "안녕하세요, 문의내용이 접수되었습니다. 2-3일 내로 처리됩니다.",
        "문의에 감사드립니다. 현재 확인 중이며 빠른 시일 내에 답변드리겠습니다."
    ]
    
    # 테스트를 위해 미답변 문의에 샘플 답변 추가
    for i, inq in enumerate(inquiries):
        if (not inq.is_answered and i < len(sample_answers)):
            # 실수로 여러 번 답변하지 않도록 실제 환경에서는 주석 해제
            # tasks.append(
            #    service.answer_inquiry(inq.id, sample_answers[i])
            # )
            pass
    
    if tasks:
        await asyncio.gather(*tasks)
        print(f"{len(tasks)}개 문의에 답변 추가 완료")
    
    return inquiries


@run_async_in_thread
async def main() -> None:
    """
    메인 비동기 실행 함수
    """
    print("Supabase에서 비동기로 개인 문의 데이터 가져오는 중...")
    
    # None 체크
    if not SUPABASE_URL or not SUPABASE_KEY:
        print("오류: Supabase URL 또는 API 키가 설정되지 않았습니다.")
        print("config/config.py 파일을 확인하세요.")
        return
    
    try:
        # 문의 서비스 초기화
        service = AsyncInquiryService(SUPABASE_URL, SUPABASE_KEY)
        
        # 문의 데이터 조회 및 처리
        inquiries = await process_sample_inquiries(service)
        
        # 문의 출력
        await print_inquiry_summary(inquiries)
        
        # 통계 정보 출력
        stats = await get_inquiry_stats(service)
        
        print("\n[통계 정보 (비동기)]")
        print(f"- 총 문의: {stats['total']}건")
        print(f"- 답변 완료: {stats['answered']}건")
        print(f"- 미답변: {stats['unanswered']}건")
        print(f"- 답변률: {stats['answer_rate']}%")
        
    except Exception as e:
        print(f"개인 문의 조회 중 오류 발생: {e}")
        import traceback
        traceback.print_exc()
        return
    
    return


def run_async_service() -> None:
    """
    외부에서 호출할 수 있는 실행 함수
    """
    main() 