"""
personal_inquiries 테이블 데이터 구조 확인 스크립트
"""
import asyncio
from config.config import SUPABASE_URL, SUPABASE_KEY
from src.services.async_inquiry_service import AsyncInquiryService


async def analyze_personal_inquiries():
    """personal_inquiries 테이블의 실제 데이터 구조 분석"""
    
    print("=== personal_inquiries 테이블 데이터 분석 ===")
    
    # 서비스 초기화
    service = AsyncInquiryService(SUPABASE_URL, SUPABASE_KEY)
    
    try:
        # 최근 데이터 5개 조회
        inquiries = await service.get_all_inquiries(
            table='personal',
            limit=5,
            only_unanswered=False
        )
        
        print(f"\n조회된 데이터 수: {len(inquiries)}개")
        print("=" * 80)
        
        for i, inquiry in enumerate(inquiries, 1):
            print(f"\n📝 문의 {i}:")
            print("-" * 40)
            
            # 모든 필드 출력
            for key, value in inquiry.items():
                if value is not None and str(value).strip():
                    # 긴 텍스트는 일부만 출력
                    if isinstance(value, str) and len(value) > 100:
                        display_value = value[:100] + "..."
                    else:
                        display_value = value
                    print(f"{key}: {display_value}")
            
            print("-" * 40)
            
            # subject, content, answer 필드 특별 분석
            subject = inquiry.get('subject', '')
            content = inquiry.get('content', '')
            answer = inquiry.get('answer', '')
            
            print(f"\n🔍 핵심 필드 분석:")
            print(f"Subject 길이: {len(subject) if subject else 0}")
            print(f"Content 길이: {len(content) if content else 0}")
            print(f"Answer 길이: {len(answer) if answer else 0}")
            
            if content:
                print(f"\n👤 사용자 질문 (content):")
                print(f"'{content[:200]}{'...' if len(content) > 200 else ''}'")
            
            if answer:
                print(f"\n🏪 셀러 답변 (answer):")
                print(f"'{answer[:200]}{'...' if len(answer) > 200 else ''}'")
            
            print("=" * 80)
        
        # 전체 통계
        print(f"\n📊 전체 데이터 통계 분석...")
        all_inquiries = await service.get_all_inquiries(
            table='personal',
            limit=100,
            only_unanswered=False
        )
        
        content_count = sum(1 for inq in all_inquiries if inq.get('content'))
        answer_count = sum(1 for inq in all_inquiries if inq.get('answer'))
        subject_count = sum(1 for inq in all_inquiries if inq.get('subject'))
        
        print(f"총 조회 데이터: {len(all_inquiries)}개")
        print(f"Content 있는 데이터: {content_count}개 ({content_count/len(all_inquiries)*100:.1f}%)")
        print(f"Answer 있는 데이터: {answer_count}개 ({answer_count/len(all_inquiries)*100:.1f}%)")
        print(f"Subject 있는 데이터: {subject_count}개 ({subject_count/len(all_inquiries)*100:.1f}%)")
        
        # 실제 분류 테스트용 데이터 추출
        print(f"\n🤖 AI 분류 테스트용 실제 데이터:")
        print("=" * 80)
        
        test_data = []
        for inquiry in all_inquiries[:10]:
            content = inquiry.get('content', '').strip()
            if content and len(content) > 10:  # 의미있는 내용이 있는 경우만
                test_data.append({
                    'id': inquiry.get('id'),
                    'content': content,
                    'answer': inquiry.get('answer', '').strip(),
                    'subject': inquiry.get('subject', '').strip()
                })
        
        for i, data in enumerate(test_data[:5], 1):
            print(f"\n{i}. ID: {data['id']}")
            print(f"   질문: {data['content'][:100]}...")
            if data['answer']:
                print(f"   답변: {data['answer'][:100]}...")
            print()
        
        return test_data
        
    except Exception as e:
        print(f"오류 발생: {e}")
        import traceback
        traceback.print_exc()
        return []


async def test_ai_classification_with_real_data():
    """실제 데이터로 AI 분류 테스트"""
    
    print("\n🤖 실제 데이터로 한국어 AI 분류 테스트")
    print("=" * 80)
    
    # 실제 데이터 가져오기
    test_data = await analyze_personal_inquiries()
    
    if not test_data:
        print("테스트할 데이터가 없습니다.")
        return
    
    try:
        from src.services.embedding_model_service import EmbeddingModelService
        from src.services.llm_service import LLMService
        from config.config import OPENAI_API_KEY
        
        # 임베딩 모델 테스트
        print("\n1️⃣ 한국어 임베딩 모델로 실제 데이터 분류")
        print("-" * 50)
        
        embedding_service = EmbeddingModelService()
        
        for i, data in enumerate(test_data[:5], 1):
            content = data['content']
            category, confidence = embedding_service.classify_text(content)
            
            print(f"\n{i}. 실제 고객 문의:")
            print(f"   내용: {content[:80]}...")
            print(f"   AI 분류: {category}")
            print(f"   신뢰도: {confidence:.3f}")
            
            if data['answer']:
                print(f"   실제 답변: {data['answer'][:60]}...")
        
        # LLM 테스트 (API 키가 있는 경우)
        if OPENAI_API_KEY:
            print(f"\n2️⃣ LLM으로 실제 데이터 분류 (2개만)")
            print("-" * 50)
            
            llm_service = LLMService()
            
            for i, data in enumerate(test_data[:2], 1):
                try:
                    content = data['content']
                    category, confidence = await llm_service.classify_inquiry(content)
                    
                    print(f"\n{i}. 실제 고객 문의:")
                    print(f"   내용: {content[:80]}...")
                    print(f"   LLM 분류: {category}")
                    print(f"   신뢰도: {confidence:.3f}")
                    
                except Exception as e:
                    print(f"LLM 분류 오류: {e}")
        
        print(f"\n✅ 실제 데이터 AI 분류 테스트 완료!")
        
    except Exception as e:
        print(f"AI 분류 테스트 중 오류: {e}")


def main():
    """메인 실행 함수"""
    asyncio.run(test_ai_classification_with_real_data())


if __name__ == "__main__":
    main()
