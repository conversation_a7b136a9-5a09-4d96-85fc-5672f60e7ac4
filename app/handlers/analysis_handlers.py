"""
분석 관련 핸들러 함수들 - 새로운 기능 통합
"""
import asyncio
from typing import Optional

from app.agents.qa_agent import QAAnalysisAgent
from app.config.manager import ConfigManager
from app.core.domain import AnalysisRequest
from app.services.service_factory import ServiceFactory
from app.services.model_comparison import ModelComparisonService
from app.services.classification_system import UnifiedClassificationSystem
from app.services.hybrid_classification import HybridClassificationPipeline
from app.services.result_storage import ResultStorageService
from app.ui.console import (
    get_user_choice, get_user_input, print_analysis_result,
    print_batch_summary, print_error, print_info, print_warning
)
from app.ui.comparison_dashboard import ComparisonDashboard


def _create_agent() -> QAAnalysisAgent:
    """QA 분석 에이전트 생성"""
    try:
        from config.config import SUPABASE_URL, SUPABASE_KEY
        agent = QAAnalysisAgent(SUPABASE_URL, SUPABASE_KEY)
        print_info("DB 연결 설정 완료")
        return agent
    except ImportError:
        print_warning("DB 설정을 찾을 수 없습니다. 텍스트 모드로 진행합니다.")
        return QAAnalysisAgent()
    except Exception as e:
        print_warning(f"DB 설정 오류: {e}. 텍스트 모드로 진행합니다.")
        return QAAnalysisAgent()


async def handle_single_analysis() -> None:
    """개별 QA 분석 처리"""
    print("\n📊 개별 QA 분석")
    print("=" * 40)

    # 입력 방식 선택
    print("입력 방식을 선택하세요:")
    print("1. 문의 ID로 분석")
    print("2. 직접 텍스트 입력")

    choice = get_user_choice("선택 (1-2): ", range(1, 3))
    agent = _create_agent()

    if choice == 1:
        inquiry_id = get_user_input("문의 ID를 입력하세요: ")
        request = AnalysisRequest(inquiry_id=inquiry_id)
    else:
        question = get_user_input("질문을 입력하세요: ")
        answer = get_user_input("답변을 입력하세요: ", required=False)
        
        request = AnalysisRequest(
            question_text=question,
            answer_text=answer
        )

    print("\n🔍 분석 중...")
    try:
        result = await agent.analyze_single_qa(request)
        print_analysis_result(result)
    except Exception as e:
        print_error(f"분석 중 오류가 발생했습니다: {e}")


async def handle_batch_analysis() -> None:
    """배치 분석 처리"""
    print("\n📈 배치 QA 분석")
    print("=" * 40)

    # 분석할 문의 수 입력
    limit_input = get_user_input(
        "분석할 문의 수를 입력하세요 (기본: 5, 최대: 10000): ", 
        required=False
    ) or "5"
    
    try:
        limit = int(limit_input)
        limit = _validate_batch_limit(limit)
    except ValueError:
        print_warning("올바른 숫자를 입력해주세요. 5건으로 설정합니다.")
        limit = 5

    agent = _create_agent()
    print(f"\n📊 {limit}건의 문의 배치 분석 시작...")

    # 대용량 처리 안내
    if limit > 100:
        print("🔄 대용량 처리를 위해 100건씩 분할 처리합니다.")

    try:
        # 샘플 데이터 분석
        batch_result = await agent.analyze_sample_data(limit)
        
        # 결과 요약 출력
        print_batch_summary(batch_result)
        
        # 결과 저장
        agent.export_results(batch_result)
        print_info("결과가 저장되었습니다.")
        
    except Exception as e:
        print_error(f"배치 분석 중 오류가 발생했습니다: {e}")


def _validate_batch_limit(limit: int) -> int:
    """배치 분석 제한 수 검증"""
    if limit > 10000:
        print_warning("최대 10,000건까지 처리 가능합니다. 10,000건으로 설정합니다.")
        return 10000
    elif limit < 1:
        print_warning("최소 1건 이상 입력해주세요. 5건으로 설정합니다.")
        return 5
    return limit


async def handle_model_test() -> None:
    """모델 테스트 처리"""
    print("\n🧪 모델 테스트")
    print("=" * 40)

    question = get_user_input("테스트 질문을 입력하세요: ")
    answer = get_user_input("테스트 답변을 입력하세요: ", required=False)

    agent = QAAnalysisAgent()
    request = AnalysisRequest(
        question_text=question,
        answer_text=answer
    )

    print("\n🔍 테스트 중...")
    try:
        result = await agent.analyze_single_qa(request)
        print_analysis_result(result)
    except Exception as e:
        print_error(f"모델 테스트 중 오류가 발생했습니다: {e}")


def handle_profile_management() -> None:
    """프로필 관리 처리"""
    config_manager = ConfigManager()

    while True:
        print("\n🔧 프로필 관리")
        print("=" * 40)
        print("1. 현재 프로필 보기")
        print("2. 프로필 변경")
        print("3. 모든 프로필 목록")
        print("4. 설정 검증")
        print("0. 돌아가기")

        choice = get_user_choice("\n선택 (0-4): ", range(0, 5))

        if choice == 1:
            config_manager.print_current_config()
        elif choice == 2:
            _handle_profile_change(config_manager)
        elif choice == 3:
            config_manager.list_all_profiles()
        elif choice == 4:
            _handle_config_validation(config_manager)
        elif choice == 0:
            break


def _handle_profile_change(config_manager: ConfigManager) -> None:
    """프로필 변경 처리"""
    profiles = config_manager.get_available_profiles()
    current = config_manager.get_current_profile()

    print("\n📋 사용 가능한 프로필:")
    for i, profile in enumerate(profiles, 1):
        marker = "✅" if profile == current else "  "
        info = config_manager.get_profile_info(profile)
        print(f"{marker} {i}. {profile}")
        print(f"     {info.get('description', '')}")

    choice = get_user_choice(
        f"\n변경할 프로필 번호 (1-{len(profiles)}): ", 
        range(1, len(profiles) + 1)
    )
    
    new_profile = profiles[choice - 1]
    if config_manager.set_profile(new_profile):
        print_info(f"프로필이 '{new_profile}'로 변경되었습니다.")


def _handle_config_validation(config_manager: ConfigManager) -> None:
    """설정 검증 처리"""
    if config_manager.validate_current_config():
        print_info("설정이 올바릅니다.")
    else:
        print_error("설정에 문제가 있습니다.")


async def handle_model_comparison() -> None:
    """모델 비교 처리"""
    print("\n🔍 모델 성능 비교")
    print("=" * 40)

    # 서비스 팩토리 초기화
    try:
        from config.config import SUPABASE_URL, SUPABASE_KEY
        service_factory = ServiceFactory(SUPABASE_URL, SUPABASE_KEY)
    except ImportError:
        print_warning("DB 설정을 찾을 수 없습니다. 기본 설정으로 진행합니다.")
        service_factory = ServiceFactory()

    # 비교 서비스 초기화
    comparison_service = ModelComparisonService(service_factory)
    dashboard = ComparisonDashboard()

    # 테스트 데이터 준비
    test_requests = [
        AnalysisRequest(question_text="배송 언제 오나요?", answer_text="3일 내 배송 예정입니다."),
        AnalysisRequest(question_text="환불 가능한가요?", answer_text="7일 내 환불 가능합니다."),
        AnalysisRequest(question_text="상품 사이즈가 어떻게 되나요?", answer_text="M, L, XL 사이즈 있습니다."),
    ]

    print("🔍 모델 비교 실행 중...")
    comparison_result = await comparison_service.compare_all_models(test_requests)

    # 결과 출력
    dashboard.display_comparison_summary(comparison_result)
    dashboard.display_detailed_metrics(comparison_result)
    dashboard.display_performance_chart(comparison_result)

    # HTML 대시보드 생성 여부 확인
    create_html = get_user_choice("HTML 대시보드를 생성하시겠습니까? (1: 예, 0: 아니오): ", range(0, 2))
    if create_html == 1:
        dashboard.generate_html_dashboard(comparison_result)
        print_info("HTML 대시보드가 outputs/dashboard.html에 생성되었습니다.")


async def handle_classification_system() -> None:
    """통합 분류 시스템 처리"""
    print("\n🏷️ 통합 분류 시스템")
    print("=" * 40)

    # 서비스 팩토리 초기화
    try:
        from config.config import SUPABASE_URL, SUPABASE_KEY
        service_factory = ServiceFactory(SUPABASE_URL, SUPABASE_KEY)
    except ImportError:
        print_warning("DB 설정을 찾을 수 없습니다. 기본 설정으로 진행합니다.")
        service_factory = ServiceFactory()

    # 분류 시스템 초기화
    embedding_model = service_factory.get_embedding_model()
    classification_system = UnifiedClassificationSystem(embedding_model)

    print("분류 방식을 선택하세요:")
    print("1. 단일 문의 분류")
    print("2. 배치 분류 (DB에서)")

    choice = get_user_choice("선택 (1-2): ", range(1, 3))

    if choice == 1:
        # 단일 문의 분류
        question = get_user_input("분류할 문의를 입력하세요: ")
        answer = get_user_input("답변을 입력하세요 (선택사항): ", required=False)

        print("\n🔍 분류 중...")
        result = await classification_system.classify_inquiry(question, answer)

        # 결과 출력
        print("\n" + "=" * 60)
        print("🏷️ 분류 결과")
        print("=" * 60)
        print(f"📂 카테고리: {result['classification']['category']} (신뢰도: {result['classification']['confidence']:.3f})")
        print(f"😊 감정: {result['sentiment_analysis']['sentiment']} (점수: {result['sentiment_analysis']['score']:.3f})")
        print(f"⚡ 긴급도: {result['urgency_analysis']['urgency']} (점수: {result['urgency_analysis']['score']:.3f})")
        print(f"🎯 우선순위: {result['priority_level']} (점수: {result['priority_score']:.3f})")
        print(f"🏢 추천 부서: {result['routing_recommendation']['department']}")

        if result['action_items']:
            print("\n📋 액션 아이템:")
            for item in result['action_items']:
                print(f"  • {item}")

    elif choice == 2:
        # 배치 분류
        limit = int(get_user_input("분류할 문의 수를 입력하세요 (기본: 10): ", required=False) or "10")

        # DB에서 데이터 가져오기
        database_service = service_factory.get_database_service()
        inquiries_data = await database_service.get_sample_inquiries(limit)

        if not inquiries_data:
            print_error("분류할 데이터가 없습니다.")
            return

        print(f"\n🔍 {len(inquiries_data)}건 배치 분류 중...")
        results = await classification_system.classify_batch(inquiries_data)

        # 요약 생성
        summary = classification_system.generate_classification_summary(results)

        # 결과 출력
        print("\n" + "=" * 60)
        print("📊 배치 분류 요약")
        print("=" * 60)
        print(f"📋 총 문의: {summary['total_inquiries']}건")

        print("\n📂 카테고리 분포:")
        for category, count in summary['category_distribution'].items():
            percentage = (count / summary['total_inquiries']) * 100
            print(f"  • {category}: {count}건 ({percentage:.1f}%)")

        print("\n😊 감정 분포:")
        for sentiment, count in summary['sentiment_distribution'].items():
            percentage = (count / summary['total_inquiries']) * 100
            print(f"  • {sentiment}: {count}건 ({percentage:.1f}%)")

        print("\n⚡ 긴급도 분포:")
        for urgency, count in summary['urgency_distribution'].items():
            percentage = (count / summary['total_inquiries']) * 100
            print(f"  • {urgency}: {count}건 ({percentage:.1f}%)")

        if summary['insights']:
            print("\n💡 인사이트:")
            for insight in summary['insights']:
                print(f"  • {insight}")


def handle_model_management() -> None:
    """모델 관리 처리"""
    print("\n🤖 모델 관리")
    print("=" * 40)

    # 서비스 팩토리 초기화
    try:
        from config.config import SUPABASE_URL, SUPABASE_KEY
        service_factory = ServiceFactory(SUPABASE_URL, SUPABASE_KEY)
    except ImportError:
        print_warning("DB 설정을 찾을 수 없습니다. 기본 설정으로 진행합니다.")
        service_factory = ServiceFactory()

    while True:
        print("\n모델 관리 메뉴:")
        print("1. 현재 모델 확인")
        print("2. 사용 가능한 모델 목록")
        print("3. 모델 교체")
        print("4. 모델 정보 조회")
        print("0. 돌아가기")

        choice = get_user_choice("선택 (0-4): ", range(0, 5))

        if choice == 1:
            # 현재 모델 확인
            current_models = service_factory.get_current_models()
            print("\n📋 현재 선택된 모델:")
            for model_type, model_name in current_models.items():
                print(f"  • {model_type}: {model_name or 'None'}")

        elif choice == 2:
            # 사용 가능한 모델 목록
            available_models = service_factory.get_available_models()
            print("\n📋 사용 가능한 모델:")
            for model_type, models in available_models.items():
                print(f"\n{model_type.upper()} 모델:")
                for model in models:
                    print(f"  • {model}")

        elif choice == 3:
            # 모델 교체
            print("\n교체할 모델 타입을 선택하세요:")
            print("1. 임베딩 모델")
            print("2. LLM 모델")
            print("3. 분류 모델")

            model_type_choice = get_user_choice("선택 (1-3): ", range(1, 4))
            model_types = ["embedding", "llm", "classification"]
            selected_type = model_types[model_type_choice - 1]

            available_models = service_factory.get_available_models()
            models = available_models.get(selected_type, [])

            if not models:
                print_warning(f"{selected_type} 모델이 없습니다.")
                continue

            print(f"\n{selected_type.upper()} 모델 목록:")
            for i, model in enumerate(models, 1):
                print(f"  {i}. {model}")

            model_choice = get_user_choice(f"선택 (1-{len(models)}): ", range(1, len(models) + 1))
            selected_model = models[model_choice - 1]

            # 모델 교체 실행
            if selected_type == "embedding":
                success = service_factory.switch_embedding_model(selected_model)
            elif selected_type == "llm":
                success = service_factory.switch_llm_model(selected_model)
            else:  # classification
                success = service_factory.switch_classification_model(selected_model)

            if success:
                print_info(f"{selected_type} 모델이 {selected_model}로 교체되었습니다.")
            else:
                print_error("모델 교체에 실패했습니다.")

        elif choice == 4:
            # 모델 정보 조회
            model_name = get_user_input("정보를 조회할 모델명을 입력하세요: ")
            model_info = service_factory.get_model_info(model_name)

            if model_info:
                print(f"\n📋 {model_name} 모델 정보:")
                for key, value in model_info.items():
                    print(f"  • {key}: {value}")
            else:
                print_error(f"모델 '{model_name}'을 찾을 수 없습니다.")

        elif choice == 0:
            break


async def handle_hybrid_classification() -> None:
    """하이브리드 분류 파이프라인 처리"""
    print("\n🔄 하이브리드 분류 파이프라인")
    print("=" * 40)

    # 서비스 팩토리 초기화
    try:
        from config.config import SUPABASE_URL, SUPABASE_KEY
        service_factory = ServiceFactory(SUPABASE_URL, SUPABASE_KEY)
    except ImportError:
        print_warning("DB 설정을 찾을 수 없습니다. 기본 설정으로 진행합니다.")
        service_factory = ServiceFactory()

    # 하이브리드 파이프라인 초기화
    pipeline = HybridClassificationPipeline(service_factory)

    print("처리 방식을 선택하세요:")
    print("1. 단일 텍스트 분류")
    print("2. 배치 분류")
    print("3. 파이프라인 설정")
    print("4. 성능 통계 확인")
    print("5. 시나리오별 설정 적용")

    choice = get_user_choice("선택 (1-5): ", range(1, 6))

    if choice == 1:
        # 단일 텍스트 분류
        text = get_user_input("분류할 텍스트를 입력하세요: ")
        use_hybrid = get_user_choice("하이브리드 모드 사용? (1: 예, 0: 아니오): ", range(0, 2)) == 1

        print("\n🔍 분류 중...")
        result = await pipeline.classify_single(text, use_hybrid)

        print("\n" + "=" * 50)
        print("🏷️ 분류 결과")
        print("=" * 50)
        print(f"📂 카테고리: {result['category']}")
        print(f"🎯 신뢰도: {result['confidence']:.3f}")
        print(f"⚙️ 처리 단계: {result.get('pipeline_stage', 'direct')}")
        print(f"⏱️ 처리 시간: {result.get('processing_time_ms', 0):.2f}ms")

        if 'stage1_result' in result:
            print(f"\n1차 결과: {result['stage1_result']['category']} ({result['stage1_result']['confidence']:.3f})")

    elif choice == 2:
        # 배치 분류
        print("\n배치 분류 데이터 소스:")
        print("1. 직접 입력")
        print("2. DB에서 가져오기")

        source_choice = get_user_choice("선택 (1-2): ", range(1, 3))

        if source_choice == 1:
            # 직접 입력
            texts = []
            print("텍스트를 입력하세요 (빈 줄 입력 시 종료):")
            while True:
                text = input(f"텍스트 {len(texts)+1}: ").strip()
                if not text:
                    break
                texts.append(text)
        else:
            # DB에서 가져오기
            limit = int(get_user_input("가져올 문의 수 (기본: 20): ", required=False) or "20")
            database_service = service_factory.get_database_service()
            inquiries = await database_service.get_sample_inquiries(limit)
            texts = [item.get('question', '') for item in inquiries if item.get('question')]

        if not texts:
            print_error("분류할 텍스트가 없습니다.")
            return

        use_hybrid = get_user_choice("하이브리드 모드 사용? (1: 예, 0: 아니오): ", range(0, 2)) == 1

        print(f"\n🔍 {len(texts)}건 배치 분류 중...")

        def progress_callback(current, total):
            print(f"  진행률: {current}/{total} ({current/total*100:.1f}%)")

        results = await pipeline.classify_batch(texts, use_hybrid, progress_callback)

        # 결과 요약
        categories = {}
        for result in results:
            category = result['category']
            categories[category] = categories.get(category, 0) + 1

        print("\n" + "=" * 50)
        print("📊 배치 분류 결과 요약")
        print("=" * 50)
        print(f"📋 총 처리: {len(results)}건")

        print("\n📂 카테고리 분포:")
        for category, count in categories.items():
            percentage = (count / len(results)) * 100
            print(f"  • {category}: {count}건 ({percentage:.1f}%)")

        # 파이프라인 통계
        stats = pipeline.get_pipeline_stats()
        if use_hybrid:
            print(f"\n⚡ 파이프라인 효율성: {stats['efficiency_percent']:.1f}%")
            print(f"📊 1차 필터링: {stats['stage1_filtered']}건")
            print(f"🎯 2차 처리: {stats['stage2_processed']}건")

    elif choice == 3:
        # 파이프라인 설정
        current_config = pipeline.get_pipeline_stats()['config']
        print("\n⚙️ 현재 설정:")
        for key, value in current_config.items():
            print(f"  • {key}: {value}")

        print("\n변경할 설정을 선택하세요:")
        print("1. 1차 임계값")
        print("2. 1차 모델")
        print("3. 2차 모델")
        print("4. 배치 크기")

        setting_choice = get_user_choice("선택 (1-4): ", range(1, 5))

        if setting_choice == 1:
            new_threshold = float(get_user_input(f"새 임계값 (현재: {current_config['stage1_threshold']}): "))
            pipeline.configure_pipeline({"stage1_threshold": new_threshold})
        elif setting_choice == 2:
            available_models = service_factory.get_available_models()["embedding"]
            print("사용 가능한 모델:")
            for i, model in enumerate(available_models, 1):
                print(f"  {i}. {model}")
            model_choice = get_user_choice(f"선택 (1-{len(available_models)}): ", range(1, len(available_models)+1))
            new_model = available_models[model_choice-1]
            pipeline.configure_pipeline({"stage1_model": new_model})
        # 추가 설정들...

    elif choice == 4:
        # 성능 통계
        stats = pipeline.get_pipeline_stats()
        print("\n📊 파이프라인 성능 통계")
        print("=" * 40)
        print(f"📋 총 처리: {stats['total_processed']}건")
        print(f"⚡ 1차 필터링: {stats['stage1_filtered']}건")
        print(f"🎯 2차 처리: {stats['stage2_processed']}건")
        print(f"📈 효율성: {stats['efficiency_percent']:.1f}%")
        print(f"💾 캐시 히트: {stats['cache_hits']}회")

        print("\n⏱️ 처리 시간:")
        for stage, time_ms in stats['processing_times'].items():
            print(f"  • {stage}: {time_ms:.2f}ms")

    elif choice == 5:
        # 시나리오별 설정
        scenarios = ["real_time", "batch_processing", "high_accuracy", "cost_efficient"]
        print("\n🎯 사용 시나리오:")
        for i, scenario in enumerate(scenarios, 1):
            print(f"  {i}. {scenario}")

        scenario_choice = get_user_choice(f"선택 (1-{len(scenarios)}): ", range(1, len(scenarios)+1))
        selected_scenario = scenarios[scenario_choice-1]

        pipeline.apply_scenario_config(selected_scenario)


async def handle_result_management() -> None:
    """결과 관리 처리"""
    print("\n📁 결과 관리")
    print("=" * 40)

    storage_service = ResultStorageService()

    while True:
        print("\n결과 관리 메뉴:")
        print("1. 저장된 결과 목록")
        print("2. 결과 검색")
        print("3. 결과 비교")
        print("4. 성능 트렌드")
        print("5. 결과 삭제")
        print("6. 저장소 정보")
        print("0. 돌아가기")

        choice = get_user_choice("선택 (0-6): ", range(0, 7))

        if choice == 1:
            # 저장된 결과 목록
            results = storage_service.list_results(limit=20)
            print(f"\n📋 최근 결과 {len(results)}건:")
            for result in results:
                print(f"  • {result['result_id']}: {result.get('description', 'No description')}")
                print(f"    저장일: {result.get('saved_at', 'Unknown')}")
                print(f"    태그: {', '.join(result.get('tags', []))}")
                print()

        elif choice == 2:
            # 결과 검색
            query = get_user_input("검색어를 입력하세요: ")
            results = storage_service.search_results(query)
            print(f"\n🔍 '{query}' 검색 결과 {len(results)}건:")
            for result in results:
                print(f"  • {result['result_id']}: {result.get('description', 'No description')}")

        elif choice == 3:
            # 결과 비교
            result_ids = get_user_input("비교할 결과 ID들을 쉼표로 구분하여 입력하세요: ").split(',')
            result_ids = [rid.strip() for rid in result_ids if rid.strip()]

            if len(result_ids) < 2:
                print_error("비교하려면 최소 2개의 결과 ID가 필요합니다.")
                continue

            comparison = storage_service.compare_results(result_ids)
            if "error" in comparison:
                print_error(comparison["error"])
            else:
                print("\n📊 결과 비교 분석:")
                for rec in comparison.get("recommendations", []):
                    print(f"  • {rec}")

        elif choice == 4:
            # 성능 트렌드
            model_name = get_user_input("모델명 (전체 분석은 빈 값): ", required=False)
            days = int(get_user_input("분석 기간 (일, 기본: 30): ", required=False) or "30")

            trends = storage_service.get_performance_trends(model_name, days)
            if "error" in trends:
                print_error(trends["error"])
            else:
                print(f"\n📈 최근 {days}일 성능 트렌드:")
                for model, trend_data in trends.items():
                    direction = trend_data.get("trend_direction", "unknown")
                    print(f"  • {model}: {direction}")

        elif choice == 5:
            # 결과 삭제
            result_id = get_user_input("삭제할 결과 ID: ")
            if storage_service.delete_result(result_id):
                print_info(f"결과 {result_id} 삭제 완료")
            else:
                print_error("삭제 실패")

        elif choice == 6:
            # 저장소 정보
            info = storage_service.get_storage_info()
            print("\n📁 저장소 정보:")
            for key, value in info.items():
                if key != "available_operations":
                    print(f"  • {key}: {value}")

        elif choice == 0:
            break


def handle_system_info() -> None:
    """시스템 정보 처리"""
    from app.ui.console import print_system_info
    config_manager = ConfigManager()
    print_system_info(config_manager)
