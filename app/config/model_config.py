"""
모델 설정 관리자 - 설정 파일 기반 모델 교체
"""
import json
import yaml
from pathlib import Path
from typing import Dict, Any, List, Optional


class ModelConfigManager:
    """
    모델 설정 관리자
    
    기능:
    - 설정 파일에서 모델 구성 로드
    - 런타임 모델 설정 변경
    - 설정 검증 및 저장
    - 프리셋 관리
    """
    
    def __init__(self, config_path: str = "config/models.yaml"):
        self.config_path = Path(config_path)
        self.config_data = {}
        self.presets = {}
        
        # 설정 파일 로드
        self.load_config()
        
        print("✅ 모델 설정 관리자 초기화 완료")
    
    def load_config(self) -> bool:
        """설정 파일 로드"""
        try:
            if not self.config_path.exists():
                print(f"⚠️ 설정 파일이 없습니다. 기본 설정을 생성합니다: {self.config_path}")
                self._create_default_config()
                return True
            
            with open(self.config_path, 'r', encoding='utf-8') as f:
                if self.config_path.suffix.lower() == '.yaml':
                    self.config_data = yaml.safe_load(f)
                else:
                    self.config_data = json.load(f)
            
            # 프리셋 로드
            self.presets = self.config_data.get("presets", {})
            
            print(f"✅ 설정 파일 로드 완료: {self.config_path}")
            return True
            
        except Exception as e:
            print(f"❌ 설정 파일 로드 실패: {e}")
            self._create_default_config()
            return False
    
    def save_config(self) -> bool:
        """설정 파일 저장"""
        try:
            # 디렉토리 생성
            self.config_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(self.config_path, 'w', encoding='utf-8') as f:
                if self.config_path.suffix.lower() == '.yaml':
                    yaml.dump(self.config_data, f, default_flow_style=False, allow_unicode=True)
                else:
                    json.dump(self.config_data, f, ensure_ascii=False, indent=2)
            
            print(f"✅ 설정 파일 저장 완료: {self.config_path}")
            return True
            
        except Exception as e:
            print(f"❌ 설정 파일 저장 실패: {e}")
            return False
    
    def get_current_models(self) -> Dict[str, str]:
        """현재 선택된 모델들 반환"""
        return self.config_data.get("current_models", {
            "embedding": "kr_sbert_default",
            "llm": None,
            "classification": None
        })
    
    def set_current_model(self, model_type: str, model_name: str) -> bool:
        """현재 모델 설정"""
        if model_type not in ["embedding", "llm", "classification"]:
            print(f"❌ 지원하지 않는 모델 타입: {model_type}")
            return False
        
        if "current_models" not in self.config_data:
            self.config_data["current_models"] = {}
        
        old_model = self.config_data["current_models"].get(model_type)
        self.config_data["current_models"][model_type] = model_name
        
        print(f"✅ {model_type} 모델 설정 변경: {old_model} → {model_name}")
        return True
    
    def get_model_configs(self) -> Dict[str, Dict[str, Any]]:
        """모델 설정들 반환"""
        return self.config_data.get("model_configs", {})
    
    def add_model_config(
        self, 
        model_name: str, 
        model_type: str, 
        config: Dict[str, Any]
    ) -> bool:
        """모델 설정 추가"""
        if "model_configs" not in self.config_data:
            self.config_data["model_configs"] = {}
        
        self.config_data["model_configs"][model_name] = {
            "type": model_type,
            "config": config,
            "added_at": self._get_timestamp()
        }
        
        print(f"✅ 모델 설정 추가: {model_name}")
        return True
    
    def remove_model_config(self, model_name: str) -> bool:
        """모델 설정 제거"""
        if "model_configs" not in self.config_data:
            return False
        
        if model_name in self.config_data["model_configs"]:
            del self.config_data["model_configs"][model_name]
            print(f"✅ 모델 설정 제거: {model_name}")
            return True
        
        print(f"❌ 모델 설정을 찾을 수 없습니다: {model_name}")
        return False
    
    def get_presets(self) -> Dict[str, Dict[str, Any]]:
        """프리셋 목록 반환"""
        return self.presets
    
    def apply_preset(self, preset_name: str) -> bool:
        """프리셋 적용"""
        if preset_name not in self.presets:
            print(f"❌ 프리셋을 찾을 수 없습니다: {preset_name}")
            return False
        
        preset = self.presets[preset_name]
        models = preset.get("models", {})
        
        # 현재 모델들 업데이트
        for model_type, model_name in models.items():
            self.set_current_model(model_type, model_name)
        
        print(f"✅ 프리셋 적용 완료: {preset_name}")
        print(f"   설명: {preset.get('description', '')}")
        return True
    
    def create_preset(
        self, 
        preset_name: str, 
        description: str, 
        models: Dict[str, str]
    ) -> bool:
        """새 프리셋 생성"""
        preset = {
            "description": description,
            "models": models,
            "created_at": self._get_timestamp()
        }
        
        self.presets[preset_name] = preset
        self.config_data["presets"] = self.presets
        
        print(f"✅ 프리셋 생성: {preset_name}")
        return True
    
    def delete_preset(self, preset_name: str) -> bool:
        """프리셋 삭제"""
        if preset_name in self.presets:
            del self.presets[preset_name]
            self.config_data["presets"] = self.presets
            print(f"✅ 프리셋 삭제: {preset_name}")
            return True
        
        print(f"❌ 프리셋을 찾을 수 없습니다: {preset_name}")
        return False
    
    def validate_config(self) -> Dict[str, List[str]]:
        """설정 검증"""
        issues = {
            "errors": [],
            "warnings": []
        }
        
        # 현재 모델 검증
        current_models = self.get_current_models()
        model_configs = self.get_model_configs()
        
        for model_type, model_name in current_models.items():
            if model_name and model_name not in model_configs:
                issues["errors"].append(f"현재 {model_type} 모델({model_name})의 설정을 찾을 수 없습니다")
        
        # 프리셋 검증
        for preset_name, preset in self.presets.items():
            preset_models = preset.get("models", {})
            for model_type, model_name in preset_models.items():
                if model_name not in model_configs:
                    issues["warnings"].append(f"프리셋 {preset_name}의 {model_type} 모델({model_name}) 설정이 없습니다")
        
        return issues
    
    def _create_default_config(self):
        """기본 설정 생성 - 업데이트된 모델"""
        self.config_data = {
            "current_models": {
                "unified_model": "xlm_roberta_base",  # 임베딩과 분류 통합
                "llm": "gpt_4_1_mini"
            },
            "model_configs": {
                "xlm_roberta_base": {
                    "type": "unified",  # 임베딩과 분류 통합
                    "config": {
                        "model_name": "xlm-roberta-base"
                    },
                    "description": "🥇 최고 정확도, 다국어 지원"
                },
                "beomi_kcelectra": {
                    "type": "unified",
                    "config": {
                        "model_name": "beomi/KcELECTRA-base"
                    },
                    "description": "🥈 한국어 특화, 균형 성능"
                },
                "koelectra_v3": {
                    "type": "unified",
                    "config": {
                        "model_name": "monologg/koelectra-base-v3-discriminator"
                    },
                    "description": "🥉 고속 처리, 경량화"
                },
                "kr_sbert": {
                    "type": "unified",
                    "config": {
                        "model_name": "snunlp/KR-SBERT-V40K-klueNLI-augSTS"
                    },
                    "description": "🏃 한국어 특화, 빠른 속도"
                },
                "gpt_4_1_mini": {
                    "type": "llm",
                    "config": {
                        "model_name": "gpt-4.1-mini",
                        "input_cost_per_1m": 0.40,
                        "output_cost_per_1m": 1.60,
                        "cached_input_cost_per_1m": 0.10
                    },
                    "description": "🎯 최적 성능/비용, 높은 정확도"
                }
            },
            "presets": {
                "korean_ecommerce_optimized": {
                    "description": "한국어 이커머스 최적화 (현재 기본)",
                    "models": {
                        "unified_model": "xlm_roberta_base",
                        "llm": "gpt_4_1_mini"
                    }
                },
                "korean_specialized": {
                    "description": "한국어 특화 고속 처리",
                    "models": {
                        "unified_model": "kr_sbert",
                        "llm": "gpt_4_1_mini"
                    }
                },
                "balanced_performance": {
                    "description": "균형잡힌 성능",
                    "models": {
                        "unified_model": "beomi_kcelectra",
                        "llm": "gpt_4_1_mini"
                    }
                },
                "high_speed": {
                    "description": "고속 처리 우선",
                    "models": {
                        "unified_model": "koelectra_v3",
                        "llm": "gpt_4_1_mini"
                    }
                }
            }
        }
        
        self.presets = self.config_data["presets"]
        self.save_config()
    
    def _get_timestamp(self) -> str:
        """현재 타임스탬프"""
        from datetime import datetime
        return datetime.now().isoformat()
    
    def get_config_summary(self) -> Dict[str, Any]:
        """설정 요약 정보"""
        return {
            "config_file": str(self.config_path),
            "current_models": self.get_current_models(),
            "total_model_configs": len(self.get_model_configs()),
            "total_presets": len(self.presets),
            "validation": self.validate_config()
        }
