"""
설정 관리자 - 간소화된 버전
"""
from typing import Dict, List, Any


class ConfigManager:
    """설정 관리자"""
    
    def __init__(self):
        """설정 초기화"""
        self.current_profile = "korean_ecommerce_optimized"
        
        # 프로필 정의 - 업데이트된 모델 조합
        self.profiles = {
            "korean_ecommerce_optimized": {
                "name": "한국어 이커머스 최적화",
                "description": "한국어 쇼핑몰 문의 분석에 최적화된 모델 조합",
                "unified_model": "snunlp/KR-SBERT-V40K-klueNLI-augSTS",  # 임베딩과 분류 통합
                "llm_model": "gpt-4.1-mini"
            },
            "high_performance": {
                "name": "고성능 분석",
                "description": "최고 정확도를 위한 대형 모델 조합",
                "unified_model": "xlm-roberta-large",  # 최고 성능
                "llm_model": "gpt-4.1-mini"
            },
            "lightweight_fast": {
                "name": "경량 고속",
                "description": "빠른 처리를 위한 경량 모델 조합",
                "unified_model": "monologg/koelectra-base-v3-discriminator",  # 고속 처리
                "llm_model": "gpt-4.1-mini"
            },
            "balanced_performance": {
                "name": "균형 성능",
                "description": "정확도와 속도의 균형을 맞춘 모델 조합",
                "unified_model": "beomi/KcELECTRA-base",  # 균형 성능
                "llm_model": "gpt-4.1-mini"
            },
            "klue_optimized": {
                "name": "KLUE 최적화",
                "description": "KLUE 데이터셋에 최적화된 모델 조합",
                "unified_model": "klue/roberta-base",  # KLUE 기본
                "llm_model": "gpt-4.1-mini"
            },
            "multilingual_support": {
                "name": "다국어 지원",
                "description": "다국어 처리에 특화된 모델 조합",
                "unified_model": "xlm-roberta-base",  # 다국어 지원
                "llm_model": "gpt-4.1-mini"
            },
            "experimental": {
                "name": "실험용",
                "description": "새로운 모델 조합 테스트용",
                "unified_model": "bert-base-multilingual-cased",  # 실험용
                "llm_model": "gpt-4.1-mini"
            }
        }
        
        print(f"✅ 설정 로드 완료 (프로필: {self.current_profile})")
    
    def get_available_profiles(self) -> List[str]:
        """사용 가능한 프로필 목록"""
        return list(self.profiles.keys())
    
    def get_current_profile(self) -> str:
        """현재 프로필"""
        return self.current_profile
    
    def set_profile(self, profile_name: str) -> bool:
        """프로필 변경"""
        if profile_name not in self.profiles:
            print(f"❌ 존재하지 않는 프로필: {profile_name}")
            return False

        self.current_profile = profile_name
        print(f"✅ 프로필 변경: {profile_name}")

        # ServiceFactory 캐시 초기화
        try:
            from app.services.simple_factory import ServiceFactory
            factory = ServiceFactory()
            factory.clear_cache()
            print("🔄 모델 캐시 초기화 완료")
        except Exception as e:
            print(f"⚠️ 캐시 초기화 실패: {e}")

        return True
    
    def get_profile_info(self, profile_name: str = None) -> Dict[str, Any]:
        """프로필 정보"""
        profile = profile_name or self.current_profile
        return self.profiles.get(profile, {})
    
    def print_current_config(self) -> None:
        """현재 설정 출력"""
        profile_info = self.get_profile_info()

        print(f"\n📋 현재 설정 (프로필: {self.current_profile})")
        print("=" * 60)
        print(f"📝 설명: {profile_info.get('description', '')}")
        print(f"🔤 통합 모델: {profile_info.get('unified_model', '')}")
        print(f"🤖 LLM 모델: {profile_info.get('llm_model', '')}")
        print("=" * 60)
    
    def list_all_profiles(self) -> None:
        """모든 프로필 목록 출력"""
        print(f"\n📋 사용 가능한 프로필:")
        print("-" * 50)
        
        for i, (profile_name, profile_info) in enumerate(self.profiles.items(), 1):
            marker = "✅" if profile_name == self.current_profile else "  "
            print(f"{marker} {i}. {profile_name}")
            print(f"     {profile_info.get('description', '')}")
            print(f"     통합 모델: {profile_info.get('unified_model', '')}")
            print(f"     LLM: {profile_info.get('llm_model', '')}")
            print()
    
    def validate_current_config(self) -> bool:
        """현재 설정 검증"""
        try:
            profile_info = self.get_profile_info()
            
            # 필수 필드 확인
            required_fields = ['unified_model', 'llm_model']
            for field in required_fields:
                if not profile_info.get(field):
                    print(f"⚠️ 필수 설정 누락: {field}")
                    return False
            
            print("✅ 설정 검증 완료")
            return True
            
        except Exception as e:
            print(f"❌ 설정 검증 실패: {e}")
            return False
