"""
콘솔 UI 모듈 - 사용자 인터페이스 관련 함수들
"""
from typing import Dict, Any
from app.core.domain import QAPair, BatchResult


def display_welcome() -> None:
    """환영 메시지 출력"""
    print("=" * 80)
    print("🚀 QA Analysis System v2.2")
    print("=" * 80)
    print("📊 한국어 이커머스 문의 분석 시스템")
    print("🤖 4개 한국어 특화 모델 지원")
    print("⚡ 완전 모듈화 구조")
    print("📁 LLM 개발 표준 구조")
    print("=" * 80)


def display_main_menu() -> None:
    """메인 메뉴 출력"""
    print("\n🎯 사용할 기능을 선택하세요:")
    print("-" * 60)
    print("📊 기본 분석:")
    print("  1. 📊 개별 QA 분석")
    print("  2. 📈 배치 QA 분석")
    print("  3. 🧪 모델 테스트")
    print("")
    print("🔍 고급 분석:")
    print("  4. 🔍 모델 성능 비교")
    print("  5. 🏷️ 통합 분류 시스템")
    print("  6. 🔄 하이브리드 분류 파이프라인")
    print("")
    print("⚙️ 관리:")
    print("  7. 🤖 모델 관리")
    print("  8. 📁 결과 관리")
    print("  9. 🔧 프로필 관리")
    print("  10. 📋 시스템 정보")
    print("")
    print("  0. 🚪 종료")
    print("-" * 60)


def get_user_choice(prompt: str, valid_range: range) -> int:
    """사용자 선택 입력 받기"""
    while True:
        try:
            choice = int(input(prompt))
            if choice in valid_range:
                return choice
            print(f"올바른 메뉴를 선택하세요 ({valid_range.start}-{valid_range.stop-1}).")
        except ValueError:
            print("숫자를 입력하세요.")


def get_user_input(prompt: str, required: bool = True) -> str:
    """사용자 입력 받기"""
    while True:
        user_input = input(prompt).strip()
        if user_input or not required:
            return user_input
        print("입력이 필요합니다.")


def print_analysis_result(result: QAPair) -> None:
    """분석 결과 출력"""
    print("\n" + "=" * 60)
    print("📊 QA 분석 결과")
    print("=" * 60)

    print(f"🆔 문의 ID: {result.id}")
    print(f"❓ 질문: {result.question[:100]}...")
    print(f"💬 답변: {result.answer[:100]}..." if result.answer else "💬 답변: (없음)")

    print("\n📈 분석 점수:")
    print(f"  • 의미적 유사도: {result.semantic_similarity:.3f}")
    print(f"  • 주제 관련성: {result.topic_relevance:.3f}")
    print(f"  • 키워드 겹침: {result.keyword_overlap:.3f}")
    print(f"  • 답변 완성도: {result.answer_completeness:.3f}")
    print(f"  • 답변 정확성: {result.answer_accuracy:.3f}")
    print(f"  • 답변 도움도: {result.answer_helpfulness:.3f}")

    print("\n🎯 종합 결과:")
    print(f"  • 전체 점수: {result.overall_score:.3f}")
    print(f"  • 등급: {result.grade.value}")
    print(f"  • 통과 여부: {'✅ 통과' if result.pass_threshold else '❌ 미통과'}")

    if result.strengths:
        print("\n💪 강점:")
        for strength in result.strengths:
            print(f"  • {strength}")

    if result.weaknesses:
        print("\n⚠️ 약점:")
        for weakness in result.weaknesses:
            print(f"  • {weakness}")

    if result.recommendations:
        print("\n💡 개선사항:")
        for rec in result.recommendations:
            print(f"  • {rec}")

    print(f"\n⏱️ 처리 시간: {result.processing_time_ms:.2f}ms")
    print("=" * 60)


def print_batch_summary(batch_result: BatchResult) -> None:
    """배치 분석 결과 요약 출력"""
    print("\n" + "=" * 60)
    print("📊 배치 분석 결과 요약")
    print("=" * 60)
    print(f"📈 총 분석: {batch_result.total_count}건")
    print(f"✅ 통과: {batch_result.pass_count}건 ({batch_result.pass_count/batch_result.total_count*100:.1f}%)")
    print(f"📊 평균 점수: {batch_result.average_score:.3f}")

    print("\n🏆 등급 분포:")
    for grade in ['A', 'B', 'C', 'D', 'F']:
        count = batch_result.grade_distribution.get(grade, 0)
        if count > 0:
            print(f"  • {grade}등급: {count}건")

    print(f"\n⏱️ 처리 시간: {batch_result.processing_time_seconds:.2f}초")
    print("=" * 60)


def print_system_info(config_manager) -> None:
    """시스템 정보 출력"""
    print("\n📋 시스템 정보")
    print("=" * 40)
    print("🚀 QA Analysis System v2.2")
    print("🏗️ LLM 개발 표준 구조")
    print("🇰🇷 한국어 특화 모델 지원")
    print("📁 간결한 폴더 구조")

    profile_info = config_manager.get_profile_info()
    print("\n⚙️ 현재 설정:")
    print(f"  • 프로필: {config_manager.get_current_profile()}")
    print(f"  • 설명: {profile_info.get('description', '')}")
    print(f"  • 임베딩: {profile_info.get('embedding_model', '')}")
    print(f"  • LLM: {profile_info.get('llm_model', '')}")

    print("\n📁 폴더 구조:")
    print("  • app/: 메인 애플리케이션")
    print("  • data/: 데이터 관리")
    print("  • outputs/: 결과 저장")
    print("  • tests/: 테스트 코드")
    print("  • scripts/: 유틸리티")
    print("  • archive/: 기존 파일 백업")


def print_error(message: str) -> None:
    """에러 메시지 출력"""
    print(f"❌ {message}")


def print_success(message: str) -> None:
    """성공 메시지 출력"""
    print(f"✅ {message}")


def print_warning(message: str) -> None:
    """경고 메시지 출력"""
    print(f"⚠️ {message}")


def print_info(message: str) -> None:
    """정보 메시지 출력"""
    print(f"ℹ️ {message}")
