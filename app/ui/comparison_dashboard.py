"""
모델 비교 대시보드 - 비교 결과 시각화
"""
from typing import Dict, List, Any
import json
from pathlib import Path


class ComparisonDashboard:
    """
    모델 비교 대시보드
    
    기능:
    - 비교 결과 시각화
    - 성능 차트 생성
    - 상세 리포트 출력
    - HTML 대시보드 생성
    """
    
    def __init__(self):
        print("✅ 비교 대시보드 초기화 완료")
    
    def display_comparison_summary(self, comparison_result: Dict[str, Any]) -> None:
        """비교 요약 출력"""
        print("\n" + "=" * 80)
        print("📊 모델 성능 비교 요약")
        print("=" * 80)
        
        # 메타데이터
        metadata = comparison_result.get("metadata", {})
        print(f"🆔 비교 ID: {metadata.get('comparison_id', 'N/A')}")
        print(f"📅 실행 시간: {metadata.get('timestamp', 'N/A')}")
        print(f"🤖 테스트 모델: {', '.join(metadata.get('models_tested', []))}")
        print(f"📋 총 요청 수: {metadata.get('total_requests', 0)}건")
        print(f"⏱️ 테스트 시간: {metadata.get('test_duration_seconds', 0):.2f}초")
        print("")
        
        # 성능 순위
        self._display_performance_rankings(comparison_result)
        
        # 추천사항
        self._display_recommendations(comparison_result)
    
    def display_detailed_metrics(self, comparison_result: Dict[str, Any]) -> None:
        """상세 메트릭 출력"""
        print("\n" + "=" * 80)
        print("📈 상세 성능 메트릭")
        print("=" * 80)
        
        performance_metrics = comparison_result.get("performance_metrics", {})
        accuracy_metrics = comparison_result.get("accuracy_metrics", {})
        
        for model_name in performance_metrics.keys():
            print(f"\n🤖 {model_name}")
            print("-" * 50)
            
            # 성능 메트릭
            perf = performance_metrics[model_name]
            print(f"  📊 평균 점수: {perf.get('avg_score', 0):.3f}")
            print(f"  ✅ 통과율: {perf.get('pass_rate', 0):.1f}%")
            print(f"  ⏱️ 평균 처리 시간: {perf.get('avg_processing_time', 0):.2f}ms")
            print(f"  🔄 처리량: {perf.get('throughput', 0):.2f} req/s")
            
            # 정확도 메트릭
            acc = accuracy_metrics.get(model_name, {})
            print(f"  🎯 의미적 유사도: {acc.get('semantic_similarity', 0):.3f}")
            print(f"  📝 주제 관련성: {acc.get('topic_relevance', 0):.3f}")
            print(f"  ✔️ 답변 정확성: {acc.get('answer_accuracy', 0):.3f}")
            print(f"  📊 일관성: {acc.get('consistency', 0):.3f}")
    
    def display_performance_chart(self, comparison_result: Dict[str, Any]) -> None:
        """성능 차트 텍스트 출력"""
        print("\n" + "=" * 80)
        print("📊 성능 차트 (텍스트 버전)")
        print("=" * 80)
        
        performance_metrics = comparison_result.get("performance_metrics", {})
        
        if not performance_metrics:
            print("❌ 성능 데이터가 없습니다.")
            return
        
        # 평균 점수 차트
        print("\n📈 평균 점수 비교:")
        max_score = max(m.get("avg_score", 0) for m in performance_metrics.values())
        
        for model_name, metrics in performance_metrics.items():
            score = metrics.get("avg_score", 0)
            bar_length = int((score / max_score) * 40) if max_score > 0 else 0
            bar = "█" * bar_length + "░" * (40 - bar_length)
            print(f"  {model_name:20} │{bar}│ {score:.3f}")
        
        # 처리 시간 차트
        print("\n⏱️ 처리 시간 비교 (낮을수록 좋음):")
        max_time = max(m.get("avg_processing_time", 0) for m in performance_metrics.values())
        
        for model_name, metrics in performance_metrics.items():
            time_ms = metrics.get("avg_processing_time", 0)
            bar_length = int((time_ms / max_time) * 40) if max_time > 0 else 0
            bar = "█" * bar_length + "░" * (40 - bar_length)
            print(f"  {model_name:20} │{bar}│ {time_ms:.2f}ms")
    
    def generate_html_dashboard(
        self, 
        comparison_result: Dict[str, Any], 
        output_path: str = "outputs/dashboard.html"
    ) -> bool:
        """HTML 대시보드 생성"""
        try:
            html_content = self._create_html_dashboard(comparison_result)
            
            # 출력 디렉토리 생성
            output_file = Path(output_path)
            output_file.parent.mkdir(parents=True, exist_ok=True)
            
            # HTML 파일 저장
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            print(f"✅ HTML 대시보드 생성 완료: {output_file}")
            return True
            
        except Exception as e:
            print(f"❌ HTML 대시보드 생성 실패: {e}")
            return False
    
    def export_comparison_data(
        self, 
        comparison_result: Dict[str, Any], 
        output_path: str = "outputs/comparison_data.json"
    ) -> bool:
        """비교 데이터 JSON 내보내기"""
        try:
            # 출력 디렉토리 생성
            output_file = Path(output_path)
            output_file.parent.mkdir(parents=True, exist_ok=True)
            
            # JSON 파일 저장
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(comparison_result, f, ensure_ascii=False, indent=2, default=str)
            
            print(f"✅ 비교 데이터 내보내기 완료: {output_file}")
            return True
            
        except Exception as e:
            print(f"❌ 비교 데이터 내보내기 실패: {e}")
            return False
    
    def _display_performance_rankings(self, comparison_result: Dict[str, Any]) -> None:
        """성능 순위 출력"""
        summary = comparison_result.get("comparison_summary", {})
        
        print("🏆 성능 순위:")
        print(f"  🥇 전체 성능 최고: {summary.get('best_overall_score', 'N/A')}")
        print(f"  ⚡ 처리 속도 최고: {summary.get('fastest_processing', 'N/A')}")
        print(f"  🔄 처리량 최고: {summary.get('highest_throughput', 'N/A')}")
        print(f"  📊 일관성 최고: {summary.get('most_consistent', 'N/A')}")
        print("")
    
    def _display_recommendations(self, comparison_result: Dict[str, Any]) -> None:
        """추천사항 출력"""
        recommendations = comparison_result.get("recommendations", [])
        
        if recommendations:
            print("💡 추천사항:")
            for rec in recommendations:
                if rec.strip():  # 빈 줄이 아닌 경우만
                    print(f"  {rec}")
        print("")
    
    def _create_html_dashboard(self, comparison_result: Dict[str, Any]) -> str:
        """HTML 대시보드 생성"""
        metadata = comparison_result.get("metadata", {})
        performance_metrics = comparison_result.get("performance_metrics", {})
        accuracy_metrics = comparison_result.get("accuracy_metrics", {})
        summary = comparison_result.get("comparison_summary", {})
        recommendations = comparison_result.get("recommendations", [])
        
        # Chart.js 데이터 준비
        chart_data = self._prepare_chart_data(comparison_result)
        
        html_template = f"""
<!DOCTYPE html>
<html lang="ko">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>모델 성능 비교 대시보드</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {{
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }}
        .container {{
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }}
        .header {{
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #eee;
        }}
        .metrics-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }}
        .metric-card {{
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }}
        .chart-container {{
            margin: 30px 0;
            height: 400px;
        }}
        .recommendations {{
            background: #e7f3ff;
            padding: 20px;
            border-radius: 8px;
            margin-top: 30px;
        }}
        .model-table {{
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }}
        .model-table th, .model-table td {{
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }}
        .model-table th {{
            background-color: #f8f9fa;
            font-weight: bold;
        }}
        .best-model {{
            background-color: #d4edda;
            font-weight: bold;
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 모델 성능 비교 대시보드</h1>
            <p>비교 ID: {metadata.get('comparison_id', 'N/A')} | 
               실행 시간: {metadata.get('timestamp', 'N/A')}</p>
            <p>테스트 모델: {', '.join(metadata.get('models_tested', []))} | 
               총 요청: {metadata.get('total_requests', 0)}건</p>
        </div>
        
        <div class="metrics-grid">
            <div class="metric-card">
                <h3>🏆 최고 성능</h3>
                <p>{summary.get('best_overall_score', 'N/A')}</p>
            </div>
            <div class="metric-card">
                <h3>⚡ 최고 속도</h3>
                <p>{summary.get('fastest_processing', 'N/A')}</p>
            </div>
            <div class="metric-card">
                <h3>🔄 최고 처리량</h3>
                <p>{summary.get('highest_throughput', 'N/A')}</p>
            </div>
            <div class="metric-card">
                <h3>📊 최고 일관성</h3>
                <p>{summary.get('most_consistent', 'N/A')}</p>
            </div>
        </div>
        
        <div class="chart-container">
            <canvas id="performanceChart"></canvas>
        </div>
        
        <div class="chart-container">
            <canvas id="speedChart"></canvas>
        </div>
        
        <h2>📊 상세 메트릭</h2>
        <table class="model-table">
            <thead>
                <tr>
                    <th>모델</th>
                    <th>평균 점수</th>
                    <th>통과율 (%)</th>
                    <th>처리 시간 (ms)</th>
                    <th>처리량 (req/s)</th>
                    <th>일관성</th>
                </tr>
            </thead>
            <tbody>
                {self._generate_table_rows(performance_metrics, accuracy_metrics, summary)}
            </tbody>
        </table>
        
        <div class="recommendations">
            <h2>💡 추천사항</h2>
            <ul>
                {self._generate_recommendation_list(recommendations)}
            </ul>
        </div>
    </div>
    
    <script>
        // 성능 차트
        const performanceCtx = document.getElementById('performanceChart').getContext('2d');
        new Chart(performanceCtx, {{
            type: 'bar',
            data: {json.dumps(chart_data['performance'])},
            options: {{
                responsive: true,
                maintainAspectRatio: false,
                plugins: {{
                    title: {{
                        display: true,
                        text: '모델별 성능 비교'
                    }}
                }},
                scales: {{
                    y: {{
                        beginAtZero: true,
                        max: 1
                    }}
                }}
            }}
        }});
        
        // 속도 차트
        const speedCtx = document.getElementById('speedChart').getContext('2d');
        new Chart(speedCtx, {{
            type: 'bar',
            data: {json.dumps(chart_data['speed'])},
            options: {{
                responsive: true,
                maintainAspectRatio: false,
                plugins: {{
                    title: {{
                        display: true,
                        text: '모델별 처리 속도 비교'
                    }}
                }},
                scales: {{
                    y: {{
                        beginAtZero: true
                    }}
                }}
            }}
        }});
    </script>
</body>
</html>
        """
        
        return html_template
    
    def _prepare_chart_data(self, comparison_result: Dict[str, Any]) -> Dict[str, Any]:
        """차트 데이터 준비"""
        performance_metrics = comparison_result.get("performance_metrics", {})
        
        models = list(performance_metrics.keys())
        
        return {
            "performance": {
                "labels": models,
                "datasets": [{
                    "label": "평균 점수",
                    "data": [performance_metrics[m].get("avg_score", 0) for m in models],
                    "backgroundColor": "rgba(54, 162, 235, 0.8)"
                }]
            },
            "speed": {
                "labels": models,
                "datasets": [{
                    "label": "처리 시간 (ms)",
                    "data": [performance_metrics[m].get("avg_processing_time", 0) for m in models],
                    "backgroundColor": "rgba(255, 99, 132, 0.8)"
                }]
            }
        }
    
    def _generate_table_rows(
        self, 
        performance_metrics: Dict[str, Any], 
        accuracy_metrics: Dict[str, Any], 
        summary: Dict[str, Any]
    ) -> str:
        """테이블 행 생성"""
        rows = []
        best_model = summary.get("best_overall_score", "")
        
        for model_name in performance_metrics.keys():
            perf = performance_metrics[model_name]
            acc = accuracy_metrics.get(model_name, {})
            
            css_class = "best-model" if model_name == best_model else ""
            
            row = f"""
                <tr class="{css_class}">
                    <td>{model_name}</td>
                    <td>{perf.get('avg_score', 0):.3f}</td>
                    <td>{perf.get('pass_rate', 0):.1f}</td>
                    <td>{perf.get('avg_processing_time', 0):.2f}</td>
                    <td>{perf.get('throughput', 0):.2f}</td>
                    <td>{acc.get('consistency', 0):.3f}</td>
                </tr>
            """
            rows.append(row)
        
        return "".join(rows)
    
    def _generate_recommendation_list(self, recommendations: List[str]) -> str:
        """추천사항 리스트 생성"""
        items = []
        for rec in recommendations:
            if rec.strip():
                items.append(f"<li>{rec}</li>")
        return "".join(items)
