"""
LLM 비용 계산 유틸리티
"""
from typing import Dict, Any
import tiktoken


class LLMCostCalculator:
    """LLM 사용 비용 계산기"""

    # GPT-4.1-mini 가격 (USD per 1M tokens)
    GPT_4_1_MINI_PRICES = {
        "input": 0.40,
        "cached_input": 0.10,
        "output": 1.60
    }

    # 환율
    USD_TO_KRW = 1400

    def __init__(self):
        try:
            self.encoding = tiktoken.encoding_for_model("gpt-4")
        except Exception:
            # 폴백: cl100k_base 인코딩 사용
            self.encoding = tiktoken.get_encoding("cl100k_base")

    def count_tokens(self, text: str) -> int:
        """텍스트의 토큰 수 계산"""
        if not text:
            return 0
        return len(self.encoding.encode(text))

    def calculate_cost(
        self,
        input_text: str,
        output_text: str = "",
        cached_input: bool = False
    ) -> Dict[str, Any]:
        """비용 계산"""
        input_tokens = self.count_tokens(input_text)
        output_tokens = self.count_tokens(output_text)

        # 비용 계산 (USD)
        if cached_input:
            input_cost_usd = (input_tokens / 1_000_000) * self.GPT_4_1_MINI_PRICES["cached_input"]
        else:
            input_cost_usd = (input_tokens / 1_000_000) * self.GPT_4_1_MINI_PRICES["input"]

        output_cost_usd = (output_tokens / 1_000_000) * self.GPT_4_1_MINI_PRICES["output"]
        total_cost_usd = input_cost_usd + output_cost_usd

        # 원화 환산
        input_cost_krw = input_cost_usd * self.USD_TO_KRW
        output_cost_krw = output_cost_usd * self.USD_TO_KRW
        total_cost_krw = total_cost_usd * self.USD_TO_KRW

        return {
            "tokens": {
                "input": input_tokens,
                "output": output_tokens,
                "total": input_tokens + output_tokens
            },
            "cost_usd": {
                "input": input_cost_usd,
                "output": output_cost_usd,
                "total": total_cost_usd
            },
            "cost_krw": {
                "input": input_cost_krw,
                "output": output_cost_krw,
                "total": total_cost_krw
            },
            "cached_input": cached_input
        }

    def format_cost_info(self, cost_info: Dict[str, Any]) -> str:
        """비용 정보 포맷팅"""
        tokens = cost_info["tokens"]
        cost_usd = cost_info["cost_usd"]
        cost_krw = cost_info["cost_krw"]

        result = """
💰 LLM 사용 비용:
  📊 토큰 사용량:
    • 입력: {tokens['input']:,} 토큰
    • 출력: {tokens['output']:,} 토큰
    • 총합: {tokens['total']:,} 토큰

  💵 비용 (USD):
    • 입력: ${cost_usd['input']:.6f}
    • 출력: ${cost_usd['output']:.6f}
    • 총합: ${cost_usd['total']:.6f}

  💴 비용 (KRW):
    • 입력: ₩{cost_krw['input']:.2f}
    • 출력: ₩{cost_krw['output']:.2f}
    • 총합: ₩{cost_krw['total']:.2f}
"""

        if cost_info["cached_input"]:
            result += "  ⚡ 캐시된 입력 할인 적용됨"

        return result.strip()

    @staticmethod
    def estimate_monthly_cost(daily_requests: int, avg_input_tokens: int, avg_output_tokens: int) -> Dict[str, float]:
        """월간 예상 비용 계산"""
        monthly_requests = daily_requests * 30

        monthly_input_tokens = monthly_requests * avg_input_tokens
        monthly_output_tokens = monthly_requests * avg_output_tokens

        # 비용 계산
        input_cost_usd = (monthly_input_tokens / 1_000_000) * LLMCostCalculator.GPT_4_1_MINI_PRICES["input"]
        output_cost_usd = (monthly_output_tokens / 1_000_000) * LLMCostCalculator.GPT_4_1_MINI_PRICES["output"]
        total_cost_usd = input_cost_usd + output_cost_usd

        return {
            "monthly_requests": monthly_requests,
            "monthly_tokens": monthly_input_tokens + monthly_output_tokens,
            "cost_usd": total_cost_usd,
            "cost_krw": total_cost_usd * LLMCostCalculator.USD_TO_KRW
        }
