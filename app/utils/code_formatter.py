"""
코드 포맷팅 유틸리티 - 코드 가독성 개선
"""
import re
from typing import List, Dict, Any


class CodeFormatter:
    """코드 포맷팅 유틸리티"""
    
    @staticmethod
    def remove_trailing_whitespace(content: str) -> str:
        """Trailing whitespace 제거"""
        lines = content.split('\n')
        cleaned_lines = [line.rstrip() for line in lines]
        return '\n'.join(cleaned_lines)
    
    @staticmethod
    def fix_f_string_placeholders(content: str) -> str:
        """불필요한 f-string 수정"""
        # f"문자열" 패턴에서 {} 없는 경우 일반 문자열로 변경
        pattern = r'f"([^"]*)"'
        
        def replace_func(match):
            string_content = match.group(1)
            if '{' not in string_content and '}' not in string_content:
                return f'"{string_content}"'
            return match.group(0)
        
        return re.sub(pattern, replace_func, content)
    
    @staticmethod
    def add_type_hints(function_def: str) -> str:
        """타입 힌트 추가 (간단한 경우만)"""
        # 기본적인 타입 힌트 패턴
        patterns = {
            r'def (\w+)\(self\):': r'def \1(self) -> None:',
            r'def (\w+)\(\):': r'def \1() -> None:',
            r'def (\w+)\(self, (\w+)\):': r'def \1(self, \2: str) -> None:',
        }
        
        result = function_def
        for pattern, replacement in patterns.items():
            result = re.sub(pattern, replacement, result)
        
        return result
    
    @staticmethod
    def format_imports(content: str) -> str:
        """Import 문 정리"""
        lines = content.split('\n')
        import_lines = []
        other_lines = []
        
        for line in lines:
            if line.strip().startswith(('import ', 'from ')):
                import_lines.append(line)
            else:
                other_lines.append(line)
        
        # Import 정렬
        import_lines.sort()
        
        # 빈 줄 추가
        if import_lines and other_lines:
            import_lines.append('')
        
        return '\n'.join(import_lines + other_lines)
    
    @staticmethod
    def add_docstrings(content: str) -> str:
        """기본 docstring 추가"""
        # 클래스와 함수에 docstring이 없는 경우 추가
        patterns = {
            r'(class \w+.*?:)\n(\s+)([^"\s])': r'\1\n\2"""\3',
            r'(def \w+.*?:)\n(\s+)([^"\s])': r'\1\n\2"""\3',
        }
        
        result = content
        for pattern, replacement in patterns.items():
            result = re.sub(pattern, replacement, result, flags=re.MULTILINE)
        
        return result


class QualityChecker:
    """코드 품질 검사기"""
    
    @staticmethod
    def check_function_length(content: str, max_lines: int = 50) -> List[Dict[str, Any]]:
        """함수 길이 검사"""
        issues = []
        lines = content.split('\n')
        
        current_function = None
        function_start = 0
        indent_level = 0
        
        for i, line in enumerate(lines, 1):
            stripped = line.strip()
            
            if stripped.startswith('def '):
                if current_function:
                    # 이전 함수 길이 검사
                    length = i - function_start
                    if length > max_lines:
                        issues.append({
                            'type': 'long_function',
                            'function': current_function,
                            'line': function_start,
                            'length': length,
                            'message': f'함수가 너무 깁니다 ({length}줄 > {max_lines}줄)'
                        })
                
                current_function = stripped.split('(')[0].replace('def ', '')
                function_start = i
                indent_level = len(line) - len(line.lstrip())
        
        return issues
    
    @staticmethod
    def check_complexity(content: str) -> List[Dict[str, Any]]:
        """복잡도 검사"""
        issues = []
        lines = content.split('\n')
        
        for i, line in enumerate(lines, 1):
            # 중첩된 if문 검사
            if_count = line.count('if ') + line.count('elif ')
            for_count = line.count('for ')
            while_count = line.count('while ')
            
            total_complexity = if_count + for_count + while_count
            
            if total_complexity > 3:
                issues.append({
                    'type': 'high_complexity',
                    'line': i,
                    'complexity': total_complexity,
                    'message': f'라인 복잡도가 높습니다 ({total_complexity})'
                })
        
        return issues
    
    @staticmethod
    def check_naming_conventions(content: str) -> List[Dict[str, Any]]:
        """네이밍 컨벤션 검사"""
        issues = []
        lines = content.split('\n')
        
        for i, line in enumerate(lines, 1):
            # 클래스명 검사 (PascalCase)
            class_match = re.search(r'class (\w+)', line)
            if class_match:
                class_name = class_match.group(1)
                if not class_name[0].isupper():
                    issues.append({
                        'type': 'naming_convention',
                        'line': i,
                        'name': class_name,
                        'message': f'클래스명은 PascalCase를 사용해야 합니다: {class_name}'
                    })
            
            # 함수명 검사 (snake_case)
            func_match = re.search(r'def (\w+)', line)
            if func_match:
                func_name = func_match.group(1)
                if not func_name.islower() and '_' not in func_name:
                    issues.append({
                        'type': 'naming_convention',
                        'line': i,
                        'name': func_name,
                        'message': f'함수명은 snake_case를 사용해야 합니다: {func_name}'
                    })
        
        return issues
