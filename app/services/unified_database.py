"""
통합 데이터베이스 서비스 - SOLID 원칙 적용
"""
from typing import List, Dict, Any, Optional
from abc import ABC, abstractmethod
import asyncio
from supabase import create_client, Client
import logging

logger = logging.getLogger(__name__)


class IDatabaseService(ABC):
    """데이터베이스 서비스 인터페이스 (Interface Segregation Principle)"""
    
    @abstractmethod
    async def connect(self) -> bool:
        """데이터베이스 연결"""
        pass
    
    @abstractmethod
    async def get_sample_inquiries(self, limit: int = 10) -> List[Dict[str, Any]]:
        """샘플 문의 데이터 조회"""
        pass
    
    @abstractmethod
    async def save_analysis_result(self, result: Dict[str, Any]) -> bool:
        """분석 결과 저장"""
        pass


class SupabaseDatabaseService(IDatabaseService):
    """
    Supabase 데이터베이스 서비스 (Single Responsibility Principle)
    
    책임:
    - Supabase 연결 관리
    - playauto_qna 테이블 조작
    - 데이터 CRUD 작업
    """
    
    def __init__(self, url: str, key: str):
        self.url = url
        self.key = key
        self.client: Optional[Client] = None
        self._connected = False
    
    async def connect(self) -> bool:
        """데이터베이스 연결 (Dependency Inversion Principle)"""
        try:
            self.client = create_client(self.url, self.key)
            # 연결 테스트
            response = self.client.table('playauto_qna').select('id').limit(1).execute()
            self._connected = True
            logger.info("✅ Supabase 연결 성공")
            return True
        except Exception as e:
            logger.error(f"❌ Supabase 연결 실패: {e}")
            self._connected = False
            return False
    
    async def get_sample_inquiries(self, limit: int = 10) -> List[Dict[str, Any]]:
        """샘플 문의 데이터 조회"""
        if not self._connected or not self.client:
            await self.connect()
        
        try:
            response = self.client.table('playauto_qna').select(
                'id, inquiry_content, answer_content, company_code, mall_name, inquiry_at, answered_at'
            ).limit(limit).execute()
            
            return response.data if response.data else []
        except Exception as e:
            logger.error(f"❌ 데이터 조회 실패: {e}")
            return []
    
    async def save_analysis_result(self, result: Dict[str, Any]) -> bool:
        """분석 결과 저장"""
        if not self._connected or not self.client:
            await self.connect()
        
        try:
            # 분석 결과를 별도 테이블에 저장
            response = self.client.table('qa_analysis_results').insert(result).execute()
            return True
        except Exception as e:
            logger.error(f"❌ 결과 저장 실패: {e}")
            return False
    
    async def get_inquiry_by_id(self, inquiry_id: str) -> Optional[Dict[str, Any]]:
        """ID로 문의 조회"""
        if not self._connected or not self.client:
            await self.connect()
        
        try:
            response = self.client.table('playauto_qna').select('*').eq('id', inquiry_id).execute()
            return response.data[0] if response.data else None
        except Exception as e:
            logger.error(f"❌ 문의 조회 실패: {e}")
            return None
    
    async def get_inquiries_by_category(self, category: str, limit: int = 50) -> List[Dict[str, Any]]:
        """카테고리별 문의 조회"""
        if not self._connected or not self.client:
            await self.connect()
        
        try:
            # 카테고리 필터링 (키워드 기반)
            response = self.client.table('playauto_qna').select('*').ilike(
                'inquiry_content', f'%{category}%'
            ).limit(limit).execute()
            
            return response.data if response.data else []
        except Exception as e:
            logger.error(f"❌ 카테고리별 조회 실패: {e}")
            return []
    
    async def get_statistics(self) -> Dict[str, Any]:
        """데이터베이스 통계 조회"""
        if not self._connected or not self.client:
            await self.connect()
        
        try:
            # 전체 문의 수
            total_response = self.client.table('playauto_qna').select('id', count='exact').execute()
            total_count = total_response.count if total_response.count else 0
            
            # 답변 있는 문의 수
            answered_response = self.client.table('playauto_qna').select(
                'id', count='exact'
            ).not_.is_('answer_content', 'null').execute()
            answered_count = answered_response.count if answered_response.count else 0
            
            return {
                "total_inquiries": total_count,
                "answered_inquiries": answered_count,
                "unanswered_inquiries": total_count - answered_count,
                "answer_rate": (answered_count / total_count * 100) if total_count > 0 else 0
            }
        except Exception as e:
            logger.error(f"❌ 통계 조회 실패: {e}")
            return {}


class MockDatabaseService(IDatabaseService):
    """
    목 데이터베이스 서비스 (Liskov Substitution Principle)
    테스트 및 개발용
    """
    
    def __init__(self):
        self._connected = False
        self.mock_data = [
            {
                "id": "mock_1",
                "inquiry_content": "배송 주문번호 문의내용 상품 언제 발송되는지 문의주셨습니다 확인 부탁드립니다 차 긴급 영업일 기준 시간 내 회신 없을 경우 차 긴급 메시지를 발송 예정입니다 답변 불충분 또는 미답변 시 구매자의 요청에 따라 환불처리가 진행될 수 있으니 판매자님께 피해가 없도록 기한 내 회신을 부탁드립니다 이후 개정된 마켓 판매회원 이용 약관과 옥션 이용약관에 따라 배송정보의 누락 또는 부정확한 정보입력의 경우 구매자의 요청에 따라 주문을 취소하고 구매자에게 환불할 수 있음을 참고 부탁드립니다 이와 관련된 상세한 내용은 내 전체마켓옥션 공지사항 메뉴에서 확인 부탁드립니다 공지번호 공지제목 마켓옥션 마켓 판매회원약관 및 옥션이용약관 개정에 따른 사전공지 감사합니다 좋은 하루 되십시오?",
                "answer_content": "안녕하세요. 확인후 연락드리겠습니다.",
                "company_code": "TEST001",
                "mall_name": "테스트몰",
                "inquiry_at": "2024-01-01T10:00:00",
                "answered_at": "2024-01-01T11:00:00"
            },
            {
                "id": "mock_2", 
                "inquiry_content": "안녕하세요 먼저 비공개 를 통해 안내드리게 되어 양해 부탁드립니다 간단한 답변만 남겨주셔도 상품의 인기도 향상에 도움이 될 수 있습니다 저희는 년 이상 온라인 마케팅을 진행해 온 기업으로 많은 판매자분들의 성공을 돕고 있습니다 대표님의 판매 촉진을 위해 신뢰 기반의 마케팅을 제공해 드리며 궁금한 점은 언제든지 문의해 주세요 응답 주시는 분들께는 상품 찜과 알림받기를 무료로 제공해 드리며 짧은 기간 내 효과를 경험하실 수 있도록 지원해 드립니다 초기 마케팅은 간단한 테스트로 진행 가능하며 효과가 없을 경우 환불도 보장되니 부담 없이 시도해 보시길 권장드립니다 최근 불법 가상 계정을 이용한 광고가 늘어나고 있어 안전한 마케팅을 위해 업체 선택 시 아래 사항을 고려해 보세요 광고 참여자가 실제 사용자 기반인지 광고 플랫폼이 직접 운영 및 관리되는 곳인지 구매전환율을 높이기 위해서는 방문 고객이 신뢰할 수 있는 제품 정보를 제공하는 것이 중요합니다 검색 순위 상승 및 바이럴 마케팅 공동구매를 적극 활용하시면 추가적인 매출 증가 효과를 얻을 수 있습니다 자세한 문의는 아래 링크를 통해 부탁드립니다 지금 바로 상담해 보시고 효과를 직접 확인해 보세요",
                "answer_content": "수고하세요.",
                "company_code": "TEST002",
                "mall_name": "테스트몰",
                "inquiry_at": "2024-01-01T12:00:00",
                "answered_at": "2024-01-01T13:00:00"
            }
        ]
    
    async def connect(self) -> bool:
        """목 연결"""
        self._connected = True
        logger.info("✅ Mock 데이터베이스 연결 성공")
        return True
    
    async def get_sample_inquiries(self, limit: int = 10) -> List[Dict[str, Any]]:
        """목 샘플 데이터"""
        return self.mock_data[:limit]
    
    async def save_analysis_result(self, result: Dict[str, Any]) -> bool:
        """목 저장"""
        logger.info(f"📝 Mock 저장: {result.get('id', 'unknown')}")
        return True


class DatabaseServiceFactory:
    """
    데이터베이스 서비스 팩토리 (Factory Pattern + Dependency Inversion)
    """
    
    @staticmethod
    def create_service(
        service_type: str = "supabase",
        url: Optional[str] = None,
        key: Optional[str] = None
    ) -> IDatabaseService:
        """데이터베이스 서비스 생성"""
        
        if service_type == "supabase":
            if not url or not key:
                logger.warning("⚠️ Supabase 설정이 없어 Mock 서비스를 사용합니다.")
                return MockDatabaseService()
            return SupabaseDatabaseService(url, key)
        
        elif service_type == "mock":
            return MockDatabaseService()
        
        else:
            raise ValueError(f"지원하지 않는 데이터베이스 타입: {service_type}")


class DatabaseManager:
    """
    데이터베이스 매니저 (Facade Pattern)
    복잡한 데이터베이스 작업을 간단한 인터페이스로 제공
    """
    
    def __init__(self, service: IDatabaseService):
        self.service = service
        self._initialized = False
    
    async def initialize(self) -> bool:
        """초기화"""
        if not self._initialized:
            self._initialized = await self.service.connect()
        return self._initialized
    
    async def get_sample_data(self, limit: int = 10) -> List[Dict[str, Any]]:
        """샘플 데이터 조회 (간단한 인터페이스)"""
        await self.initialize()
        return await self.service.get_sample_inquiries(limit)
    
    async def save_result(self, result: Dict[str, Any]) -> bool:
        """결과 저장 (간단한 인터페이스)"""
        await self.initialize()
        return await self.service.save_analysis_result(result)
    
    async def get_database_info(self) -> Dict[str, Any]:
        """데이터베이스 정보 조회"""
        await self.initialize()
        
        if hasattr(self.service, 'get_statistics'):
            stats = await self.service.get_statistics()
            return {
                "service_type": type(self.service).__name__,
                "connected": self._initialized,
                "statistics": stats
            }
        else:
            return {
                "service_type": type(self.service).__name__,
                "connected": self._initialized,
                "statistics": {}
            }
