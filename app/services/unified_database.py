"""
통합 데이터베이스 서비스 - SOLID 원칙 적용
"""
from typing import List, Dict, Any, Optional
from abc import ABC, abstractmethod
import asyncio
from supabase import create_client, Client
import logging

logger = logging.getLogger(__name__)


class IDatabaseService(ABC):
    """데이터베이스 서비스 인터페이스 (Interface Segregation Principle)"""
    
    @abstractmethod
    async def connect(self) -> bool:
        """데이터베이스 연결"""
        pass
    
    @abstractmethod
    async def get_sample_inquiries(self, limit: int = 10) -> List[Dict[str, Any]]:
        """샘플 문의 데이터 조회"""
        pass
    
    @abstractmethod
    async def save_analysis_result(self, result: Dict[str, Any]) -> bool:
        """분석 결과 저장"""
        pass


class SupabaseDatabaseService(IDatabaseService):
    """
    Supabase 데이터베이스 서비스 (Single Responsibility Principle)
    
    책임:
    - Supabase 연결 관리
    - playauto_qna 테이블 조작
    - 데이터 CRUD 작업
    """
    
    def __init__(self, url: str, key: str):
        self.url = url
        self.key = key
        self.client: Optional[Client] = None
        self._connected = False
    
    async def connect(self) -> bool:
        """데이터베이스 연결 (Dependency Inversion Principle)"""
        try:
            self.client = create_client(self.url, self.key)
            # 연결 테스트
            response = self.client.table('playauto_qna').select('id').limit(1).execute()
            self._connected = True
            logger.info("✅ Supabase 연결 성공")
            return True
        except Exception as e:
            logger.error(f"❌ Supabase 연결 실패: {e}")
            self._connected = False
            return False
    
    async def get_sample_inquiries(self, limit: int = 10) -> List[Dict[str, Any]]:
        """샘플 문의 데이터 조회"""
        if not self._connected or not self.client:
            await self.connect()
        
        try:
            response = self.client.table('playauto_qna').select(
                'id, inquiry_content, answer_content, company_code, mall_name, inquiry_at, answered_at'
            ).limit(limit).execute()
            
            return response.data if response.data else []
        except Exception as e:
            logger.error(f"❌ 데이터 조회 실패: {e}")
            return []
    
    async def save_analysis_result(self, result: Dict[str, Any]) -> bool:
        """분석 결과 저장"""
        if not self._connected or not self.client:
            await self.connect()
        
        try:
            # 분석 결과를 별도 테이블에 저장
            response = self.client.table('qa_analysis_results').insert(result).execute()
            return True
        except Exception as e:
            logger.error(f"❌ 결과 저장 실패: {e}")
            return False
    
    async def get_inquiry_by_id(self, inquiry_id: str) -> Optional[Dict[str, Any]]:
        """ID로 문의 조회"""
        if not self._connected or not self.client:
            await self.connect()
        
        try:
            response = self.client.table('playauto_qna').select('*').eq('id', inquiry_id).execute()
            return response.data[0] if response.data else None
        except Exception as e:
            logger.error(f"❌ 문의 조회 실패: {e}")
            return None
    
    async def get_inquiries_by_category(self, category: str, limit: int = 50) -> List[Dict[str, Any]]:
        """카테고리별 문의 조회"""
        if not self._connected or not self.client:
            await self.connect()
        
        try:
            # 카테고리 필터링 (키워드 기반)
            response = self.client.table('playauto_qna').select('*').ilike(
                'inquiry_content', f'%{category}%'
            ).limit(limit).execute()
            
            return response.data if response.data else []
        except Exception as e:
            logger.error(f"❌ 카테고리별 조회 실패: {e}")
            return []
    
    async def get_statistics(self) -> Dict[str, Any]:
        """데이터베이스 통계 조회"""
        if not self._connected or not self.client:
            await self.connect()
        
        try:
            # 전체 문의 수
            total_response = self.client.table('playauto_qna').select('id', count='exact').execute()
            total_count = total_response.count if total_response.count else 0
            
            # 답변 있는 문의 수
            answered_response = self.client.table('playauto_qna').select(
                'id', count='exact'
            ).not_.is_('answer_content', 'null').execute()
            answered_count = answered_response.count if answered_response.count else 0
            
            return {
                "total_inquiries": total_count,
                "answered_inquiries": answered_count,
                "unanswered_inquiries": total_count - answered_count,
                "answer_rate": (answered_count / total_count * 100) if total_count > 0 else 0
            }
        except Exception as e:
            logger.error(f"❌ 통계 조회 실패: {e}")
            return {}


class MockDatabaseService(IDatabaseService):
    """
    목 데이터베이스 서비스 (Liskov Substitution Principle)
    테스트 및 개발용
    """
    
    def __init__(self):
        self._connected = False
        self.mock_data = [
            {
                "id": "mock_1",
                "inquiry_content": "배송이 너무 늦어요. 언제 도착하나요?",
                "answer_content": "안녕하세요. 배송 지연에 대해 죄송합니다.",
                "company_code": "TEST001",
                "mall_name": "테스트몰",
                "inquiry_at": "2024-01-01T10:00:00",
                "answered_at": "2024-01-01T11:00:00"
            },
            {
                "id": "mock_2", 
                "inquiry_content": "상품이 설명과 달라요. 환불 가능한가요?",
                "answer_content": "고객님, 환불 절차를 안내드리겠습니다.",
                "company_code": "TEST002",
                "mall_name": "테스트몰",
                "inquiry_at": "2024-01-01T12:00:00",
                "answered_at": "2024-01-01T13:00:00"
            }
        ]
    
    async def connect(self) -> bool:
        """목 연결"""
        self._connected = True
        logger.info("✅ Mock 데이터베이스 연결 성공")
        return True
    
    async def get_sample_inquiries(self, limit: int = 10) -> List[Dict[str, Any]]:
        """목 샘플 데이터"""
        return self.mock_data[:limit]
    
    async def save_analysis_result(self, result: Dict[str, Any]) -> bool:
        """목 저장"""
        logger.info(f"📝 Mock 저장: {result.get('id', 'unknown')}")
        return True


class DatabaseServiceFactory:
    """
    데이터베이스 서비스 팩토리 (Factory Pattern + Dependency Inversion)
    """
    
    @staticmethod
    def create_service(
        service_type: str = "supabase",
        url: Optional[str] = None,
        key: Optional[str] = None
    ) -> IDatabaseService:
        """데이터베이스 서비스 생성"""
        
        if service_type == "supabase":
            if not url or not key:
                logger.warning("⚠️ Supabase 설정이 없어 Mock 서비스를 사용합니다.")
                return MockDatabaseService()
            return SupabaseDatabaseService(url, key)
        
        elif service_type == "mock":
            return MockDatabaseService()
        
        else:
            raise ValueError(f"지원하지 않는 데이터베이스 타입: {service_type}")


class DatabaseManager:
    """
    데이터베이스 매니저 (Facade Pattern)
    복잡한 데이터베이스 작업을 간단한 인터페이스로 제공
    """
    
    def __init__(self, service: IDatabaseService):
        self.service = service
        self._initialized = False
    
    async def initialize(self) -> bool:
        """초기화"""
        if not self._initialized:
            self._initialized = await self.service.connect()
        return self._initialized
    
    async def get_sample_data(self, limit: int = 10) -> List[Dict[str, Any]]:
        """샘플 데이터 조회 (간단한 인터페이스)"""
        await self.initialize()
        return await self.service.get_sample_inquiries(limit)
    
    async def save_result(self, result: Dict[str, Any]) -> bool:
        """결과 저장 (간단한 인터페이스)"""
        await self.initialize()
        return await self.service.save_analysis_result(result)
    
    async def get_database_info(self) -> Dict[str, Any]:
        """데이터베이스 정보 조회"""
        await self.initialize()
        
        if hasattr(self.service, 'get_statistics'):
            stats = await self.service.get_statistics()
            return {
                "service_type": type(self.service).__name__,
                "connected": self._initialized,
                "statistics": stats
            }
        else:
            return {
                "service_type": type(self.service).__name__,
                "connected": self._initialized,
                "statistics": {}
            }
