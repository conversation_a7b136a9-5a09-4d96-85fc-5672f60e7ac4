"""
피드백 생성 서비스 - Single Responsibility Principle 적용
"""
from typing import List, Dict, Tuple
from app.core.interfaces import IFeedbackGenerator


class FeedbackGenerator(IFeedbackGenerator):
    """피드백 생성 서비스 - 피드백 생성에만 집중"""
    
    def generate_feedback(
        self, 
        question: str, 
        answer: str, 
        scores: Dict[str, float]
    ) -> Tuple[List[str], List[str], List[str]]:
        """피드백 생성 (강점, 약점, 개선사항)"""
        strengths = self._identify_strengths(question, answer, scores)
        weaknesses = self._identify_weaknesses(question, answer, scores)
        recommendations = self._generate_recommendations(question, answer, scores)
        
        return strengths, weaknesses, recommendations
    
    def _identify_strengths(
        self, 
        question: str, 
        answer: str, 
        scores: Dict[str, float]
    ) -> List[str]:
        """강점 식별"""
        strengths = []
        
        if scores['semantic_similarity'] >= 0.8:
            strengths.append("질문과 답변의 의미적 연관성이 높음")
        
        if scores['topic_relevance'] >= 0.8:
            strengths.append("주제 관련성이 우수함")
        
        if scores['keyword_overlap'] >= 0.7:
            strengths.append("핵심 키워드가 잘 포함됨")
        
        if scores['answer_completeness'] >= 0.8:
            strengths.append("답변이 충분히 상세함")
        
        if scores['answer_accuracy'] >= 0.8:
            strengths.append("답변의 정확성이 높음")
        
        if scores['answer_helpfulness'] >= 0.8:
            strengths.append("고객에게 도움이 되는 답변")
        
        # 특정 표현 분석
        if answer and any(word in answer for word in ["안녕하세요", "감사합니다"]):
            strengths.append("정중하고 친근한 표현 사용")
        
        if answer and any(word in answer for word in ["처리", "해결", "안내"]):
            strengths.append("해결 지향적 답변")
        
        return strengths
    
    def _identify_weaknesses(
        self, 
        question: str, 
        answer: str, 
        scores: Dict[str, float]
    ) -> List[str]:
        """약점 식별"""
        weaknesses = []
        
        if scores['semantic_similarity'] < 0.5:
            weaknesses.append("질문과 답변의 의미적 연관성이 낮음")
        
        if scores['topic_relevance'] < 0.5:
            weaknesses.append("주제 관련성이 부족함")
        
        if scores['keyword_overlap'] < 0.3:
            weaknesses.append("질문의 핵심 키워드가 답변에 부족")
        
        if scores['answer_completeness'] < 0.5:
            weaknesses.append("답변이 너무 간단함")
        
        if scores['answer_accuracy'] < 0.5:
            weaknesses.append("답변의 정확성이 의심됨")
        
        if scores['answer_helpfulness'] < 0.5:
            weaknesses.append("고객에게 실질적 도움이 부족")
        
        if not answer or answer.strip().lower() in ["", "nan", "null"]:
            weaknesses.append("답변이 없음")
        
        return weaknesses
    
    def _generate_recommendations(
        self, 
        question: str, 
        answer: str, 
        scores: Dict[str, float]
    ) -> List[str]:
        """개선사항 제안"""
        recommendations = []
        
        if scores['semantic_similarity'] < 0.6:
            recommendations.append("질문의 핵심 키워드를 답변에 더 많이 포함")
        
        if scores['answer_completeness'] < 0.7:
            recommendations.append("더 상세하고 구체적인 답변 제공")
        
        if scores['answer_helpfulness'] < 0.7:
            recommendations.append("고객이 다음에 취할 수 있는 구체적 행동 안내")
        
        if answer and not any(word in answer for word in ["고객센터", "문의"]):
            recommendations.append("추가 문의 채널 안내 추가")
        
        if answer and not any(word in answer for word in ["안녕하세요", "감사합니다"]):
            recommendations.append("더 정중하고 친근한 표현 사용")
        
        if scores['topic_relevance'] < 0.6:
            recommendations.append("질문의 주제에 더 집중한 답변 작성")
        
        if len(answer) < 50:
            recommendations.append("답변 길이를 늘려 더 충분한 정보 제공")
        
        return recommendations
