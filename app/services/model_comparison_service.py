"""
모델 성능 비교 서비스 - 모델별 성능 데이터 저장 및 분석
"""
import json
import os
from datetime import datetime
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict


@dataclass
class ModelPerformanceResult:
    """모델 성능 결과"""
    model_name: str
    avg_confidence: float
    avg_processing_time: float
    test_count: int
    test_results: List[Dict[str, Any]]
    timestamp: str
    error: Optional[str] = None


@dataclass
class ComparisonSession:
    """비교 세션"""
    session_id: str
    timestamp: str
    test_data_count: int
    models_tested: List[str]
    results: List[ModelPerformanceResult]
    best_accuracy_model: str
    best_speed_model: str
    summary: Dict[str, Any]


class ModelComparisonService:
    """모델 성능 비교 서비스"""
    
    def __init__(self, results_dir: str = "results/model_comparisons"):
        self.results_dir = results_dir
        os.makedirs(results_dir, exist_ok=True)
    
    def save_comparison_results(
        self, 
        results: Dict[str, Any], 
        test_questions: List[str],
        session_id: Optional[str] = None
    ) -> str:
        """모델 비교 결과 저장"""
        if session_id is None:
            session_id = f"comparison_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        timestamp = datetime.now().isoformat()
        
        # 모델별 성능 결과 생성
        performance_results = []
        valid_results = {}
        
        for model_name, model_data in results.items():
            if "error" in model_data:
                performance_result = ModelPerformanceResult(
                    model_name=model_name,
                    avg_confidence=0.0,
                    avg_processing_time=0.0,
                    test_count=0,
                    test_results=[],
                    timestamp=timestamp,
                    error=model_data["error"]
                )
            else:
                performance_result = ModelPerformanceResult(
                    model_name=model_name,
                    avg_confidence=model_data["avg_confidence"],
                    avg_processing_time=model_data["avg_processing_time"],
                    test_count=len(model_data["results"]),
                    test_results=model_data["results"],
                    timestamp=timestamp
                )
                valid_results[model_name] = model_data
            
            performance_results.append(performance_result)
        
        # 최고 성능 모델 찾기
        best_accuracy_model = ""
        best_speed_model = ""
        
        if valid_results:
            # 정확도 최고
            best_accuracy = max(valid_results.items(), 
                              key=lambda x: x[1]["avg_confidence"])
            best_accuracy_model = best_accuracy[0]
            
            # 속도 최고 (낮을수록 좋음)
            best_speed = min(valid_results.items(),
                           key=lambda x: x[1]["avg_processing_time"])
            best_speed_model = best_speed[0]
        
        # 요약 정보
        summary = {
            "total_models": len(results),
            "successful_models": len(valid_results),
            "failed_models": len(results) - len(valid_results),
            "test_data_count": len(test_questions),
            "best_accuracy": {
                "model": best_accuracy_model,
                "confidence": valid_results.get(best_accuracy_model, {}).get("avg_confidence", 0)
            },
            "best_speed": {
                "model": best_speed_model,
                "time_ms": valid_results.get(best_speed_model, {}).get("avg_processing_time", 0)
            }
        }
        
        # 비교 세션 생성
        comparison_session = ComparisonSession(
            session_id=session_id,
            timestamp=timestamp,
            test_data_count=len(test_questions),
            models_tested=list(results.keys()),
            results=performance_results,
            best_accuracy_model=best_accuracy_model,
            best_speed_model=best_speed_model,
            summary=summary
        )
        
        # 파일 저장
        filename = f"{session_id}.json"
        filepath = os.path.join(self.results_dir, filename)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(asdict(comparison_session), f, ensure_ascii=False, indent=2)
        
        # 개별 모델 결과도 저장
        self._save_individual_model_results(performance_results, session_id)
        
        print(f"✅ 모델 비교 결과 저장: {filepath}")
        return session_id
    
    def _save_individual_model_results(
        self, 
        performance_results: List[ModelPerformanceResult], 
        session_id: str
    ):
        """개별 모델 결과 저장"""
        models_dir = os.path.join(self.results_dir, "by_model")
        os.makedirs(models_dir, exist_ok=True)
        
        for result in performance_results:
            model_safe_name = result.model_name.replace("/", "_").replace(":", "_")
            filename = f"{model_safe_name}_{session_id}.json"
            filepath = os.path.join(models_dir, filename)
            
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(asdict(result), f, ensure_ascii=False, indent=2)
    
    def load_comparison_results(self, session_id: str) -> Optional[ComparisonSession]:
        """비교 결과 로드"""
        filepath = os.path.join(self.results_dir, f"{session_id}.json")
        
        if not os.path.exists(filepath):
            return None
        
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # ComparisonSession 객체로 변환
            results = []
            for result_data in data["results"]:
                results.append(ModelPerformanceResult(**result_data))
            
            data["results"] = results
            return ComparisonSession(**data)
            
        except Exception as e:
            print(f"❌ 비교 결과 로드 실패: {e}")
            return None
    
    def list_comparison_sessions(self) -> List[Dict[str, Any]]:
        """비교 세션 목록"""
        sessions = []
        
        if not os.path.exists(self.results_dir):
            return sessions
        
        for filename in os.listdir(self.results_dir):
            if filename.endswith('.json') and not filename.startswith('by_model'):
                filepath = os.path.join(self.results_dir, filename)
                try:
                    with open(filepath, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    
                    sessions.append({
                        "session_id": data["session_id"],
                        "timestamp": data["timestamp"],
                        "models_count": len(data["models_tested"]),
                        "test_data_count": data["test_data_count"],
                        "best_accuracy_model": data["best_accuracy_model"],
                        "best_speed_model": data["best_speed_model"]
                    })
                except Exception:
                    continue
        
        # 시간순 정렬 (최신순)
        sessions.sort(key=lambda x: x["timestamp"], reverse=True)
        return sessions
    
    def get_model_performance_history(self, model_name: str) -> List[Dict[str, Any]]:
        """특정 모델의 성능 히스토리"""
        history = []
        models_dir = os.path.join(self.results_dir, "by_model")
        
        if not os.path.exists(models_dir):
            return history
        
        model_safe_name = model_name.replace("/", "_").replace(":", "_")
        
        for filename in os.listdir(models_dir):
            if filename.startswith(model_safe_name):
                filepath = os.path.join(models_dir, filename)
                try:
                    with open(filepath, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    
                    history.append({
                        "timestamp": data["timestamp"],
                        "avg_confidence": data["avg_confidence"],
                        "avg_processing_time": data["avg_processing_time"],
                        "test_count": data["test_count"],
                        "error": data.get("error")
                    })
                except Exception:
                    continue
        
        # 시간순 정렬
        history.sort(key=lambda x: x["timestamp"])
        return history
    
    def generate_comparison_report(self, session_id: str) -> str:
        """비교 보고서 생성"""
        session = self.load_comparison_results(session_id)
        if not session:
            return "❌ 세션을 찾을 수 없습니다."
        
        report = []
        report.append(f"📊 모델 성능 비교 보고서")
        report.append(f"=" * 50)
        report.append(f"세션 ID: {session.session_id}")
        report.append(f"분석 시간: {session.timestamp}")
        report.append(f"테스트 데이터: {session.test_data_count}건")
        report.append(f"테스트 모델: {len(session.models_tested)}개")
        report.append("")
        
        report.append(f"🏆 최고 성능:")
        report.append(f"  정확도: {session.best_accuracy_model} ({session.summary['best_accuracy']['confidence']:.3f})")
        report.append(f"  속도: {session.best_speed_model} ({session.summary['best_speed']['time_ms']:.2f}ms)")
        report.append("")
        
        report.append(f"📋 상세 결과:")
        for result in session.results:
            if result.error:
                report.append(f"  ❌ {result.model_name}: {result.error}")
            else:
                report.append(f"  ✅ {result.model_name}:")
                report.append(f"     신뢰도: {result.avg_confidence:.3f}")
                report.append(f"     처리시간: {result.avg_processing_time:.2f}ms")
                report.append(f"     테스트: {result.test_count}건")
        
        return "\n".join(report)
