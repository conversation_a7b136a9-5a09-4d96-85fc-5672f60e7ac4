"""
서비스 팩토리 - 간소화된 버전
"""
from typing import Dict, Any, Optional
from app.models.embedding import EmbeddingModel
from app.config.manager import ConfigManager


class ServiceFactory:
    """
    서비스 팩토리 - 간소화된 버전

    특징:
    - Dependency Injection Container
    - Singleton 패턴 적용
    - 설정 기반 서비스 생성
    """

    _instance = None
    _services: Dict[str, Any] = {}

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(ServiceFactory, cls).__new__(cls)
            cls._instance._initialized = False
        return cls._instance

    def __init__(self):
        if not hasattr(self, '_initialized') or not self._initialized:
            self.config_manager = ConfigManager()
            self._initialized = True

    def get_embedding_model(self, model_name: Optional[str] = None) -> EmbeddingModel:
        """임베딩/분류 모델 생성/조회 (동일 모델 사용)"""
        if model_name is None:
            profile_info = self.config_manager.get_profile_info()
            # 임베딩과 분류에 동일한 모델 사용
            model_name = profile_info.get('embedding_model', 'xlm-roberta-base')

        service_key = f"unified_model_{model_name}"

        if service_key not in self._services:
            self._services[service_key] = EmbeddingModel(model_name)

        return self._services[service_key]

    def get_classification_model(self, model_name: Optional[str] = None) -> EmbeddingModel:
        """분류 모델 조회 (임베딩 모델과 동일)"""
        # 임베딩과 분류는 동일한 모델 사용
        return self.get_embedding_model(model_name)

    def get_config_manager(self) -> ConfigManager:
        """설정 관리자 조회"""
        return self.config_manager

    def clear_cache(self):
        """서비스 캐시 초기화"""
        self._services.clear()

    def get_service_info(self) -> Dict[str, Any]:
        """서비스 정보 조회"""
        return {
            "cached_services": list(self._services.keys()),
            "current_profile": self.config_manager.get_current_profile(),
            "factory_initialized": self._initialized
        }
