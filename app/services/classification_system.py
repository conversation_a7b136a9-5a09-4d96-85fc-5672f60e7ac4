"""
통합 분류 시스템 - 질문-답변 종합 분류
"""
import asyncio
from typing import Dict, List, Any, Optional
from datetime import datetime

from app.core.interfaces import IClassificationAgent, ISentimentAgent, IEmbeddingModel
from app.agents.classification_agent import ClassificationAgent
from app.agents.sentiment_agent import SentimentAgent


class UnifiedClassificationSystem:
    """
    통합 분류 시스템
    
    기능:
    - 카테고리 분류 (배송, 환불, 상품문의 등)
    - 감정 분석 (긍정, 중립, 부정, 분노)
    - 긴급도 분석 (낮음, 보통, 높음, 긴급)
    - 우선순위 결정
    - 고객 만족도 예측
    - 자동 라우팅 추천
    """
    
    def __init__(self, embedding_model: IEmbeddingModel):
        self.classification_agent = ClassificationAgent(embedding_model)
        self.sentiment_agent = SentimentAgent()
        
        # 우선순위 가중치
        self.priority_weights = {
            "urgency": 0.4,      # 긴급도 40%
            "sentiment": 0.3,    # 감정 30%
            "category": 0.2,     # 카테고리 20%
            "satisfaction": 0.1  # 만족도 10%
        }
        
        # 카테고리별 기본 우선순위
        self.category_priority = {
            "환불문의": 0.8,
            "배송문의": 0.6,
            "결제문의": 0.7,
            "상품문의": 0.4,
            "회원문의": 0.3,
            "쿠폰할인": 0.2,
            "기타문의": 0.1
        }
        
        print("✅ 통합 분류 시스템 초기화 완료")
    
    async def classify_inquiry(
        self, 
        question: str, 
        answer: str = None,
        customer_id: str = None
    ) -> Dict[str, Any]:
        """문의 종합 분류"""
        
        classification_result = {
            "inquiry_id": f"inq_{int(datetime.now().timestamp())}",
            "customer_id": customer_id,
            "timestamp": datetime.now().isoformat(),
            "question": question,
            "answer": answer,
            "classification": {},
            "sentiment_analysis": {},
            "urgency_analysis": {},
            "priority_score": 0.0,
            "priority_level": "보통",
            "routing_recommendation": {},
            "action_items": []
        }
        
        # 1. 카테고리 분류
        print("🔍 카테고리 분류 중...")
        category_result = await self.classification_agent.classify_inquiry(question)
        classification_result["classification"] = category_result
        
        # 2. 감정 분석
        print("😊 감정 분석 중...")
        sentiment_result = await self.sentiment_agent.analyze_sentiment(question)
        classification_result["sentiment_analysis"] = sentiment_result
        
        # 3. 긴급도 분석
        print("⚡ 긴급도 분석 중...")
        urgency_result = await self.sentiment_agent.analyze_urgency(question)
        classification_result["urgency_analysis"] = urgency_result
        
        # 4. 고객 만족도 예측 (답변이 있는 경우)
        if answer:
            print("📊 고객 만족도 예측 중...")
            satisfaction_result = await self.sentiment_agent.analyze_customer_satisfaction(question, answer)
            classification_result["satisfaction_prediction"] = satisfaction_result
        
        # 5. 우선순위 계산
        priority_score, priority_level = self._calculate_priority(
            category_result, sentiment_result, urgency_result, 
            classification_result.get("satisfaction_prediction")
        )
        classification_result["priority_score"] = priority_score
        classification_result["priority_level"] = priority_level
        
        # 6. 라우팅 추천
        routing_rec = self._generate_routing_recommendation(classification_result)
        classification_result["routing_recommendation"] = routing_rec
        
        # 7. 액션 아이템 생성
        action_items = self._generate_action_items(classification_result)
        classification_result["action_items"] = action_items
        
        return classification_result
    
    async def classify_batch(
        self, 
        inquiries: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """배치 분류"""
        
        print(f"📊 {len(inquiries)}건의 문의 배치 분류 시작...")
        
        results = []
        for i, inquiry in enumerate(inquiries, 1):
            print(f"  처리 중: {i}/{len(inquiries)}")
            
            question = inquiry.get("question", "")
            answer = inquiry.get("answer", "")
            customer_id = inquiry.get("customer_id")
            
            result = await self.classify_inquiry(question, answer, customer_id)
            
            # 원본 데이터 추가
            result["original_data"] = inquiry
            results.append(result)
        
        print("✅ 배치 분류 완료")
        return results
    
    def generate_classification_summary(
        self, 
        batch_results: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """분류 결과 요약"""
        
        if not batch_results:
            return {"error": "분류 결과가 없습니다"}
        
        summary = {
            "total_inquiries": len(batch_results),
            "timestamp": datetime.now().isoformat(),
            "category_distribution": {},
            "sentiment_distribution": {},
            "urgency_distribution": {},
            "priority_distribution": {},
            "routing_distribution": {},
            "insights": []
        }
        
        # 카테고리 분포
        for result in batch_results:
            category = result.get("classification", {}).get("category", "기타")
            summary["category_distribution"][category] = summary["category_distribution"].get(category, 0) + 1
        
        # 감정 분포
        for result in batch_results:
            sentiment = result.get("sentiment_analysis", {}).get("sentiment", "중립")
            summary["sentiment_distribution"][sentiment] = summary["sentiment_distribution"].get(sentiment, 0) + 1
        
        # 긴급도 분포
        for result in batch_results:
            urgency = result.get("urgency_analysis", {}).get("urgency", "보통")
            summary["urgency_distribution"][urgency] = summary["urgency_distribution"].get(urgency, 0) + 1
        
        # 우선순위 분포
        for result in batch_results:
            priority = result.get("priority_level", "보통")
            summary["priority_distribution"][priority] = summary["priority_distribution"].get(priority, 0) + 1
        
        # 라우팅 분포
        for result in batch_results:
            department = result.get("routing_recommendation", {}).get("department", "일반상담")
            summary["routing_distribution"][department] = summary["routing_distribution"].get(department, 0) + 1
        
        # 인사이트 생성
        summary["insights"] = self._generate_insights(summary, batch_results)
        
        return summary
    
    def _calculate_priority(
        self, 
        category_result: Dict[str, Any], 
        sentiment_result: Dict[str, Any], 
        urgency_result: Dict[str, Any],
        satisfaction_result: Optional[Dict[str, Any]] = None
    ) -> tuple[float, str]:
        """우선순위 계산"""
        
        # 긴급도 점수 (0-1)
        urgency_mapping = {"낮음": 0.1, "보통": 0.4, "높음": 0.7, "긴급": 1.0}
        urgency_score = urgency_mapping.get(urgency_result.get("urgency", "보통"), 0.4)
        
        # 감정 점수 (부정적일수록 높은 우선순위)
        sentiment_mapping = {"긍정": 0.2, "중립": 0.4, "부정": 0.7, "분노": 1.0}
        sentiment_score = sentiment_mapping.get(sentiment_result.get("sentiment", "중립"), 0.4)
        
        # 카테고리 점수
        category = category_result.get("category", "기타문의")
        category_score = self.category_priority.get(category, 0.1)
        
        # 만족도 점수 (낮을수록 높은 우선순위)
        satisfaction_score = 0.5  # 기본값
        if satisfaction_result:
            predicted_satisfaction = satisfaction_result.get("predicted_satisfaction", 0.5)
            satisfaction_score = 1.0 - predicted_satisfaction  # 역수
        
        # 가중 평균으로 최종 우선순위 계산
        priority_score = (
            urgency_score * self.priority_weights["urgency"] +
            sentiment_score * self.priority_weights["sentiment"] +
            category_score * self.priority_weights["category"] +
            satisfaction_score * self.priority_weights["satisfaction"]
        )
        
        # 우선순위 레벨 결정
        if priority_score >= 0.8:
            priority_level = "긴급"
        elif priority_score >= 0.6:
            priority_level = "높음"
        elif priority_score >= 0.4:
            priority_level = "보통"
        else:
            priority_level = "낮음"
        
        return priority_score, priority_level
    
    def _generate_routing_recommendation(self, classification_result: Dict[str, Any]) -> Dict[str, Any]:
        """라우팅 추천 생성"""
        
        category = classification_result.get("classification", {}).get("category", "기타문의")
        urgency = classification_result.get("urgency_analysis", {}).get("urgency", "보통")
        sentiment = classification_result.get("sentiment_analysis", {}).get("sentiment", "중립")
        priority_level = classification_result.get("priority_level", "보통")
        
        # 부서 매핑
        department_mapping = {
            "배송문의": "물류팀",
            "환불문의": "환불팀",
            "결제문의": "결제팀",
            "상품문의": "상품팀",
            "회원문의": "회원팀",
            "쿠폰할인": "마케팅팀",
            "기타문의": "일반상담"
        }
        
        department = department_mapping.get(category, "일반상담")
        
        # 특수 케이스 처리
        if urgency == "긴급" or sentiment == "분노":
            department = "고급상담팀"
            escalation = True
        elif priority_level == "높음":
            escalation = True
        else:
            escalation = False
        
        # 예상 처리 시간
        processing_time_mapping = {
            "긴급": "즉시",
            "높음": "1시간 이내",
            "보통": "4시간 이내",
            "낮음": "24시간 이내"
        }
        
        expected_response_time = processing_time_mapping.get(priority_level, "4시간 이내")
        
        return {
            "department": department,
            "escalation_required": escalation,
            "expected_response_time": expected_response_time,
            "routing_reason": f"{category} + {urgency} 긴급도 + {sentiment} 감정",
            "special_instructions": self._get_special_instructions(classification_result)
        }
    
    def _generate_action_items(self, classification_result: Dict[str, Any]) -> List[str]:
        """액션 아이템 생성"""
        
        action_items = []
        
        category = classification_result.get("classification", {}).get("category", "기타문의")
        urgency = classification_result.get("urgency_analysis", {}).get("urgency", "보통")
        sentiment = classification_result.get("sentiment_analysis", {}).get("sentiment", "중립")
        priority_level = classification_result.get("priority_level", "보통")
        
        # 긴급도 기반 액션
        if urgency == "긴급":
            action_items.append("즉시 담당자 배정 필요")
            action_items.append("1시간 이내 1차 응답 완료")
        elif urgency == "높음":
            action_items.append("우선 처리 대상으로 분류")
            action_items.append("4시간 이내 응답 완료")
        
        # 감정 기반 액션
        if sentiment == "분노":
            action_items.append("고급 상담사 배정")
            action_items.append("감정 완화 우선 대응")
            action_items.append("관리자 검토 필요")
        elif sentiment == "부정":
            action_items.append("정중한 사과 및 해결책 제시")
            action_items.append("추가 보상 검토")
        
        # 카테고리 기반 액션
        if category == "환불문의":
            action_items.append("환불 정책 안내")
            action_items.append("주문 내역 확인")
        elif category == "배송문의":
            action_items.append("배송 상태 확인")
            action_items.append("운송장 번호 제공")
        elif category == "결제문의":
            action_items.append("결제 내역 확인")
            action_items.append("결제 수단 검증")
        
        # 우선순위 기반 액션
        if priority_level == "긴급":
            action_items.append("에스컬레이션 프로세스 시작")
            action_items.append("실시간 모니터링")
        
        return action_items
    
    def _get_special_instructions(self, classification_result: Dict[str, Any]) -> List[str]:
        """특별 지시사항 생성"""
        
        instructions = []
        
        sentiment = classification_result.get("sentiment_analysis", {}).get("sentiment", "중립")
        urgency = classification_result.get("urgency_analysis", {}).get("urgency", "보통")
        
        if sentiment == "분노":
            instructions.append("고객 감정 진정 우선")
            instructions.append("공감적 언어 사용")
            instructions.append("즉시 해결책 제시")
        
        if urgency == "긴급":
            instructions.append("신속 처리 필수")
            instructions.append("진행 상황 실시간 업데이트")
        
        return instructions
    
    def _generate_insights(
        self, 
        summary: Dict[str, Any], 
        batch_results: List[Dict[str, Any]]
    ) -> List[str]:
        """인사이트 생성"""
        
        insights = []
        total = summary["total_inquiries"]
        
        # 카테고리 인사이트
        category_dist = summary["category_distribution"]
        if category_dist:
            top_category = max(category_dist.keys(), key=lambda k: category_dist[k])
            top_ratio = (category_dist[top_category] / total) * 100
            insights.append(f"가장 많은 문의 유형: {top_category} ({top_ratio:.1f}%)")
        
        # 감정 인사이트
        sentiment_dist = summary["sentiment_distribution"]
        negative_count = sentiment_dist.get("부정", 0) + sentiment_dist.get("분노", 0)
        if negative_count > 0:
            negative_ratio = (negative_count / total) * 100
            insights.append(f"부정적 감정 문의: {negative_ratio:.1f}% (주의 필요)")
        
        # 긴급도 인사이트
        urgency_dist = summary["urgency_distribution"]
        urgent_count = urgency_dist.get("긴급", 0) + urgency_dist.get("높음", 0)
        if urgent_count > 0:
            urgent_ratio = (urgent_count / total) * 100
            insights.append(f"긴급 처리 필요 문의: {urgent_ratio:.1f}%")
        
        # 우선순위 인사이트
        priority_dist = summary["priority_distribution"]
        high_priority = priority_dist.get("긴급", 0) + priority_dist.get("높음", 0)
        if high_priority > total * 0.3:  # 30% 이상이 높은 우선순위
            insights.append("높은 우선순위 문의가 많음 - 리소스 증대 검토 필요")
        
        return insights
    
    def get_system_info(self) -> Dict[str, Any]:
        """시스템 정보"""
        return {
            "system_type": "Unified Classification System",
            "version": "1.0.0",
            "capabilities": [
                "카테고리 분류",
                "감정 분석",
                "긴급도 분석",
                "우선순위 계산",
                "라우팅 추천",
                "액션 아이템 생성"
            ],
            "categories": self.classification_agent.get_categories(),
            "priority_weights": self.priority_weights,
            "agents": {
                "classification": self.classification_agent.get_agent_info(),
                "sentiment": self.sentiment_agent.get_agent_info()
            }
        }
