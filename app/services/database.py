"""
데이터베이스 서비스 - Supabase 직접 연동 (Interface Segregation Principle 적용)
"""
from typing import List, Dict, Any, Optional

from app.core.interfaces import IDatabaseService


class DatabaseService(IDatabaseService):
    """데이터베이스 서비스 - playauto_qna 테이블 직접 연결"""

    def __init__(self, supabase_url: str = None, supabase_key: str = None):
        self.supabase_url = supabase_url
        self.supabase_key = supabase_key
        self._client = None
        self._is_connected = False

    async def connect(self) -> bool:
        """DB 연결"""
        if not self.supabase_url or not self.supabase_key:
            print("⚠️ DB 연결 정보가 없습니다")
            return False

        try:
            from supabase import create_client
            self._client = create_client(self.supabase_url, self.supabase_key)

            # 연결 테스트
            self._client.table('playauto_qna').select('id').limit(1).execute()

            self._is_connected = True
            print("✅ DB 서비스 연결 완료 (playauto_qna)")
            return True
        except Exception as e:
            print(f"⚠️ DB 연결 실패: {e}")
            return False
    
    async def get_inquiry_by_id(self, inquiry_id: str) -> Optional[Dict[str, Any]]:
        """ID로 문의 조회 - playauto_qna 테이블"""
        if not self._is_connected:
            await self.connect()

        if not self._client:
            return None

        try:
            result = self._client.table('playauto_qna').select('*').eq('id', inquiry_id).execute()

            if result.data and len(result.data) > 0:
                raw_data = result.data[0]
                return self._transform_playauto_data(raw_data)

            return None

        except Exception as e:
            print(f"⚠️ 문의 조회 실패: {e}")
            return None

    async def get_sample_inquiries(self, limit: int = 20) -> List[Dict[str, Any]]:
        """샘플 문의 목록 조회 - playauto_qna 테이블"""
        if not self._is_connected:
            await self.connect()

        if not self._client:
            return []

        try:
            print(f"📊 playauto_qna에서 {limit}건 조회 중...")

            # playauto_qna 테이블에서 데이터 조회
            result = self._client.table('playauto_qna').select('*').limit(limit).execute()

            if result.data:
                print(f"✅ {len(result.data)}건 조회 완료")
                # playauto_qna 데이터 변환
                transformed_data = [self._transform_playauto_data(item) for item in result.data]
                return transformed_data
            else:
                print("⚠️ 조회된 데이터가 없습니다")
                return []

        except Exception as e:
            print(f"⚠️ 샘플 문의 조회 실패: {e}")
            return []

    def is_connected(self) -> bool:
        """연결 상태 확인"""
        return self._is_connected

    def _transform_playauto_data(self, raw_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        playauto_qna 테이블 데이터를 표준 형식으로 변환

        playauto_qna 테이블 구조:
        - id (uuid): 고유 ID
        - document (text): 문서 정보
        - company_code (text): 회사 코드
        - mall_name (text): 쇼핑몰 이름
        - inquiry_at (text): 문의 시간
        - answered_at (text): 답변 시간
        - inquiry_content (text): 문의 내용
        - answer_content (text): 답변 내용
        """

        # 기본 변환
        transformed = {
            "id": raw_data.get("id"),
            "document": raw_data.get("document"),
            "company_code": raw_data.get("company_code"),
            "mall_name": raw_data.get("mall_name"),
            "inquiry_at": raw_data.get("inquiry_at"),
            "answered_at": raw_data.get("answered_at"),
            "inquiry_content": raw_data.get("inquiry_content"),
            "answer_content": raw_data.get("answer_content")
        }

        # 표준 필드 추가 (기존 시스템과의 호환성)
        transformed.update({
            "question": raw_data.get("inquiry_content", ""),
            "answer": raw_data.get("answer_content", ""),
            "subject": "",  # playauto_qna에는 제목 필드가 없음
            "content": raw_data.get("inquiry_content", ""),
            "created_at": raw_data.get("inquiry_at"),
            "updated_at": raw_data.get("answered_at")
        })

        # 메타데이터 추가
        transformed["metadata"] = {
            "source_table": "playauto_qna",
            "company_code": raw_data.get("company_code"),
            "mall_name": raw_data.get("mall_name"),
            "document": raw_data.get("document")
        }

        return transformed

    async def get_inquiries_by_company(self, company_code: str, limit: int = 50) -> List[Dict[str, Any]]:
        """회사 코드별 문의 조회"""
        if not self._is_connected:
            await self.connect()

        if not self._client:
            return []

        try:
            print(f"📊 회사 {company_code}의 문의 {limit}건 조회 중...")

            result = self._client.table('playauto_qna').select('*').eq('company_code', company_code).limit(limit).execute()

            if result.data:
                print(f"✅ {len(result.data)}건 조회 완료")
                transformed_data = [self._transform_playauto_data(item) for item in result.data]
                return transformed_data
            else:
                print("⚠️ 조회된 데이터가 없습니다")
                return []

        except Exception as e:
            print(f"⚠️ 회사별 문의 조회 실패: {e}")
            return []

    async def get_inquiries_by_mall(self, mall_name: str, limit: int = 50) -> List[Dict[str, Any]]:
        """쇼핑몰별 문의 조회"""
        if not self._is_connected:
            await self.connect()

        if not self._client:
            return []

        try:
            print(f"📊 쇼핑몰 {mall_name}의 문의 {limit}건 조회 중...")

            result = self._client.table('playauto_qna').select('*').eq('mall_name', mall_name).limit(limit).execute()

            if result.data:
                print(f"✅ {len(result.data)}건 조회 완료")
                transformed_data = [self._transform_playauto_data(item) for item in result.data]
                return transformed_data
            else:
                print("⚠️ 조회된 데이터가 없습니다")
                return []

        except Exception as e:
            print(f"⚠️ 쇼핑몰별 문의 조회 실패: {e}")
            return []

    async def get_unanswered_inquiries(self, limit: int = 50) -> List[Dict[str, Any]]:
        """미답변 문의 조회"""
        if not self._is_connected:
            await self.connect()

        if not self._client:
            return []

        try:
            print(f"📊 미답변 문의 {limit}건 조회 중...")

            # answer_content가 null이거나 빈 문자열인 경우
            result = self._client.table('playauto_qna').select('*').or_('answer_content.is.null,answer_content.eq.').limit(limit).execute()

            if result.data:
                print(f"✅ {len(result.data)}건 조회 완료")
                transformed_data = [self._transform_playauto_data(item) for item in result.data]
                return transformed_data
            else:
                print("⚠️ 조회된 데이터가 없습니다")
                return []

        except Exception as e:
            print(f"⚠️ 미답변 문의 조회 실패: {e}")
            return []

    async def get_recent_inquiries(self, days: int = 7, limit: int = 100) -> List[Dict[str, Any]]:
        """최근 문의 조회"""
        if not self._is_connected:
            await self.connect()

        if not self._client:
            return []

        try:
            from datetime import datetime, timedelta

            # 최근 N일 계산
            cutoff_date = (datetime.now() - timedelta(days=days)).isoformat()

            print(f"📊 최근 {days}일간 문의 {limit}건 조회 중...")

            result = self._client.table('playauto_qna').select('*').gte('inquiry_at', cutoff_date).limit(limit).execute()

            if result.data:
                print(f"✅ {len(result.data)}건 조회 완료")
                transformed_data = [self._transform_playauto_data(item) for item in result.data]
                return transformed_data
            else:
                print("⚠️ 조회된 데이터가 없습니다")
                return []

        except Exception as e:
            print(f"⚠️ 최근 문의 조회 실패: {e}")
            return []

    def get_table_stats(self) -> Dict[str, Any]:
        """테이블 통계 정보"""
        if not self._is_connected:
            return {"error": "데이터베이스에 연결되지 않았습니다"}

        try:
            # 전체 레코드 수
            total_result = self._client.table('playauto_qna').select('id', count='exact').execute()
            total_count = total_result.count if hasattr(total_result, 'count') else 0

            # 답변된 문의 수
            answered_result = self._client.table('playauto_qna').select('id', count='exact').not_.is_('answer_content', 'null').execute()
            answered_count = answered_result.count if hasattr(answered_result, 'count') else 0

            # 미답변 문의 수
            unanswered_count = total_count - answered_count

            return {
                "table_name": "playauto_qna",
                "total_inquiries": total_count,
                "answered_inquiries": answered_count,
                "unanswered_inquiries": unanswered_count,
                "answer_rate": (answered_count / total_count * 100) if total_count > 0 else 0
            }

        except Exception as e:
            print(f"⚠️ 테이블 통계 조회 실패: {e}")
            return {"error": str(e)}
