"""
2단계 하이브리드 분류 파이프라인
1차: 임베딩 기반 유사도 필터링 (고속)
2차: 세부 분류 모델 (고정밀)
"""
import time
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime

from app.core.interfaces import IEmbeddingModel, IClassificationAgent
from app.agents.classification_agent import ClassificationAgent
from app.services.service_factory import ServiceFactory


class HybridClassificationPipeline:
    """
    2단계 하이브리드 분류 파이프라인
    
    1차 필터링: KR-SBERT 임베딩 기반 유사도로 대량 텍스트 1차 분류
    2차 정밀 분류: 고성능 모델로 세부 분류 (필터링된 샘플만)
    
    장점:
    - 대용량 처리 시 효율성 극대화
    - 정확도와 속도의 균형
    - 비용 효율적 운영
    """
    
    def __init__(self, service_factory: ServiceFactory):
        self.service_factory = service_factory
        
        # 1차 필터링용 임베딩 모델 (고속)
        self.fast_embedding_model = None
        
        # 2차 분류용 모델 (고정밀)
        self.precision_model = None
        
        # 파이프라인 설정
        self.config = {
            "stage1_threshold": 0.7,  # 1차 필터링 임계값
            "stage1_model": "kr_sbert_default",  # 1차용 모델
            "stage2_model": "beomi_kcelectra",   # 2차용 모델 (정확도 우수)
            "batch_size": 100,  # 배치 크기
            "enable_caching": True,  # 캐싱 활성화
            "fallback_enabled": True  # 폴백 활성화
        }
        
        # 캐시
        self.embedding_cache = {}
        self.classification_cache = {}
        
        # 통계
        self.stats = {
            "total_processed": 0,
            "stage1_filtered": 0,
            "stage2_processed": 0,
            "cache_hits": 0,
            "processing_times": {
                "stage1_avg": 0.0,
                "stage2_avg": 0.0,
                "total_avg": 0.0
            }
        }
        
        print("✅ 하이브리드 분류 파이프라인 초기화 완료")
    
    async def classify_single(self, text: str, use_hybrid: bool = True) -> Dict[str, Any]:
        """단일 텍스트 분류"""
        
        start_time = time.time()
        
        if not use_hybrid:
            # 직접 2차 모델 사용
            return await self._stage2_classify(text)
        
        # 1차 필터링
        stage1_result = await self._stage1_filter(text)
        
        # 임계값 확인
        if stage1_result["confidence"] >= self.config["stage1_threshold"]:
            # 1차 결과로 충분
            result = {
                **stage1_result,
                "pipeline_stage": "stage1_only",
                "processing_time_ms": (time.time() - start_time) * 1000
            }
        else:
            # 2차 정밀 분류 필요
            stage2_result = await self._stage2_classify(text)
            result = {
                **stage2_result,
                "stage1_result": stage1_result,
                "pipeline_stage": "stage1_and_stage2",
                "processing_time_ms": (time.time() - start_time) * 1000
            }
            self.stats["stage2_processed"] += 1
        
        self.stats["total_processed"] += 1
        return result
    
    async def classify_batch(
        self, 
        texts: List[str], 
        use_hybrid: bool = True,
        progress_callback: Optional[callable] = None
    ) -> List[Dict[str, Any]]:
        """배치 분류 - 대용량 처리 최적화"""
        
        if not use_hybrid:
            # 직접 2차 모델로 모든 텍스트 처리
            return await self._batch_stage2_classify(texts, progress_callback)
        
        print(f"🔍 하이브리드 파이프라인으로 {len(texts)}건 처리 시작...")
        
        results = []
        stage1_times = []
        stage2_times = []
        
        # 1차 필터링 (전체 배치)
        print("  📊 1차 임베딩 기반 필터링...")
        stage1_start = time.time()
        
        stage1_results = await self._batch_stage1_filter(texts)
        stage1_time = time.time() - stage1_start
        stage1_times.append(stage1_time)
        
        # 2차 처리 대상 선별
        stage2_candidates = []
        stage2_indices = []
        
        for i, (text, stage1_result) in enumerate(zip(texts, stage1_results)):
            if stage1_result["confidence"] >= self.config["stage1_threshold"]:
                # 1차 결과로 충분
                results.append({
                    **stage1_result,
                    "pipeline_stage": "stage1_only",
                    "text_index": i
                })
            else:
                # 2차 처리 필요
                stage2_candidates.append(text)
                stage2_indices.append(i)
                results.append(None)  # 나중에 채울 자리
        
        self.stats["stage1_filtered"] = len(texts) - len(stage2_candidates)
        
        # 2차 정밀 분류 (필터링된 샘플만)
        if stage2_candidates:
            print(f"  🎯 2차 정밀 분류 ({len(stage2_candidates)}건)...")
            stage2_start = time.time()
            
            stage2_results = await self._batch_stage2_classify(
                stage2_candidates, progress_callback
            )
            
            stage2_time = time.time() - stage2_start
            stage2_times.append(stage2_time)
            
            # 결과 병합
            for idx, stage2_result in zip(stage2_indices, stage2_results):
                results[idx] = {
                    **stage2_result,
                    "stage1_result": stage1_results[idx],
                    "pipeline_stage": "stage1_and_stage2",
                    "text_index": idx
                }
            
            self.stats["stage2_processed"] += len(stage2_candidates)
        
        # 통계 업데이트
        self.stats["total_processed"] += len(texts)
        if stage1_times:
            self.stats["processing_times"]["stage1_avg"] = sum(stage1_times) / len(stage1_times)
        if stage2_times:
            self.stats["processing_times"]["stage2_avg"] = sum(stage2_times) / len(stage2_times)
        
        efficiency = (self.stats["stage1_filtered"] / len(texts)) * 100
        print(f"✅ 하이브리드 처리 완료 - 1차 필터링 효율: {efficiency:.1f}%")
        
        return results
    
    async def _stage1_filter(self, text: str) -> Dict[str, Any]:
        """1차 임베딩 기반 필터링"""
        
        # 캐시 확인
        cache_key = f"stage1_{hash(text)}"
        if self.config["enable_caching"] and cache_key in self.embedding_cache:
            self.stats["cache_hits"] += 1
            return self.embedding_cache[cache_key]
        
        # 임베딩 모델 준비
        if not self.fast_embedding_model:
            self.service_factory.switch_embedding_model(self.config["stage1_model"])
            self.fast_embedding_model = self.service_factory.get_embedding_model()
        
        # 카테고리별 대표 임베딩과 유사도 계산
        category_similarities = await self._calculate_category_similarities(text)
        
        # 최고 유사도 카테고리 선택
        best_category = max(category_similarities.keys(), 
                           key=lambda k: category_similarities[k])
        best_confidence = category_similarities[best_category]
        
        result = {
            "category": best_category,
            "confidence": best_confidence,
            "method": "embedding_similarity",
            "all_similarities": category_similarities
        }
        
        # 캐시 저장
        if self.config["enable_caching"]:
            self.embedding_cache[cache_key] = result
        
        return result
    
    async def _stage2_classify(self, text: str) -> Dict[str, Any]:
        """2차 정밀 분류"""
        
        # 캐시 확인
        cache_key = f"stage2_{hash(text)}"
        if self.config["enable_caching"] and cache_key in self.classification_cache:
            self.stats["cache_hits"] += 1
            return self.classification_cache[cache_key]
        
        # 정밀 분류 모델 준비
        if not self.precision_model:
            self.service_factory.switch_embedding_model(self.config["stage2_model"])
            embedding_model = self.service_factory.get_embedding_model()
            self.precision_model = ClassificationAgent(embedding_model)
        
        # 정밀 분류 실행
        result = await self.precision_model.classify_inquiry(text)
        result["method"] = "precision_classification"
        
        # 캐시 저장
        if self.config["enable_caching"]:
            self.classification_cache[cache_key] = result
        
        return result
    
    async def _batch_stage1_filter(self, texts: List[str]) -> List[Dict[str, Any]]:
        """배치 1차 필터링"""
        
        results = []
        for text in texts:
            result = await self._stage1_filter(text)
            results.append(result)
        
        return results
    
    async def _batch_stage2_classify(
        self, 
        texts: List[str], 
        progress_callback: Optional[callable] = None
    ) -> List[Dict[str, Any]]:
        """배치 2차 분류"""
        
        results = []
        total = len(texts)
        
        for i, text in enumerate(texts):
            result = await self._stage2_classify(text)
            results.append(result)
            
            if progress_callback and (i + 1) % 10 == 0:
                progress_callback(i + 1, total)
        
        return results
    
    async def _calculate_category_similarities(self, text: str) -> Dict[str, float]:
        """카테고리별 유사도 계산"""
        
        # 카테고리별 대표 문장들 (실제로는 학습된 임베딩 사용)
        category_examples = {
            "배송문의": "배송 언제 오나요? 택배 추적하고 싶어요.",
            "환불문의": "환불 가능한가요? 취소하고 싶습니다.",
            "상품문의": "상품 사이즈가 어떻게 되나요? 색상 확인 부탁드려요.",
            "결제문의": "결제가 안됩니다. 카드 오류가 나요.",
            "회원문의": "로그인이 안됩니다. 비밀번호를 찾고 싶어요.",
            "쿠폰할인": "쿠폰 사용하고 싶어요. 할인 적용 안되나요?",
            "기타문의": "문의드립니다. 도움이 필요해요."
        }
        
        text_embedding = self.fast_embedding_model.encode(text)
        similarities = {}
        
        for category, example in category_examples.items():
            example_embedding = self.fast_embedding_model.encode(example)
            similarity = self._cosine_similarity(text_embedding, example_embedding)
            similarities[category] = max(0.0, similarity)
        
        return similarities
    
    def _cosine_similarity(self, vec1: List[float], vec2: List[float]) -> float:
        """코사인 유사도 계산"""
        if not vec1 or not vec2 or len(vec1) != len(vec2):
            return 0.0
        
        dot_product = sum(a * b for a, b in zip(vec1, vec2))
        magnitude1 = sum(a * a for a in vec1) ** 0.5
        magnitude2 = sum(b * b for b in vec2) ** 0.5
        
        if magnitude1 == 0 or magnitude2 == 0:
            return 0.0
        
        return dot_product / (magnitude1 * magnitude2)
    
    def configure_pipeline(self, config_updates: Dict[str, Any]) -> None:
        """파이프라인 설정 업데이트"""
        
        old_config = self.config.copy()
        self.config.update(config_updates)
        
        # 모델 변경 시 캐시 초기화
        if (old_config.get("stage1_model") != self.config.get("stage1_model") or
            old_config.get("stage2_model") != self.config.get("stage2_model")):
            self.clear_cache()
            self.fast_embedding_model = None
            self.precision_model = None
        
        print(f"✅ 파이프라인 설정 업데이트: {config_updates}")
    
    def get_pipeline_stats(self) -> Dict[str, Any]:
        """파이프라인 통계"""
        
        efficiency = 0.0
        if self.stats["total_processed"] > 0:
            efficiency = (self.stats["stage1_filtered"] / self.stats["total_processed"]) * 100
        
        return {
            **self.stats,
            "config": self.config,
            "efficiency_percent": efficiency,
            "cache_size": {
                "stage1": len(self.embedding_cache),
                "stage2": len(self.classification_cache)
            }
        }
    
    def clear_cache(self) -> None:
        """캐시 초기화"""
        self.embedding_cache.clear()
        self.classification_cache.clear()
        print("✅ 파이프라인 캐시 초기화 완료")
    
    def reset_stats(self) -> None:
        """통계 초기화"""
        self.stats = {
            "total_processed": 0,
            "stage1_filtered": 0,
            "stage2_processed": 0,
            "cache_hits": 0,
            "processing_times": {
                "stage1_avg": 0.0,
                "stage2_avg": 0.0,
                "total_avg": 0.0
            }
        }
        print("✅ 파이프라인 통계 초기화 완료")
    
    def get_recommended_config(self, scenario: str) -> Dict[str, Any]:
        """시나리오별 추천 설정"""
        
        configs = {
            "real_time": {
                "stage1_threshold": 0.8,
                "stage1_model": "kr_sbert_default",
                "stage2_model": "koelectra_v3",
                "batch_size": 50,
                "enable_caching": True
            },
            "batch_processing": {
                "stage1_threshold": 0.7,
                "stage1_model": "kr_sbert_default", 
                "stage2_model": "beomi_kcelectra",
                "batch_size": 200,
                "enable_caching": True
            },
            "high_accuracy": {
                "stage1_threshold": 0.6,  # 더 많은 샘플을 2차로
                "stage1_model": "kr_sbert_default",
                "stage2_model": "klue_roberta_large",
                "batch_size": 100,
                "enable_caching": True
            },
            "cost_efficient": {
                "stage1_threshold": 0.85,  # 1차에서 대부분 처리
                "stage1_model": "multilingual_mini",
                "stage2_model": "beomi_kcelectra",
                "batch_size": 500,
                "enable_caching": True
            }
        }
        
        return configs.get(scenario, self.config)
    
    def apply_scenario_config(self, scenario: str) -> bool:
        """시나리오 설정 적용"""
        
        recommended_config = self.get_recommended_config(scenario)
        if recommended_config != self.config:
            self.configure_pipeline(recommended_config)
            print(f"✅ '{scenario}' 시나리오 설정 적용 완료")
            return True
        
        print(f"⚠️ '{scenario}' 시나리오를 찾을 수 없습니다")
        return False
