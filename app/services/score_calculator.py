"""
점수 계산 서비스 - Single Responsibility Principle 적용
"""
import re
from typing import List, Set
from app.core.interfaces import IScoreCalculator, IEmbeddingModel


class ScoreCalculator(IScoreCalculator):
    """점수 계산 서비스 - 점수 계산에만 집중"""
    
    def __init__(self, embedding_model: IEmbeddingModel):
        self.embedding_model = embedding_model
        
        # 한국어 불용어
        self.stopwords = {
            '은', '는', '이', '가', '을', '를', '에', '의', '와', '과', '도', '만',
            '부터', '까지', '로', '으로', '에서', '께서', '한테', '안녕하세요',
            '감사합니다', '죄송합니다', '고객님', '문의', '요청', '부탁드립니다'
        }
    
    def calculate_semantic_similarity(self, question: str, answer: str) -> float:
        """의미적 유사도 계산"""
        if not question or not answer:
            return 0.0
        
        try:
            question_embedding = self.embedding_model.encode(question)
            answer_embedding = self.embedding_model.encode(answer)
            
            # 코사인 유사도 계산
            similarity = self._cosine_similarity(question_embedding, answer_embedding)
            return max(0.0, min(1.0, similarity))
        except Exception as e:
            print(f"⚠️ 임베딩 계산 오류: {e}")
            return 0.0
    
    def calculate_topic_relevance(self, question: str, answer: str) -> float:
        """주제 관련성 계산"""
        if not question or not answer:
            return 0.0
        
        question_keywords = self._extract_keywords(question)
        answer_keywords = self._extract_keywords(answer)
        
        if not question_keywords:
            return 0.0
        
        # 주제어 매칭 점수
        matched_keywords = set(question_keywords) & set(answer_keywords)
        relevance_score = len(matched_keywords) / len(question_keywords)
        
        # 특정 주제별 가중치 적용
        topic_weights = {
            '배송': 1.2, '택배': 1.2, '발송': 1.2,
            '환불': 1.3, '취소': 1.3, '반품': 1.3,
            '상품': 1.1, '제품': 1.1, '품질': 1.1,
            '결제': 1.2, '카드': 1.2, '계좌': 1.2
        }
        
        # 가중치 적용
        weighted_score = relevance_score
        for keyword in matched_keywords:
            if keyword in topic_weights:
                weighted_score *= topic_weights[keyword]
        
        return min(1.0, weighted_score)
    
    def calculate_keyword_overlap(self, question: str, answer: str) -> float:
        """키워드 겹침 계산"""
        if not question or not answer:
            return 0.0
        
        question_keywords = set(self._extract_keywords(question))
        answer_keywords = set(self._extract_keywords(answer))
        
        if not question_keywords:
            return 0.0
        
        overlap = len(question_keywords & answer_keywords)
        return overlap / len(question_keywords)
    
    def _extract_keywords(self, text: str) -> List[str]:
        """키워드 추출"""
        if not text:
            return []
        
        # 한글, 영문, 숫자만 추출
        words = re.findall(r'[가-힣a-zA-Z0-9]+', text.lower())
        
        # 불용어 제거 및 길이 필터링
        keywords = [
            word for word in words 
            if word not in self.stopwords and len(word) >= 2
        ]
        
        return keywords
    
    def _cosine_similarity(self, vec1: List[float], vec2: List[float]) -> float:
        """코사인 유사도 계산"""
        if not vec1 or not vec2 or len(vec1) != len(vec2):
            return 0.0
        
        # 내적 계산
        dot_product = sum(a * b for a, b in zip(vec1, vec2))
        
        # 벡터 크기 계산
        magnitude1 = sum(a * a for a in vec1) ** 0.5
        magnitude2 = sum(b * b for b in vec2) ** 0.5
        
        if magnitude1 == 0 or magnitude2 == 0:
            return 0.0
        
        return dot_product / (magnitude1 * magnitude2)
