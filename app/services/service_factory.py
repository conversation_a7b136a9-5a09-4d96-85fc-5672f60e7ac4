"""
서비스 팩토리 - Dependency Injection Container with Dynamic Model Support
"""
from typing import Optional, Dict, Any, List
from app.core.interfaces import (
    IAnalysisService, IScoreCalculator, IQualityEvaluator,
    IFeedbackGenerator, IEmbeddingModel, IDatabaseService
)
from app.core.model_registry import ModelRegistry
from app.services.analysis_refactored import RefactoredAnalysisService
from app.services.score_calculator import ScoreCalculator
from app.services.quality_evaluator import QualityEvaluator
from app.services.feedback_generator import FeedbackGenerator
from app.services.database import DatabaseService
from app.models.embedding import EmbeddingModel


class ServiceFactory:
    """
    서비스 팩토리 - Dependency Inversion Principle 적용 + 동적 모델 지원

    모든 의존성을 중앙에서 관리하고 주입
    동적으로 모델을 교체할 수 있는 기능 제공
    """

    def __init__(self, supabase_url: str = None, supabase_key: str = None):
        self.supabase_url = supabase_url
        self.supabase_key = supabase_key

        # 모델 레지스트리
        self.model_registry = ModelRegistry()

        # 현재 선택된 모델들
        self.current_embedding_model = "kr_sbert_default"
        self.current_llm_model = None
        self.current_classification_model = None

        # 싱글톤 인스턴스들
        self._embedding_model = None
        self._score_calculator = None
        self._quality_evaluator = None
        self._feedback_generator = None
        self._analysis_service = None
        self._database_service = None
    
    def get_embedding_model(self) -> IEmbeddingModel:
        """임베딩 모델 인스턴스 반환 (동적 모델 지원)"""
        if self._embedding_model is None:
            # 모델 레지스트리에서 현재 선택된 모델 가져오기
            model = self.model_registry.get_embedding_model(self.current_embedding_model)
            if model is None:
                # 폴백: 기본 모델 사용
                print(f"⚠️ 선택된 모델({self.current_embedding_model})을 사용할 수 없습니다. 기본 모델을 사용합니다.")
                model = EmbeddingModel()
            self._embedding_model = model
        return self._embedding_model
    
    def get_score_calculator(self) -> IScoreCalculator:
        """점수 계산기 인스턴스 반환 (싱글톤)"""
        if self._score_calculator is None:
            embedding_model = self.get_embedding_model()
            self._score_calculator = ScoreCalculator(embedding_model)
        return self._score_calculator
    
    def get_quality_evaluator(self) -> IQualityEvaluator:
        """품질 평가기 인스턴스 반환 (싱글톤)"""
        if self._quality_evaluator is None:
            score_calculator = self.get_score_calculator()
            self._quality_evaluator = QualityEvaluator(score_calculator)
        return self._quality_evaluator
    
    def get_feedback_generator(self) -> IFeedbackGenerator:
        """피드백 생성기 인스턴스 반환 (싱글톤)"""
        if self._feedback_generator is None:
            self._feedback_generator = FeedbackGenerator()
        return self._feedback_generator
    
    def get_analysis_service(self) -> IAnalysisService:
        """분석 서비스 인스턴스 반환 (싱글톤)"""
        if self._analysis_service is None:
            score_calculator = self.get_score_calculator()
            quality_evaluator = self.get_quality_evaluator()
            feedback_generator = self.get_feedback_generator()
            
            self._analysis_service = RefactoredAnalysisService(
                score_calculator=score_calculator,
                quality_evaluator=quality_evaluator,
                feedback_generator=feedback_generator
            )
        return self._analysis_service
    
    def get_database_service(self) -> IDatabaseService:
        """데이터베이스 서비스 인스턴스 반환 (싱글톤)"""
        if self._database_service is None:
            self._database_service = DatabaseService(
                supabase_url=self.supabase_url,
                supabase_key=self.supabase_key
            )
        return self._database_service
    
    def create_legacy_analysis_service(self):
        """기존 분석 서비스 생성 (호환성 유지)"""
        from app.services.analysis import AnalysisService
        return AnalysisService()
    
    def switch_embedding_model(self, model_name: str) -> bool:
        """임베딩 모델 동적 교체"""
        if model_name not in self.model_registry.list_available_models()["embedding"]:
            print(f"❌ 사용할 수 없는 임베딩 모델: {model_name}")
            return False

        old_model = self.current_embedding_model
        self.current_embedding_model = model_name

        # 관련 인스턴스들 초기화 (재생성 유도)
        self._embedding_model = None
        self._score_calculator = None
        self._quality_evaluator = None
        self._analysis_service = None

        print(f"✅ 임베딩 모델 교체: {old_model} → {model_name}")
        return True

    def switch_llm_model(self, model_name: str) -> bool:
        """LLM 모델 동적 교체"""
        if model_name not in self.model_registry.list_available_models()["llm"]:
            print(f"❌ 사용할 수 없는 LLM 모델: {model_name}")
            return False

        old_model = self.current_llm_model
        self.current_llm_model = model_name

        # 관련 인스턴스들 초기화
        self._analysis_service = None

        print(f"✅ LLM 모델 교체: {old_model} → {model_name}")
        return True

    def switch_classification_model(self, model_name: str) -> bool:
        """분류 모델 동적 교체"""
        if model_name not in self.model_registry.list_available_models()["classification"]:
            print(f"❌ 사용할 수 없는 분류 모델: {model_name}")
            return False

        old_model = self.current_classification_model
        self.current_classification_model = model_name

        print(f"✅ 분류 모델 교체: {old_model} → {model_name}")
        return True

    def get_current_models(self) -> Dict[str, Optional[str]]:
        """현재 선택된 모델들 반환"""
        return {
            "embedding": self.current_embedding_model,
            "llm": self.current_llm_model,
            "classification": self.current_classification_model
        }

    def get_available_models(self) -> Dict[str, List[str]]:
        """사용 가능한 모델 목록 반환"""
        return self.model_registry.list_available_models()

    def get_model_info(self, model_name: str) -> Optional[Dict[str, Any]]:
        """모델 정보 반환"""
        return self.model_registry.get_model_info(model_name)

    def register_custom_model(
        self,
        model_type: str,
        name: str,
        model_class,
        config: Dict[str, Any],
        description: str = "",
        tags: List[str] = None
    ) -> bool:
        """커스텀 모델 등록"""
        if model_type == "embedding":
            return self.model_registry.register_embedding_model(
                name, model_class, config, description, tags
            )
        elif model_type == "llm":
            return self.model_registry.register_llm_model(
                name, model_class, config, description, tags
            )
        elif model_type == "classification":
            return self.model_registry.register_classification_model(
                name, model_class, config, description, tags
            )
        else:
            print(f"❌ 지원하지 않는 모델 타입: {model_type}")
            return False

    def reset_instances(self):
        """모든 인스턴스 초기화 (테스트용)"""
        self._embedding_model = None
        self._score_calculator = None
        self._quality_evaluator = None
        self._feedback_generator = None
        self._analysis_service = None
        self._database_service = None
