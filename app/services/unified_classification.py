"""
통합 분류 시스템 - 간소화된 버전
"""
import re
import time
from typing import Dict, List, Any, Optional
from datetime import datetime

from app.models.embedding import EmbeddingModel


class UnifiedClassificationSystem:
    """통합 분류 시스템 - 간소화된 버전"""
    
    def __init__(self, embedding_model: EmbeddingModel):
        self.embedding_model = embedding_model
        
        # 카테고리 키워드 매핑
        self.category_keywords = {
            "배송": ["배송", "택배", "발송", "운송", "배달", "도착", "언제", "빨리", "운송장"],
            "상품": ["상품", "제품", "품질", "사이즈", "색상", "재질", "크기", "무게", "사양"],
            "교환": ["교환", "바꿔", "다른", "변경", "사이즈", "색깔", "다시", "재발송"],
            "환불": ["환불", "취소", "돌려", "반품", "환급", "되돌리기", "철회", "계좌"],
            "결제": ["결제", "카드", "계좌", "무통장", "입금", "결제수단", "할부", "포인트"],
            "1:1문의": ["문의", "질문", "궁금", "알고싶어", "확인", "체크", "문의드려"],
            "불만": ["불만", "화나", "짜증", "실망", "최악", "별로", "나쁘", "엉망"]
        }
        
        # 감정 키워드
        self.sentiment_keywords = {
            "긍정": ["좋아", "만족", "감사", "훌륭", "최고", "완벽", "기뻐"],
            "부정": ["화나", "짜증", "실망", "최악", "별로", "나쁘", "엉망", "속상"],
            "중립": ["문의", "질문", "확인", "알려주세요", "궁금"]
        }
        
        # 긴급도 키워드
        self.urgency_keywords = {
            "높음": ["급해", "빨리", "당장", "즉시", "지금", "오늘", "내일"],
            "보통": ["언제", "며칠", "일주일", "시간", "기간"],
            "낮음": ["천천히", "괜찮아", "급하지않아", "여유"]
        }
    
    def _extract_keywords(self, text: str) -> List[str]:
        """키워드 추출"""
        if not text:
            return []
        
        # 한글, 영문, 숫자만 추출
        words = re.findall(r'[가-힣a-zA-Z0-9]+', text.lower())
        return [word for word in words if len(word) > 1]
    
    def _classify_category(self, text: str) -> Dict[str, Any]:
        """카테고리 분류"""
        keywords = self._extract_keywords(text)
        category_scores = {}
        
        for category, cat_keywords in self.category_keywords.items():
            score = 0
            for keyword in keywords:
                for cat_keyword in cat_keywords:
                    if keyword in cat_keyword or cat_keyword in keyword:
                        score += 1
            
            if score > 0:
                category_scores[category] = score / len(cat_keywords)
        
        if category_scores:
            best_category = max(category_scores.items(), key=lambda x: x[1])
            return {
                "category": best_category[0],
                "confidence": best_category[1]
            }
        else:
            return {
                "category": "기타",
                "confidence": 0.0
            }
    
    def _analyze_sentiment(self, text: str) -> Dict[str, Any]:
        """감정 분석"""
        keywords = self._extract_keywords(text)
        sentiment_scores = {}
        
        for sentiment, sent_keywords in self.sentiment_keywords.items():
            score = 0
            for keyword in keywords:
                for sent_keyword in sent_keywords:
                    if keyword in sent_keyword or sent_keyword in keyword:
                        score += 1
            
            if score > 0:
                sentiment_scores[sentiment] = score
        
        if sentiment_scores:
            best_sentiment = max(sentiment_scores.items(), key=lambda x: x[1])
            return {
                "sentiment": best_sentiment[0],
                "score": best_sentiment[1] / 10  # 정규화
            }
        else:
            return {
                "sentiment": "중립",
                "score": 0.5
            }
    
    def _analyze_urgency(self, text: str) -> Dict[str, Any]:
        """긴급도 분석"""
        keywords = self._extract_keywords(text)
        urgency_scores = {}
        
        for urgency, urg_keywords in self.urgency_keywords.items():
            score = 0
            for keyword in keywords:
                for urg_keyword in urg_keywords:
                    if keyword in urg_keyword or urg_keyword in keyword:
                        score += 1
            
            if score > 0:
                urgency_scores[urgency] = score
        
        if urgency_scores:
            best_urgency = max(urgency_scores.items(), key=lambda x: x[1])
            return {
                "urgency": best_urgency[0],
                "score": best_urgency[1] / 5  # 정규화
            }
        else:
            return {
                "urgency": "보통",
                "score": 0.5
            }
    
    def _calculate_priority(self, classification: Dict, sentiment: Dict, urgency: Dict) -> Dict[str, Any]:
        """우선순위 계산"""
        # 가중치
        weights = {
            "category": 0.3,
            "sentiment": 0.4,
            "urgency": 0.3
        }
        
        # 카테고리별 우선순위
        category_priority = {
            "환불": 0.9,
            "불만": 0.8,
            "배송": 0.7,
            "결제": 0.6,
            "교환": 0.5,
            "상품": 0.4,
            "1:1문의": 0.3,
            "기타": 0.2
        }
        
        # 감정별 우선순위
        sentiment_priority = {
            "부정": 0.9,
            "중립": 0.5,
            "긍정": 0.3
        }
        
        # 긴급도별 우선순위
        urgency_priority = {
            "높음": 0.9,
            "보통": 0.5,
            "낮음": 0.3
        }
        
        # 종합 우선순위 계산
        cat_score = category_priority.get(classification["category"], 0.2)
        sent_score = sentiment_priority.get(sentiment["sentiment"], 0.5)
        urg_score = urgency_priority.get(urgency["urgency"], 0.5)
        
        priority_score = (
            cat_score * weights["category"] +
            sent_score * weights["sentiment"] +
            urg_score * weights["urgency"]
        )
        
        # 우선순위 레벨 결정
        if priority_score >= 0.8:
            priority_level = "긴급"
        elif priority_score >= 0.6:
            priority_level = "높음"
        elif priority_score >= 0.4:
            priority_level = "보통"
        else:
            priority_level = "낮음"
        
        return {
            "level": priority_level,
            "score": priority_score
        }
    
    def _recommend_routing(self, classification: Dict, priority: Dict) -> Dict[str, Any]:
        """라우팅 추천"""
        category = classification["category"]
        priority_level = priority["level"]
        
        # 부서 매핑
        department_mapping = {
            "배송": "배송팀",
            "상품": "상품팀",
            "교환": "교환팀",
            "환불": "환불팀",
            "결제": "결제팀",
            "1:1문의": "고객서비스팀",
            "불만": "고객만족팀",
            "기타": "일반상담팀"
        }
        
        department = department_mapping.get(category, "일반상담팀")
        
        # 액션 아이템 생성
        action_items = []
        
        if priority_level in ["긴급", "높음"]:
            action_items.append("즉시 처리 필요")
            action_items.append("관리자 에스컬레이션")
        
        if category == "불만":
            action_items.append("고객 만족도 조사 실시")
            action_items.append("보상 방안 검토")
        
        if category in ["환불", "교환"]:
            action_items.append("정책 확인 후 처리")
            action_items.append("처리 결과 고객 안내")
        
        return {
            "department": department,
            "action_items": action_items
        }
    
    async def classify_inquiry(self, question: str, answer: str = None) -> Dict[str, Any]:
        """문의 분류 - 메인 메서드"""
        start_time = time.time()
        
        try:
            # 분류 실행
            classification = self._classify_category(question)
            sentiment = self._analyze_sentiment(question)
            urgency = self._analyze_urgency(question)
            priority = self._calculate_priority(classification, sentiment, urgency)
            routing = self._recommend_routing(classification, priority)
            
            # 처리 시간 계산
            processing_time = (time.time() - start_time) * 1000
            
            return {
                "classification": classification,
                "sentiment_analysis": sentiment,
                "urgency_analysis": urgency,
                "priority_level": priority["level"],
                "priority_score": priority["score"],
                "routing_recommendation": routing,
                "action_items": routing["action_items"],
                "processing_time_ms": processing_time,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            print(f"❌ 분류 중 오류: {e}")
            return {
                "classification": {"category": "기타", "confidence": 0.0},
                "sentiment_analysis": {"sentiment": "중립", "score": 0.5},
                "urgency_analysis": {"urgency": "보통", "score": 0.5},
                "priority_level": "보통",
                "priority_score": 0.5,
                "routing_recommendation": {"department": "일반상담팀"},
                "action_items": ["분류 실패 - 수동 처리 필요"],
                "processing_time_ms": (time.time() - start_time) * 1000,
                "timestamp": datetime.now().isoformat(),
                "error": str(e)
            }
    
    async def classify_batch(self, inquiries_data: List[Dict]) -> List[Dict[str, Any]]:
        """배치 분류"""
        results = []
        
        for i, inquiry in enumerate(inquiries_data, 1):
            question = inquiry.get('inquiry_content') or inquiry.get('question') or inquiry.get('content', '')
            answer = inquiry.get('answer_content') or inquiry.get('answer', '')
            
            result = await self.classify_inquiry(question, answer)
            result['inquiry_id'] = inquiry.get('id', f'batch_{i}')
            result['original_data'] = inquiry
            
            results.append(result)
            
            # 진행률 출력
            if i % 10 == 0 or i == len(inquiries_data):
                print(f"  진행률: {i}/{len(inquiries_data)} ({i/len(inquiries_data)*100:.1f}%)")
        
        return results
    
    def generate_classification_summary(self, results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """분류 결과 요약 생성"""
        if not results:
            return {}
        
        total_inquiries = len(results)
        
        # 카테고리 분포
        category_distribution = {}
        for result in results:
            category = result['classification']['category']
            category_distribution[category] = category_distribution.get(category, 0) + 1
        
        # 감정 분포
        sentiment_distribution = {}
        for result in results:
            sentiment = result['sentiment_analysis']['sentiment']
            sentiment_distribution[sentiment] = sentiment_distribution.get(sentiment, 0) + 1
        
        # 긴급도 분포
        urgency_distribution = {}
        for result in results:
            urgency = result['urgency_analysis']['urgency']
            urgency_distribution[urgency] = urgency_distribution.get(urgency, 0) + 1
        
        # 인사이트 생성
        insights = []
        
        # 가장 많은 카테고리
        top_category = max(category_distribution.items(), key=lambda x: x[1])
        insights.append(f"가장 많은 문의 유형: {top_category[0]} ({top_category[1]}건)")
        
        # 부정적 감정 비율
        negative_count = sentiment_distribution.get("부정", 0)
        negative_rate = (negative_count / total_inquiries) * 100
        if negative_rate > 30:
            insights.append(f"부정적 감정 비율이 높음: {negative_rate:.1f}%")
        
        # 긴급 문의 비율
        urgent_count = urgency_distribution.get("높음", 0)
        urgent_rate = (urgent_count / total_inquiries) * 100
        if urgent_rate > 20:
            insights.append(f"긴급 문의 비율이 높음: {urgent_rate:.1f}%")
        
        return {
            "total_inquiries": total_inquiries,
            "category_distribution": category_distribution,
            "sentiment_distribution": sentiment_distribution,
            "urgency_distribution": urgency_distribution,
            "insights": insights,
            "analysis_timestamp": datetime.now().isoformat()
        }
