"""
모델 비교 서비스 - 여러 모델의 성능을 동시에 비교
"""
import time
import asyncio
from typing import Dict, List, Any, Optional
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor

from app.core.domain import AnalysisRequest, QAPair, BatchResult
from app.core.interfaces import IAnalysisService
from app.services.service_factory import ServiceFactory
from app.services.result_storage import ResultStorageService


class ModelComparisonService:
    """
    모델 비교 서비스
    
    기능:
    - 여러 모델 동시 실행 및 비교
    - 성능 메트릭 수집
    - 비교 대시보드 데이터 생성
    - 모델 추천 시스템
    """
    
    def __init__(self, service_factory: ServiceFactory):
        self.service_factory = service_factory
        self.comparison_history = []
        self.storage_service = ResultStorageService()

        print("✅ 모델 비교 서비스 초기화 완료")
    
    async def compare_all_models(
        self, 
        requests: List[AnalysisRequest],
        model_names: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """모든 사용 가능한 모델로 비교 실행"""
        
        if model_names is None:
            # 사용 가능한 모든 임베딩 모델 가져오기
            available_models = self.service_factory.get_available_models()
            model_names = available_models.get("embedding", [])
        
        if not model_names:
            return {"error": "비교할 모델이 없습니다"}
        
        print(f"🔍 {len(model_names)}개 모델로 {len(requests)}건 요청 비교 시작...")
        
        comparison_result = {
            "metadata": {
                "comparison_id": f"comp_{int(time.time())}",
                "timestamp": datetime.now().isoformat(),
                "models_tested": model_names,
                "total_requests": len(requests),
                "test_duration_seconds": 0
            },
            "model_results": {},
            "performance_metrics": {},
            "accuracy_metrics": {},
            "comparison_summary": {},
            "recommendations": []
        }
        
        start_time = time.time()
        
        # 각 모델별로 테스트 실행
        for model_name in model_names:
            print(f"  📊 {model_name} 모델 테스트 중...")
            
            model_result = await self._test_single_model(model_name, requests)
            comparison_result["model_results"][model_name] = model_result
            
            # 성능 메트릭 추출
            comparison_result["performance_metrics"][model_name] = self._extract_performance_metrics(model_result)
            
            # 정확도 메트릭 추출
            comparison_result["accuracy_metrics"][model_name] = self._extract_accuracy_metrics(model_result)
        
        # 테스트 완료 시간
        comparison_result["metadata"]["test_duration_seconds"] = time.time() - start_time
        
        # 비교 요약 생성
        comparison_result["comparison_summary"] = self._generate_comparison_summary(comparison_result)
        
        # 추천 생성
        comparison_result["recommendations"] = self._generate_model_recommendations(comparison_result)
        
        # 히스토리에 저장
        self.comparison_history.append(comparison_result)

        # 영구 저장
        tags = ["model_comparison", "auto_generated"]
        if len(model_names) <= 3:
            tags.append("small_scale")
        else:
            tags.append("large_scale")

        description = f"{len(model_names)}개 모델 비교 ({len(requests)}건 요청)"
        result_id = self.storage_service.save_comparison_result(
            comparison_result, tags=tags, description=description
        )

        if result_id:
            comparison_result["storage_info"] = {
                "result_id": result_id,
                "saved": True
            }

        print(f"✅ 모델 비교 완료 ({comparison_result['metadata']['test_duration_seconds']:.2f}초)")
        if result_id:
            print(f"📁 결과 저장 완료: {result_id}")

        return comparison_result
    
    async def benchmark_models(
        self, 
        test_dataset: List[Dict[str, Any]],
        model_names: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """표준 데이터셋으로 모델 벤치마크"""
        
        # 테스트 데이터를 AnalysisRequest로 변환
        requests = []
        for item in test_dataset:
            request = AnalysisRequest(
                inquiry_id=item.get("id", f"test_{len(requests)}"),
                question_text=item.get("question", ""),
                answer_text=item.get("answer", "")
            )
            requests.append(request)
        
        print(f"🏁 {len(requests)}건의 벤치마크 데이터로 모델 성능 측정...")
        
        # 비교 실행
        comparison_result = await self.compare_all_models(requests, model_names)
        
        # 벤치마크 특화 메트릭 추가
        comparison_result["benchmark_metrics"] = self._calculate_benchmark_metrics(
            comparison_result, test_dataset
        )
        
        return comparison_result
    
    def generate_comparison_dashboard(self, comparison_result: Dict[str, Any]) -> Dict[str, Any]:
        """비교 대시보드 데이터 생성"""
        
        dashboard_data = {
            "overview": {
                "total_models": len(comparison_result.get("model_results", {})),
                "total_requests": comparison_result.get("metadata", {}).get("total_requests", 0),
                "test_duration": comparison_result.get("metadata", {}).get("test_duration_seconds", 0),
                "best_model": self._get_best_model(comparison_result)
            },
            "performance_chart": self._generate_performance_chart_data(comparison_result),
            "accuracy_chart": self._generate_accuracy_chart_data(comparison_result),
            "speed_chart": self._generate_speed_chart_data(comparison_result),
            "detailed_metrics": comparison_result.get("performance_metrics", {}),
            "model_rankings": self._generate_model_rankings(comparison_result),
            "recommendations": comparison_result.get("recommendations", [])
        }
        
        return dashboard_data
    
    async def _test_single_model(
        self, 
        model_name: str, 
        requests: List[AnalysisRequest]
    ) -> Dict[str, Any]:
        """단일 모델 테스트"""
        
        # 모델 교체
        success = self.service_factory.switch_embedding_model(model_name)
        if not success:
            return {"error": f"모델 교체 실패: {model_name}"}
        
        # 분석 서비스 가져오기
        analysis_service = self.service_factory.get_analysis_service()
        
        results = []
        processing_times = []
        
        for request in requests:
            start_time = time.time()
            
            try:
                # QA 데이터 준비
                qa_data = await self._prepare_qa_data(request)
                
                # 분석 실행
                result = analysis_service.analyze_qa_pair(
                    qa_data['id'], qa_data['question'], qa_data['answer']
                )
                
                processing_time = (time.time() - start_time) * 1000  # ms
                processing_times.append(processing_time)
                results.append(result)
                
            except Exception as e:
                print(f"  ❌ 요청 처리 실패: {e}")
                processing_times.append(0)
                continue
        
        return {
            "model_name": model_name,
            "total_requests": len(requests),
            "successful_requests": len(results),
            "results": results,
            "processing_times": processing_times,
            "avg_processing_time": sum(processing_times) / len(processing_times) if processing_times else 0,
            "min_processing_time": min(processing_times) if processing_times else 0,
            "max_processing_time": max(processing_times) if processing_times else 0
        }
    
    def _extract_performance_metrics(self, model_result: Dict[str, Any]) -> Dict[str, float]:
        """성능 메트릭 추출"""
        results = model_result.get("results", [])
        
        if not results:
            return {
                "avg_score": 0.0,
                "pass_rate": 0.0,
                "avg_processing_time": model_result.get("avg_processing_time", 0),
                "throughput": 0.0
            }
        
        scores = [r.overall_score for r in results]
        pass_count = sum(1 for r in results if r.pass_threshold)
        
        # 처리량 계산 (requests per second)
        avg_time_seconds = model_result.get("avg_processing_time", 1) / 1000
        throughput = 1 / avg_time_seconds if avg_time_seconds > 0 else 0
        
        return {
            "avg_score": sum(scores) / len(scores),
            "pass_rate": (pass_count / len(results)) * 100,
            "avg_processing_time": model_result.get("avg_processing_time", 0),
            "throughput": throughput
        }
    
    def _extract_accuracy_metrics(self, model_result: Dict[str, Any]) -> Dict[str, float]:
        """정확도 메트릭 추출"""
        results = model_result.get("results", [])
        
        if not results:
            return {
                "semantic_similarity": 0.0,
                "topic_relevance": 0.0,
                "answer_accuracy": 0.0,
                "consistency": 0.0
            }
        
        # 각 메트릭별 평균 계산
        semantic_scores = [r.semantic_similarity for r in results]
        topic_scores = [r.topic_relevance for r in results]
        accuracy_scores = [r.answer_accuracy for r in results]
        
        # 일관성 계산 (점수의 표준편차 역수)
        import statistics
        score_variance = statistics.variance([r.overall_score for r in results]) if len(results) > 1 else 0
        consistency = 1 / (1 + score_variance)  # 분산이 낮을수록 일관성 높음
        
        return {
            "semantic_similarity": sum(semantic_scores) / len(semantic_scores),
            "topic_relevance": sum(topic_scores) / len(topic_scores),
            "answer_accuracy": sum(accuracy_scores) / len(accuracy_scores),
            "consistency": consistency
        }
    
    def _generate_comparison_summary(self, comparison_result: Dict[str, Any]) -> Dict[str, Any]:
        """비교 요약 생성"""
        performance_metrics = comparison_result.get("performance_metrics", {})
        
        if not performance_metrics:
            return {}
        
        # 최고 성능 모델들 찾기
        best_score_model = max(performance_metrics.keys(), 
                              key=lambda k: performance_metrics[k].get("avg_score", 0))
        
        fastest_model = min(performance_metrics.keys(), 
                           key=lambda k: performance_metrics[k].get("avg_processing_time", float('inf')))
        
        highest_throughput_model = max(performance_metrics.keys(), 
                                     key=lambda k: performance_metrics[k].get("throughput", 0))
        
        most_consistent_model = max(performance_metrics.keys(), 
                                   key=lambda k: comparison_result.get("accuracy_metrics", {}).get(k, {}).get("consistency", 0))
        
        return {
            "best_overall_score": best_score_model,
            "fastest_processing": fastest_model,
            "highest_throughput": highest_throughput_model,
            "most_consistent": most_consistent_model,
            "performance_spread": {
                "score_range": self._calculate_metric_range(performance_metrics, "avg_score"),
                "speed_range": self._calculate_metric_range(performance_metrics, "avg_processing_time"),
                "throughput_range": self._calculate_metric_range(performance_metrics, "throughput")
            }
        }
    
    def _generate_model_recommendations(self, comparison_result: Dict[str, Any]) -> List[str]:
        """모델 추천 생성"""
        recommendations = []
        summary = comparison_result.get("comparison_summary", {})
        
        if not summary:
            return ["비교 결과가 부족합니다."]
        
        # 용도별 추천
        recommendations.append(f"🏆 전체 성능 최고: {summary.get('best_overall_score', 'N/A')}")
        recommendations.append(f"⚡ 처리 속도 최고: {summary.get('fastest_processing', 'N/A')}")
        recommendations.append(f"🔄 처리량 최고: {summary.get('highest_throughput', 'N/A')}")
        recommendations.append(f"📊 일관성 최고: {summary.get('most_consistent', 'N/A')}")
        
        # 시나리오별 추천
        recommendations.append("")
        recommendations.append("📋 사용 시나리오별 추천:")
        recommendations.append("• 실시간 처리: 처리 속도 최고 모델 사용")
        recommendations.append("• 대용량 배치: 처리량 최고 모델 사용")
        recommendations.append("• 고품질 분석: 전체 성능 최고 모델 사용")
        recommendations.append("• 안정적 서비스: 일관성 최고 모델 사용")
        
        return recommendations
    
    async def _prepare_qa_data(self, request: AnalysisRequest) -> Dict[str, str]:
        """QA 데이터 준비 (간단 버전)"""
        return {
            'id': request.inquiry_id or f"test_{int(time.time())}",
            'question': request.question_text or "",
            'answer': request.answer_text or ""
        }
    
    def _calculate_metric_range(self, metrics: Dict[str, Dict[str, float]], metric_name: str) -> Dict[str, float]:
        """메트릭 범위 계산"""
        values = [m.get(metric_name, 0) for m in metrics.values()]
        return {
            "min": min(values) if values else 0,
            "max": max(values) if values else 0,
            "avg": sum(values) / len(values) if values else 0
        }
    
    def _get_best_model(self, comparison_result: Dict[str, Any]) -> str:
        """최고 성능 모델 반환"""
        summary = comparison_result.get("comparison_summary", {})
        return summary.get("best_overall_score", "N/A")
    
    def _generate_performance_chart_data(self, comparison_result: Dict[str, Any]) -> Dict[str, Any]:
        """성능 차트 데이터 생성"""
        performance_metrics = comparison_result.get("performance_metrics", {})
        
        return {
            "labels": list(performance_metrics.keys()),
            "datasets": [
                {
                    "label": "평균 점수",
                    "data": [m.get("avg_score", 0) for m in performance_metrics.values()],
                    "type": "bar"
                },
                {
                    "label": "통과율 (%)",
                    "data": [m.get("pass_rate", 0) for m in performance_metrics.values()],
                    "type": "line"
                }
            ]
        }
    
    def _generate_accuracy_chart_data(self, comparison_result: Dict[str, Any]) -> Dict[str, Any]:
        """정확도 차트 데이터 생성"""
        accuracy_metrics = comparison_result.get("accuracy_metrics", {})
        
        return {
            "labels": list(accuracy_metrics.keys()),
            "datasets": [
                {
                    "label": "의미적 유사도",
                    "data": [m.get("semantic_similarity", 0) for m in accuracy_metrics.values()]
                },
                {
                    "label": "주제 관련성",
                    "data": [m.get("topic_relevance", 0) for m in accuracy_metrics.values()]
                },
                {
                    "label": "답변 정확성",
                    "data": [m.get("answer_accuracy", 0) for m in accuracy_metrics.values()]
                }
            ]
        }
    
    def _generate_speed_chart_data(self, comparison_result: Dict[str, Any]) -> Dict[str, Any]:
        """속도 차트 데이터 생성"""
        performance_metrics = comparison_result.get("performance_metrics", {})
        
        return {
            "labels": list(performance_metrics.keys()),
            "datasets": [
                {
                    "label": "평균 처리 시간 (ms)",
                    "data": [m.get("avg_processing_time", 0) for m in performance_metrics.values()],
                    "type": "bar"
                },
                {
                    "label": "처리량 (req/s)",
                    "data": [m.get("throughput", 0) for m in performance_metrics.values()],
                    "type": "line"
                }
            ]
        }
    
    def _generate_model_rankings(self, comparison_result: Dict[str, Any]) -> List[Dict[str, Any]]:
        """모델 순위 생성"""
        performance_metrics = comparison_result.get("performance_metrics", {})
        accuracy_metrics = comparison_result.get("accuracy_metrics", {})
        
        rankings = []
        
        for model_name in performance_metrics.keys():
            perf = performance_metrics[model_name]
            acc = accuracy_metrics.get(model_name, {})
            
            # 종합 점수 계산 (가중 평균)
            overall_score = (
                perf.get("avg_score", 0) * 0.4 +
                (perf.get("pass_rate", 0) / 100) * 0.2 +
                acc.get("semantic_similarity", 0) * 0.2 +
                acc.get("consistency", 0) * 0.2
            )
            
            rankings.append({
                "model": model_name,
                "overall_score": overall_score,
                "avg_score": perf.get("avg_score", 0),
                "pass_rate": perf.get("pass_rate", 0),
                "avg_processing_time": perf.get("avg_processing_time", 0),
                "throughput": perf.get("throughput", 0),
                "consistency": acc.get("consistency", 0)
            })
        
        # 종합 점수 순으로 정렬
        rankings.sort(key=lambda x: x["overall_score"], reverse=True)
        
        return rankings
    
    def _calculate_benchmark_metrics(
        self, 
        comparison_result: Dict[str, Any], 
        test_dataset: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """벤치마크 특화 메트릭 계산"""
        
        # 데이터셋 특성 분석
        dataset_stats = {
            "total_samples": len(test_dataset),
            "avg_question_length": sum(len(item.get("question", "")) for item in test_dataset) / len(test_dataset),
            "avg_answer_length": sum(len(item.get("answer", "")) for item in test_dataset) / len(test_dataset),
            "categories": {}
        }
        
        # 카테고리별 분석
        for item in test_dataset:
            category = item.get("category", "기타")
            dataset_stats["categories"][category] = dataset_stats["categories"].get(category, 0) + 1
        
        return {
            "dataset_stats": dataset_stats,
            "benchmark_score": self._calculate_benchmark_score(comparison_result),
            "model_efficiency": self._calculate_model_efficiency(comparison_result)
        }
    
    def _calculate_benchmark_score(self, comparison_result: Dict[str, Any]) -> Dict[str, float]:
        """벤치마크 점수 계산"""
        performance_metrics = comparison_result.get("performance_metrics", {})
        
        benchmark_scores = {}
        for model_name, metrics in performance_metrics.items():
            # 정규화된 벤치마크 점수 (0-100)
            score = (
                metrics.get("avg_score", 0) * 40 +  # 40%
                (metrics.get("pass_rate", 0) / 100) * 30 +  # 30%
                min(1.0, metrics.get("throughput", 0) / 10) * 20 +  # 20%
                (1 - min(1.0, metrics.get("avg_processing_time", 0) / 1000)) * 10  # 10%
            ) * 100
            
            benchmark_scores[model_name] = score
        
        return benchmark_scores
    
    def _calculate_model_efficiency(self, comparison_result: Dict[str, Any]) -> Dict[str, float]:
        """모델 효율성 계산 (성능 대비 속도)"""
        performance_metrics = comparison_result.get("performance_metrics", {})
        
        efficiency_scores = {}
        for model_name, metrics in performance_metrics.items():
            avg_score = metrics.get("avg_score", 0)
            avg_time = metrics.get("avg_processing_time", 1)
            
            # 효율성 = 성능 / 시간
            efficiency = (avg_score * 1000) / avg_time if avg_time > 0 else 0
            efficiency_scores[model_name] = efficiency
        
        return efficiency_scores
    
    def get_comparison_history(self) -> List[Dict[str, Any]]:
        """비교 히스토리 반환 (메모리)"""
        return self.comparison_history

    def clear_comparison_history(self):
        """비교 히스토리 초기화 (메모리)"""
        self.comparison_history = []
        print("✅ 비교 히스토리 초기화 완료")

    def get_stored_results(self, limit: int = 20) -> List[Dict[str, Any]]:
        """저장된 결과 목록 조회"""
        return self.storage_service.list_results(limit=limit)

    def load_stored_result(self, result_id: str) -> Optional[Dict[str, Any]]:
        """저장된 결과 로드"""
        return self.storage_service.load_comparison_result(result_id)

    def compare_stored_results(self, result_ids: List[str]) -> Dict[str, Any]:
        """저장된 결과들 간 비교"""
        return self.storage_service.compare_results(result_ids)

    def get_performance_trends(self, model_name: str = None, days: int = 30) -> Dict[str, Any]:
        """성능 트렌드 분석"""
        return self.storage_service.get_performance_trends(model_name, days)

    def search_results(self, query: str) -> List[Dict[str, Any]]:
        """결과 검색"""
        return self.storage_service.search_results(query)

    def delete_stored_result(self, result_id: str) -> bool:
        """저장된 결과 삭제"""
        return self.storage_service.delete_result(result_id)

    def export_results_summary(self) -> str:
        """결과 요약 내보내기"""
        return self.storage_service.export_results_summary()

    def get_storage_info(self) -> Dict[str, Any]:
        """저장소 정보"""
        return self.storage_service.get_storage_info()
