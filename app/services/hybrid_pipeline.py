"""
하이브리드 분류 파이프라인 - 간소화된 버전
"""
import asyncio
import time
from typing import Dict, List, Any, Optional, Callable
from datetime import datetime

from app.services.simple_factory import ServiceFactory
from app.services.unified_classification import UnifiedClassificationSystem


class HybridClassificationPipeline:
    """
    하이브리드 분류 파이프라인 - 간소화된 버전
    
    특징:
    - 1차: 빠른 키워드 기반 분류
    - 2차: 정확한 임베딩 기반 분류
    - 적응형: 신뢰도에 따라 단계 조절
    """
    
    def __init__(self, service_factory: ServiceFactory):
        self.service_factory = service_factory
        self.classification_system = UnifiedClassificationSystem(
            service_factory.get_embedding_model()
        )
        
        # 파이프라인 설정
        self.confidence_thresholds = {
            "stage1_pass": 0.8,    # 1차에서 통과할 신뢰도
            "stage2_pass": 0.9,    # 2차에서 통과할 신뢰도
            "final_minimum": 0.6   # 최종 최소 신뢰도
        }
        
        # 성능 통계
        self.performance_stats = {
            "total_processed": 0,
            "stage1_success": 0,
            "stage2_success": 0,
            "average_processing_time": 0.0
        }
    
    async def classify_single(self, text: str, use_hybrid: bool = True) -> Dict[str, Any]:
        """단일 텍스트 분류"""
        start_time = time.time()
        
        try:
            if use_hybrid:
                # 하이브리드 모드: 다단계 분류
                result = await self._hybrid_classify(text)
            else:
                # 직접 모드: 단일 분류
                result = await self.classification_system.classify_inquiry(text)
                result["pipeline_stage"] = "direct"
            
            # 성능 통계 업데이트
            processing_time = (time.time() - start_time) * 1000
            result["processing_time_ms"] = processing_time
            self._update_stats(result, processing_time)
            
            return result
            
        except Exception as e:
            print(f"❌ 분류 중 오류: {e}")
            return {
                "category": "기타",
                "confidence": 0.0,
                "pipeline_stage": "error",
                "processing_time_ms": (time.time() - start_time) * 1000,
                "error": str(e)
            }
    
    async def _hybrid_classify(self, text: str) -> Dict[str, Any]:
        """하이브리드 분류 실행"""
        # 1차: 빠른 분류
        stage1_result = await self.classification_system.classify_inquiry(text)
        
        # 1차 결과 신뢰도 확인
        stage1_confidence = stage1_result["classification"]["confidence"]
        
        if stage1_confidence >= self.confidence_thresholds["stage1_pass"]:
            # 1차에서 충분한 신뢰도 → 완료
            self.performance_stats["stage1_success"] += 1
            stage1_result["pipeline_stage"] = "stage1_complete"
            return stage1_result
        
        # 2차: 정밀 분류 (임베딩 기반)
        stage2_result = await self._stage2_classify(text, stage1_result)
        stage2_confidence = stage2_result["classification"]["confidence"]
        
        if stage2_confidence >= self.confidence_thresholds["stage2_pass"]:
            # 2차에서 충분한 신뢰도 → 완료
            self.performance_stats["stage2_success"] += 1
            stage2_result["pipeline_stage"] = "stage2_complete"
            stage2_result["stage1_result"] = stage1_result["classification"]
            return stage2_result
        
        # 최종: 가장 좋은 결과 선택
        if stage2_confidence > stage1_confidence:
            final_result = stage2_result
            final_result["pipeline_stage"] = "stage2_final"
        else:
            final_result = stage1_result
            final_result["pipeline_stage"] = "stage1_final"
        
        final_result["stage1_result"] = stage1_result["classification"]
        return final_result
    
    async def _stage2_classify(self, text: str, stage1_result: Dict) -> Dict[str, Any]:
        """2차 정밀 분류"""
        # 임베딩 기반 유사도 계산으로 정밀도 향상
        try:
            # 기본 분류 재실행 (더 정밀한 설정으로)
            result = await self.classification_system.classify_inquiry(text)
            
            # 1차 결과와 결합하여 신뢰도 향상
            stage1_category = stage1_result["classification"]["category"]
            stage2_category = result["classification"]["category"]
            
            if stage1_category == stage2_category:
                # 두 단계 결과가 일치하면 신뢰도 증가
                result["classification"]["confidence"] = min(1.0, 
                    result["classification"]["confidence"] * 1.2)
            
            return result
            
        except Exception as e:
            print(f"⚠️ 2차 분류 실패: {e}")
            return stage1_result
    
    async def classify_batch(
        self, 
        texts: List[str], 
        use_hybrid: bool = True,
        progress_callback: Optional[Callable] = None
    ) -> List[Dict[str, Any]]:
        """배치 분류"""
        results = []
        
        for i, text in enumerate(texts, 1):
            try:
                result = await self.classify_single(text, use_hybrid)
                results.append(result)
                
                # 진행률 콜백
                if progress_callback:
                    progress_callback(i, len(texts))
                
            except Exception as e:
                print(f"⚠️ 배치 분류 실패 ({i}/{len(texts)}): {e}")
                results.append({
                    "category": "기타",
                    "confidence": 0.0,
                    "error": str(e)
                })
        
        return results
    
    def _update_stats(self, result: Dict, processing_time: float) -> None:
        """성능 통계 업데이트"""
        self.performance_stats["total_processed"] += 1
        
        # 평균 처리 시간 업데이트
        total = self.performance_stats["total_processed"]
        current_avg = self.performance_stats["average_processing_time"]
        self.performance_stats["average_processing_time"] = (
            (current_avg * (total - 1) + processing_time) / total
        )
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """성능 통계 조회"""
        total = self.performance_stats["total_processed"]
        if total == 0:
            return self.performance_stats
        
        stats = self.performance_stats.copy()
        stats["stage1_success_rate"] = (stats["stage1_success"] / total) * 100
        stats["stage2_success_rate"] = (stats["stage2_success"] / total) * 100
        
        return stats
    
    def reset_stats(self) -> None:
        """성능 통계 초기화"""
        self.performance_stats = {
            "total_processed": 0,
            "stage1_success": 0,
            "stage2_success": 0,
            "average_processing_time": 0.0
        }
    
    def configure_thresholds(self, **kwargs) -> None:
        """임계값 설정"""
        for key, value in kwargs.items():
            if key in self.confidence_thresholds:
                self.confidence_thresholds[key] = value
                print(f"✅ {key} 임계값을 {value}로 설정했습니다.")
    
    def get_scenario_config(self, scenario: str) -> Dict[str, float]:
        """시나리오별 설정 반환"""
        scenarios = {
            "high_accuracy": {
                "stage1_pass": 0.9,
                "stage2_pass": 0.95,
                "final_minimum": 0.8
            },
            "balanced": {
                "stage1_pass": 0.8,
                "stage2_pass": 0.9,
                "final_minimum": 0.6
            },
            "high_speed": {
                "stage1_pass": 0.6,
                "stage2_pass": 0.8,
                "final_minimum": 0.4
            },
            "conservative": {
                "stage1_pass": 0.95,
                "stage2_pass": 0.98,
                "final_minimum": 0.9
            }
        }
        
        return scenarios.get(scenario, scenarios["balanced"])
    
    def apply_scenario_config(self, scenario: str) -> bool:
        """시나리오별 설정 적용"""
        config = self.get_scenario_config(scenario)
        if config:
            self.confidence_thresholds.update(config)
            print(f"✅ '{scenario}' 시나리오 설정을 적용했습니다.")
            return True
        else:
            print(f"❌ 알 수 없는 시나리오: {scenario}")
            return False
