"""
하이브리드 분류 파이프라인 - 간소화된 버전
1차: 키워드 기반 분류 (고속)
2차: LLM 기반 분류 (고정밀)
"""
import asyncio
import time
import openai
from typing import Dict, List, Any, Optional, Callable
from datetime import datetime

from app.services.simple_factory import ServiceFactory
from app.services.unified_classification import UnifiedClassificationSystem


class HybridClassificationPipeline:
    """
    하이브리드 분류 파이프라인 - 간소화된 버전
    
    특징:
    - 1차: 빠른 키워드 기반 분류
    - 2차: 정확한 임베딩 기반 분류
    - 적응형: 신뢰도에 따라 단계 조절
    """
    
    def __init__(self, service_factory: ServiceFactory):
        self.service_factory = service_factory
        self.classification_system = UnifiedClassificationSystem(
            service_factory.get_embedding_model()
        )
        
        # 파이프라인 설정
        self.confidence_thresholds = {
            "stage1_pass": 0.8,    # 1차에서 통과할 신뢰도
            "stage2_pass": 0.9,    # 2차에서 통과할 신뢰도
            "final_minimum": 0.6   # 최종 최소 신뢰도
        }
        
        # 성능 통계
        self.performance_stats = {
            "total_processed": 0,
            "stage1_success": 0,
            "stage2_success": 0,
            "average_processing_time": 0.0
        }
    
    async def classify_single(self, text: str, use_hybrid: bool = True) -> Dict[str, Any]:
        """단일 텍스트 분류"""
        start_time = time.time()
        
        try:
            if use_hybrid:
                # 하이브리드 모드: 다단계 분류
                result = await self._hybrid_classify(text)
            else:
                # 직접 모드: 단일 분류
                result = await self.classification_system.classify_inquiry(text)
                result["pipeline_stage"] = "direct"
            
            # 성능 통계 업데이트
            processing_time = (time.time() - start_time) * 1000
            result["processing_time_ms"] = processing_time
            self._update_stats(result, processing_time)
            
            return result
            
        except Exception as e:
            print(f"❌ 분류 중 오류: {e}")
            return {
                "category": "기타",
                "confidence": 0.0,
                "pipeline_stage": "error",
                "processing_time_ms": (time.time() - start_time) * 1000,
                "error": str(e)
            }
    
    async def _hybrid_classify(self, text: str) -> Dict[str, Any]:
        """하이브리드 분류 실행"""
        # 1차: 빠른 키워드 기반 분류
        stage1_result = await self.classification_system.classify_inquiry(text)

        # 1차 결과 신뢰도 확인
        stage1_confidence = stage1_result.get("classification", {}).get("confidence", 0.0)

        if stage1_confidence >= self.confidence_thresholds["stage1_pass"]:
            # 1차에서 충분한 신뢰도 → 완료
            self.performance_stats["stage1_success"] += 1
            return {
                "category": stage1_result.get("classification", {}).get("category", "기타"),
                "confidence": stage1_confidence,
                "pipeline_stage": "stage1_complete",
                "stage1_result": stage1_result.get("classification", {})
            }

        # 2차: LLM 기반 정밀 분류
        stage2_result = await self._stage2_llm_classify(text, stage1_result)
        stage2_confidence = stage2_result.get("confidence", 0.0)

        if stage2_confidence >= self.confidence_thresholds["stage2_pass"]:
            # 2차에서 충분한 신뢰도 → 완료
            self.performance_stats["stage2_success"] += 1
            return {
                "category": stage2_result.get("category", "기타"),
                "confidence": stage2_confidence,
                "pipeline_stage": "stage2_complete",
                "stage1_result": stage1_result.get("classification", {}),
                "stage2_result": stage2_result
            }

        # 최종: 가장 좋은 결과 선택
        if stage2_confidence > stage1_confidence:
            return {
                "category": stage2_result.get("category", "기타"),
                "confidence": stage2_confidence,
                "pipeline_stage": "stage2_final",
                "stage1_result": stage1_result.get("classification", {}),
                "stage2_result": stage2_result
            }
        else:
            return {
                "category": stage1_result.get("classification", {}).get("category", "기타"),
                "confidence": stage1_confidence,
                "pipeline_stage": "stage1_final",
                "stage1_result": stage1_result.get("classification", {})
            }
    
    async def _stage2_llm_classify(self, text: str, stage1_result: Dict) -> Dict[str, Any]:
        """2차 LLM 기반 정밀 분류"""
        try:
            # LLM 프롬프트 생성
            prompt = f"""
다음 고객 문의를 정확히 분류해주세요.

문의 내용: {text}

1차 분류 결과: {stage1_result.get("classification", {}).get("category", "기타")}

다음 카테고리 중 하나로 분류하세요:
- 배송: 배송 관련 문의
- 상품: 상품 정보, 품질 문의
- 교환: 상품 교환 요청
- 환불: 환불 요청
- 결제: 결제 관련 문의
- 1:1문의: 일반 문의
- 불만: 불만 사항
- 기타: 위에 해당하지 않는 경우

응답 형식: {{"category": "카테고리명", "confidence": 0.0-1.0, "reason": "분류 이유"}}
"""

            # 간단한 규칙 기반 LLM 시뮬레이션 (실제로는 OpenAI API 호출)
            llm_result = self._simulate_llm_classification(text, stage1_result)

            return llm_result

        except Exception as e:
            print(f"⚠️ LLM 분류 실패: {e}")
            # 폴백: 1차 결과 반환
            return {
                "category": stage1_result.get("classification", {}).get("category", "기타"),
                "confidence": stage1_result.get("classification", {}).get("confidence", 0.0),
                "reason": "LLM 분류 실패로 1차 결과 사용"
            }

    def _simulate_llm_classification(self, text: str, stage1_result: Dict) -> Dict[str, Any]:
        """LLM 분류 시뮬레이션 (실제 구현에서는 OpenAI API 사용)"""
        # 키워드 기반 고급 분류
        text_lower = text.lower()

        # 더 정교한 키워드 매핑
        advanced_keywords = {
            "배송": ["배송", "택배", "발송", "운송", "배달", "도착", "언제", "빨리", "운송장", "배송기사"],
            "상품": ["상품", "제품", "품질", "사이즈", "색상", "재질", "크기", "무게", "사양", "본체"],
            "교환": ["교환", "바꿔", "다른", "변경", "사이즈", "색깔", "다시", "재발송", "교체"],
            "환불": ["환불", "취소", "돌려", "반품", "환급", "되돌리기", "철회", "계좌", "돈"],
            "결제": ["결제", "카드", "계좌", "무통장", "입금", "결제수단", "할부", "포인트", "결제"],
            "1:1문의": ["문의", "질문", "궁금", "알고싶어", "확인", "체크", "문의드려", "연락"],
            "불만": ["불만", "화나", "짜증", "실망", "최악", "별로", "나쁘", "엉망", "답답"]
        }

        # 점수 계산
        category_scores = {}
        for category, keywords in advanced_keywords.items():
            score = 0
            matched_keywords = []
            for keyword in keywords:
                if keyword in text_lower:
                    score += 1
                    matched_keywords.append(keyword)

            if score > 0:
                # 키워드 밀도 고려
                density = score / len(text_lower.split())
                category_scores[category] = {
                    "score": score,
                    "density": density,
                    "final_score": score * 0.7 + density * 0.3,
                    "keywords": matched_keywords
                }

        if category_scores:
            # 최고 점수 카테고리 선택
            best_category = max(category_scores.items(), key=lambda x: x[1]["final_score"])
            category = best_category[0]
            score_info = best_category[1]

            # 신뢰도 계산 (0.6-0.95 범위)
            confidence = min(0.95, 0.6 + score_info["final_score"] * 0.35)

            return {
                "category": category,
                "confidence": confidence,
                "reason": f"키워드 매칭: {', '.join(score_info['keywords'][:3])}"
            }
        else:
            # 키워드 매칭 실패시 1차 결과 사용하되 신뢰도 약간 향상
            stage1_category = stage1_result.get("classification", {}).get("category", "기타")
            stage1_confidence = stage1_result.get("classification", {}).get("confidence", 0.0)

            return {
                "category": stage1_category,
                "confidence": min(0.8, stage1_confidence * 1.1),
                "reason": "LLM 보정 적용"
            }
    
    async def classify_batch(
        self, 
        texts: List[str], 
        use_hybrid: bool = True,
        progress_callback: Optional[Callable] = None
    ) -> List[Dict[str, Any]]:
        """배치 분류"""
        results = []
        
        for i, text in enumerate(texts, 1):
            try:
                result = await self.classify_single(text, use_hybrid)
                results.append(result)
                
                # 진행률 콜백
                if progress_callback:
                    progress_callback(i, len(texts))
                
            except Exception as e:
                print(f"⚠️ 배치 분류 실패 ({i}/{len(texts)}): {e}")
                results.append({
                    "category": "기타",
                    "confidence": 0.0,
                    "error": str(e)
                })
        
        return results
    
    def _update_stats(self, result: Dict, processing_time: float) -> None:
        """성능 통계 업데이트"""
        self.performance_stats["total_processed"] += 1
        
        # 평균 처리 시간 업데이트
        total = self.performance_stats["total_processed"]
        current_avg = self.performance_stats["average_processing_time"]
        self.performance_stats["average_processing_time"] = (
            (current_avg * (total - 1) + processing_time) / total
        )
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """성능 통계 조회"""
        total = self.performance_stats["total_processed"]
        if total == 0:
            return self.performance_stats
        
        stats = self.performance_stats.copy()
        stats["stage1_success_rate"] = (stats["stage1_success"] / total) * 100
        stats["stage2_success_rate"] = (stats["stage2_success"] / total) * 100
        
        return stats
    
    def reset_stats(self) -> None:
        """성능 통계 초기화"""
        self.performance_stats = {
            "total_processed": 0,
            "stage1_success": 0,
            "stage2_success": 0,
            "average_processing_time": 0.0
        }
    
    def configure_thresholds(self, **kwargs) -> None:
        """임계값 설정"""
        for key, value in kwargs.items():
            if key in self.confidence_thresholds:
                self.confidence_thresholds[key] = value
                print(f"✅ {key} 임계값을 {value}로 설정했습니다.")
    
    def get_scenario_config(self, scenario: str) -> Dict[str, float]:
        """시나리오별 설정 반환"""
        scenarios = {
            "high_accuracy": {
                "stage1_pass": 0.9,
                "stage2_pass": 0.95,
                "final_minimum": 0.8
            },
            "balanced": {
                "stage1_pass": 0.8,
                "stage2_pass": 0.9,
                "final_minimum": 0.6
            },
            "high_speed": {
                "stage1_pass": 0.6,
                "stage2_pass": 0.8,
                "final_minimum": 0.4
            },
            "conservative": {
                "stage1_pass": 0.95,
                "stage2_pass": 0.98,
                "final_minimum": 0.9
            }
        }
        
        return scenarios.get(scenario, scenarios["balanced"])
    
    def apply_scenario_config(self, scenario: str) -> bool:
        """시나리오별 설정 적용"""
        config = self.get_scenario_config(scenario)
        if config:
            self.confidence_thresholds.update(config)
            print(f"✅ '{scenario}' 시나리오 설정을 적용했습니다.")
            return True
        else:
            print(f"❌ 알 수 없는 시나리오: {scenario}")
            return False
