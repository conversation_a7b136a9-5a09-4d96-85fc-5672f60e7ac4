"""
리팩토링된 분석 서비스 - SOLID 원칙 적용
"""
import time
from typing import Dict
from datetime import datetime

from app.core.domain import QAPair, Grade
from app.core.interfaces import (
    IAnalysisService, IScoreCalculator, IQualityEvaluator, IFeedbackGenerator
)


class RefactoredAnalysisService(IAnalysisService):
    """
    리팩토링된 QA 분석 서비스
    
    SOLID 원칙 적용:
    - Single Responsibility: 분석 조정 및 결과 생성에만 집중
    - Open/Closed: 인터페이스를 통한 확장 가능
    - Liskov Substitution: 인터페이스 구현체들은 교체 가능
    - Interface Segregation: 각 기능별로 분리된 인터페이스 사용
    - Dependency Inversion: 구체 클래스가 아닌 인터페이스에 의존
    """
    
    def __init__(
        self,
        score_calculator: IScoreCalculator,
        quality_evaluator: IQualityEvaluator,
        feedback_generator: IFeedbackGenerator
    ):
        # 의존성 주입 (DIP)
        self.score_calculator = score_calculator
        self.quality_evaluator = quality_evaluator
        self.feedback_generator = feedback_generator
        
        # 분석 설정
        self.pass_threshold = 0.6
        self.grade_thresholds = {
            'A': 0.9, 'B': 0.8, 'C': 0.7, 'D': 0.6, 'F': 0.0
        }
        self.score_weights = {
            'semantic_similarity': 0.25,
            'topic_relevance': 0.25,
            'keyword_overlap': 0.15,
            'answer_completeness': 0.15,
            'answer_accuracy': 0.10,
            'answer_helpfulness': 0.10
        }
    
    def analyze_qa_pair(self, qa_id: str, question: str, answer: str) -> QAPair:
        """QA 쌍 분석 - 메인 메서드 (SRP 적용)"""
        start_time = time.time()
        
        try:
            # 각 전문 서비스에 위임
            scores = self._calculate_all_scores(question, answer)
            overall_score = self._calculate_overall_score(scores)
            grade = self._determine_grade(overall_score)
            pass_threshold = overall_score >= self.pass_threshold
            
            # 피드백 생성
            strengths, weaknesses, recommendations = self.feedback_generator.generate_feedback(
                question, answer, scores
            )
            
            # 처리 시간 계산
            processing_time = (time.time() - start_time) * 1000
            
            # QAPair 객체 생성
            return self._create_qa_pair(
                qa_id, question, answer, scores, overall_score, grade,
                pass_threshold, strengths, weaknesses, recommendations, processing_time
            )
            
        except Exception as e:
            print(f"❌ 분석 중 오류: {e}")
            return self._create_error_qa_pair(qa_id, question, answer, start_time)
    
    def _calculate_all_scores(self, question: str, answer: str) -> Dict[str, float]:
        """모든 점수 계산 (각 전문 서비스에 위임)"""
        # 점수 계산기에 위임
        semantic_similarity = self.score_calculator.calculate_semantic_similarity(question, answer)
        topic_relevance = self.score_calculator.calculate_topic_relevance(question, answer)
        keyword_overlap = self.score_calculator.calculate_keyword_overlap(question, answer)
        
        # 품질 평가기에 위임
        completeness, accuracy, helpfulness = self.quality_evaluator.evaluate_answer_quality(
            question, answer
        )
        
        return {
            'semantic_similarity': semantic_similarity,
            'topic_relevance': topic_relevance,
            'keyword_overlap': keyword_overlap,
            'answer_completeness': completeness,
            'answer_accuracy': accuracy,
            'answer_helpfulness': helpfulness
        }
    
    def _calculate_overall_score(self, scores: Dict[str, float]) -> float:
        """종합 점수 계산 (가중평균)"""
        return sum(scores[key] * self.score_weights[key] for key in scores.keys())
    
    def _determine_grade(self, score: float) -> Grade:
        """점수에 따른 등급 결정"""
        for grade, threshold in self.grade_thresholds.items():
            if score >= threshold:
                return Grade(grade)
        return Grade.F
    
    def _create_qa_pair(
        self, qa_id: str, question: str, answer: str, scores: Dict[str, float],
        overall_score: float, grade: Grade, pass_threshold: bool,
        strengths: list, weaknesses: list, recommendations: list, processing_time: float
    ) -> QAPair:
        """QAPair 객체 생성"""
        return QAPair(
            id=qa_id,
            question=question,
            answer=answer,
            semantic_similarity=scores['semantic_similarity'],
            topic_relevance=scores['topic_relevance'],
            keyword_overlap=scores['keyword_overlap'],
            answer_completeness=scores['answer_completeness'],
            answer_accuracy=scores['answer_accuracy'],
            answer_helpfulness=scores['answer_helpfulness'],
            overall_score=overall_score,
            grade=grade,
            pass_threshold=pass_threshold,
            strengths=strengths,
            weaknesses=weaknesses,
            recommendations=recommendations,
            analysis_timestamp=datetime.now(),
            processing_time_ms=processing_time,
            model_info={"service": "RefactoredAnalysisService", "version": "2.2.0"}
        )
    
    def _create_error_qa_pair(self, qa_id: str, question: str, answer: str, start_time: float) -> QAPair:
        """오류 발생 시 기본 QAPair 반환"""
        return QAPair(
            id=qa_id,
            question=question,
            answer=answer,
            weaknesses=["분석 실패"],
            recommendations=["다시 시도해주세요"],
            analysis_timestamp=datetime.now(),
            processing_time_ms=(time.time() - start_time) * 1000,
            model_info={"error": "analysis_failed"}
        )
