"""
분류 서비스 - 고객 질문 분류 및 셀러 답변 평가
"""
import re
import time
from typing import List, Tuple
from datetime import datetime

from app.core.classification_domain import (
    InquiryClassification, AnswerEvaluation, InquiryCategory, AnswerQuality
)
from app.models.embedding import EmbeddingModel


class ClassificationService:
    """분류 서비스 - 질문 분류 + 답변 평가"""
    
    def __init__(self):
        self.embedding_model = EmbeddingModel()
        
        # 카테고리별 키워드 매핑
        self.category_keywords = {
            InquiryCategory.SHIPPING: [
                '배송', '택배', '발송', '운송', '배달', '도착', '언제', '빨리',
                '운송장', '추적', '배송비', '무료배송', '당일배송', '익일배송'
            ],
            InquiryCategory.PRODUCT: [
                '상품', '제품', '품질', '사이즈', '색상', '재질', '크기', '무게',
                '사양', '스펙', '성능', '기능', '디자인', '모델', '브랜드'
            ],
            InquiryCategory.EXCHANGE: [
                '교환', '바꿔', '다른', '변경', '사이즈', '색깔', '다시', '재발송',
                '교체', '새로', '다른걸로', '바꾸고싶어'
            ],
            InquiryCategory.REFUND: [
                '환불', '취소', '돌려', '반품', '환급', '되돌리기', '철회',
                '계좌', '카드', '결제취소', '주문취소'
            ],
            InquiryCategory.PAYMENT: [
                '결제', '카드', '계좌', '무통장', '입금', '결제수단', '할부',
                '포인트', '적립금', '쿠폰', '할인', '가격', '금액'
            ],
            InquiryCategory.ONE_ON_ONE: [
                '문의', '질문', '궁금', '알고싶어', '확인', '체크', '문의드려',
                '여쭤', '물어', '답변', '연락'
            ],
            InquiryCategory.COMPLAINT: [
                '불만', '화나', '짜증', '실망', '최악', '별로', '나쁘', '엉망',
                '문제', '이상해', '잘못', '틀렸', '다르', '속았'
            ]
        }
        
        # 한국어 불용어
        self.stopwords = {
            '은', '는', '이', '가', '을', '를', '에', '의', '와', '과', '도', '만',
            '부터', '까지', '로', '으로', '에서', '께서', '한테', '안녕하세요',
            '감사합니다', '죄송합니다', '고객님', '문의', '요청', '부탁드립니다'
        }
    
    def _extract_keywords(self, text: str) -> List[str]:
        """키워드 추출"""
        if not text:
            return []
        
        # 한글, 영문, 숫자만 추출
        words = re.findall(r'[가-힣a-zA-Z0-9]+', text.lower())
        
        # 불용어 제거 및 길이 필터링
        keywords = [word for word in words 
                   if len(word) > 1 and word not in self.stopwords]
        
        return list(set(keywords))  # 중복 제거
    
    def _classify_inquiry_category(self, inquiry_content: str) -> Tuple[InquiryCategory, float, List[str]]:
        """고객 문의 카테고리 분류"""
        if not inquiry_content:
            return InquiryCategory.OTHER, 0.0, []
        
        keywords = self._extract_keywords(inquiry_content)
        category_scores = {}
        matched_keywords = []
        
        # 각 카테고리별 점수 계산
        for category, category_keywords in self.category_keywords.items():
            score = 0
            category_matches = []
            
            for keyword in keywords:
                for cat_keyword in category_keywords:
                    if keyword in cat_keyword or cat_keyword in keyword:
                        score += 1
                        category_matches.append(keyword)
            
            if score > 0:
                category_scores[category] = score / len(category_keywords)
                matched_keywords.extend(category_matches)
        
        # 최고 점수 카테고리 선택
        if category_scores:
            best_category = max(category_scores.items(), key=lambda x: x[1])
            return best_category[0], best_category[1], list(set(matched_keywords))
        else:
            return InquiryCategory.OTHER, 0.0, []
    
    def _generate_auto_tags(self, inquiry_content: str, category: InquiryCategory) -> List[str]:
        """자동 태그 생성"""
        tags = []
        
        # 카테고리 기반 태그
        tags.append(category.value)
        
        # 긴급도 태그
        urgent_keywords = ['급해', '빨리', '언제', '지금', '당장', '즉시']
        if any(keyword in inquiry_content for keyword in urgent_keywords):
            tags.append('긴급')
        
        # 감정 태그
        negative_keywords = ['화나', '짜증', '실망', '최악', '별로', '나쁘']
        if any(keyword in inquiry_content for keyword in negative_keywords):
            tags.append('부정적')
        
        positive_keywords = ['좋아', '만족', '감사', '훌륭', '최고']
        if any(keyword in inquiry_content for keyword in positive_keywords):
            tags.append('긍정적')
        
        # 복잡도 태그
        if len(inquiry_content) > 200:
            tags.append('상세문의')
        elif len(inquiry_content) < 50:
            tags.append('간단문의')
        
        return tags
    
    def _evaluate_answer_quality(self, inquiry_content: str, answer_content: str) -> Tuple[AnswerQuality, Dict[str, float]]:
        """셀러 답변 품질 평가"""
        if not answer_content or answer_content.strip().lower() in ["", "nan", "null"]:
            return AnswerQuality.POOR, {
                'completeness': 0.0, 'accuracy': 0.0, 
                'helpfulness': 0.0, 'politeness': 0.0, 'overall': 0.0
            }
        
        scores = {}
        
        # 1. 완성도 평가
        completeness = min(1.0, len(answer_content) / 150)  # 150자 기준
        
        # 구체적 정보 보너스
        specific_keywords = ["주문번호", "운송장", "연락드리겠습니다", "처리", "확인"]
        if any(word in answer_content for word in specific_keywords):
            completeness += 0.2
        
        scores['completeness'] = min(1.0, completeness)
        
        # 2. 정확성 평가 (키워드 매칭)
        inquiry_keywords = set(self._extract_keywords(inquiry_content))
        answer_keywords = set(self._extract_keywords(answer_content))
        
        if inquiry_keywords:
            accuracy = len(inquiry_keywords & answer_keywords) / len(inquiry_keywords)
        else:
            accuracy = 0.5
        
        scores['accuracy'] = accuracy
        
        # 3. 도움도 평가
        helpfulness = 0.3  # 기본 점수
        
        # 해결 지향적 표현
        solution_keywords = ["해결", "처리", "완료", "안내", "확인", "도움"]
        if any(word in answer_content for word in solution_keywords):
            helpfulness += 0.4
        
        # 추가 지원 제안
        support_keywords = ["고객센터", "문의", "연락", "게시판", "상담"]
        if any(word in answer_content for word in support_keywords):
            helpfulness += 0.3
        
        scores['helpfulness'] = min(1.0, helpfulness)
        
        # 4. 정중함 평가
        politeness = 0.2  # 기본 점수
        
        # 정중한 표현
        polite_keywords = ["안녕하세요", "감사합니다", "죄송합니다", "고객님"]
        politeness += sum(0.2 for word in polite_keywords if word in answer_content)
        
        scores['politeness'] = min(1.0, politeness)
        
        # 종합 점수
        overall = (
            scores['completeness'] * 0.3 +
            scores['accuracy'] * 0.3 +
            scores['helpfulness'] * 0.25 +
            scores['politeness'] * 0.15
        )
        scores['overall'] = overall
        
        # 품질 등급 결정
        if overall >= 0.8:
            quality = AnswerQuality.EXCELLENT
        elif overall >= 0.6:
            quality = AnswerQuality.GOOD
        elif overall >= 0.4:
            quality = AnswerQuality.AVERAGE
        else:
            quality = AnswerQuality.POOR
        
        return quality, scores
    
    def _generate_reuse_scenarios(self, category: InquiryCategory, quality: AnswerQuality) -> List[str]:
        """재사용 시나리오 생성"""
        if quality not in [AnswerQuality.EXCELLENT, AnswerQuality.GOOD]:
            return []
        
        scenarios = []
        
        if category == InquiryCategory.SHIPPING:
            scenarios = ["배송 지연 문의", "배송 추적 문의", "배송비 문의"]
        elif category == InquiryCategory.PRODUCT:
            scenarios = ["상품 정보 문의", "사이즈 문의", "품질 문의"]
        elif category == InquiryCategory.EXCHANGE:
            scenarios = ["교환 절차 안내", "교환 조건 문의"]
        elif category == InquiryCategory.REFUND:
            scenarios = ["환불 절차 안내", "환불 조건 문의"]
        elif category == InquiryCategory.PAYMENT:
            scenarios = ["결제 문제 해결", "결제 수단 문의"]
        
        return scenarios
    
    def classify_inquiry(self, id: str, inquiry_content: str, **metadata) -> InquiryClassification:
        """고객 문의 분류"""
        start_time = time.time()
        
        try:
            # 카테고리 분류
            primary_category, confidence, keywords = self._classify_inquiry_category(inquiry_content)
            
            # 자동 태그 생성
            auto_tags = self._generate_auto_tags(inquiry_content, primary_category)
            
            # 처리 시간 계산
            processing_time = (time.time() - start_time) * 1000
            
            return InquiryClassification(
                id=id,
                inquiry_content=inquiry_content,
                primary_category=primary_category,
                confidence_score=confidence,
                auto_tags=auto_tags,
                keywords=keywords,
                company_code=metadata.get('company_code'),
                mall_name=metadata.get('mall_name'),
                inquiry_at=metadata.get('inquiry_at'),
                classified_at=datetime.now(),
                processing_time_ms=processing_time,
                model_info=self.embedding_model.get_model_info()
            )
            
        except Exception as e:
            print(f"❌ 분류 중 오류: {e}")
            return InquiryClassification(
                id=id,
                inquiry_content=inquiry_content,
                primary_category=InquiryCategory.OTHER,
                confidence_score=0.0,
                classified_at=datetime.now(),
                processing_time_ms=(time.time() - start_time) * 1000
            )
    
    def evaluate_answer(self, id: str, inquiry_content: str, answer_content: str, **metadata) -> AnswerEvaluation:
        """셀러 답변 평가"""
        start_time = time.time()
        
        try:
            # 답변 품질 평가
            quality_grade, scores = self._evaluate_answer_quality(inquiry_content, answer_content)
            
            # 카테고리 분류 (재사용 시나리오용)
            category, _, _ = self._classify_inquiry_category(inquiry_content)
            
            # 재사용 추천 여부
            recommended = quality_grade in [AnswerQuality.EXCELLENT, AnswerQuality.GOOD]
            reuse_scenarios = self._generate_reuse_scenarios(category, quality_grade) if recommended else []
            
            # 피드백 생성
            strengths = []
            weaknesses = []
            improvements = []
            
            if scores['completeness'] > 0.7:
                strengths.append("답변이 충분히 상세함")
            else:
                weaknesses.append("답변이 너무 간단함")
                improvements.append("더 구체적이고 상세한 답변 제공")
            
            if scores['politeness'] > 0.7:
                strengths.append("정중하고 친절한 답변 톤")
            else:
                improvements.append("더 정중한 표현 사용")
            
            if scores['helpfulness'] > 0.7:
                strengths.append("고객에게 도움이 되는 답변")
            else:
                improvements.append("구체적인 해결 방안 제시")
            
            # 처리 시간 계산
            processing_time = (time.time() - start_time) * 1000
            
            return AnswerEvaluation(
                id=id,
                inquiry_content=inquiry_content,
                answer_content=answer_content,
                quality_grade=quality_grade,
                completeness_score=scores['completeness'],
                accuracy_score=scores['accuracy'],
                helpfulness_score=scores['helpfulness'],
                politeness_score=scores['politeness'],
                overall_score=scores['overall'],
                recommended_for_reuse=recommended,
                reuse_scenarios=reuse_scenarios,
                strengths=strengths,
                weaknesses=weaknesses,
                improvement_suggestions=improvements,
                company_code=metadata.get('company_code'),
                mall_name=metadata.get('mall_name'),
                answered_at=metadata.get('answered_at'),
                evaluated_at=datetime.now(),
                processing_time_ms=processing_time,
                model_info=self.embedding_model.get_model_info()
            )
            
        except Exception as e:
            print(f"❌ 평가 중 오류: {e}")
            return AnswerEvaluation(
                id=id,
                inquiry_content=inquiry_content,
                answer_content=answer_content,
                quality_grade=AnswerQuality.POOR,
                overall_score=0.0,
                recommended_for_reuse=False,
                weaknesses=["평가 실패"],
                improvement_suggestions=["다시 시도해주세요"],
                evaluated_at=datetime.now(),
                processing_time_ms=(time.time() - start_time) * 1000
            )
