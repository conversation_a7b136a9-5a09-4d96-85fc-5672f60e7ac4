"""
품질 평가 서비스 - Single Responsibility Principle 적용
"""
from typing import <PERSON>ple
from app.core.interfaces import IQualityEvaluator, IScoreCalculator


class QualityEvaluator(IQualityEvaluator):
    """답변 품질 평가 서비스 - 품질 평가에만 집중"""
    
    def __init__(self, score_calculator: IScoreCalculator):
        self.score_calculator = score_calculator
    
    def evaluate_answer_quality(self, question: str, answer: str) -> Tuple[float, float, float]:
        """답변 품질 평가 (완성도, 정확성, 도움도)"""
        if not answer or answer.strip().lower() in ["", "nan", "null"]:
            return 0.0, 0.0, 0.0
        
        completeness = self._evaluate_completeness(answer)
        accuracy = self._evaluate_accuracy(question, answer)
        helpfulness = self._evaluate_helpfulness(answer)
        
        return completeness, accuracy, helpfulness
    
    def _evaluate_completeness(self, answer: str) -> float:
        """완성도 평가"""
        # 기본 길이 점수 (200자 기준)
        completeness = min(1.0, len(answer) / 200)
        
        # 정중함 보너스
        politeness_words = ["안녕하세요", "감사합니다", "죄송합니다"]
        if any(word in answer for word in politeness_words):
            completeness += 0.1
        
        # 구체적 정보 보너스
        specific_info_words = ["주문번호", "운송장", "연락드리겠습니다", "처리"]
        if any(word in answer for word in specific_info_words):
            completeness += 0.2
        
        return min(1.0, completeness)
    
    def _evaluate_accuracy(self, question: str, answer: str) -> float:
        """정확성 평가"""
        # 주제 관련성을 정확성의 지표로 사용
        return self.score_calculator.calculate_topic_relevance(question, answer)
    
    def _evaluate_helpfulness(self, answer: str) -> float:
        """도움도 평가"""
        helpfulness = 0.5  # 기본 점수
        
        # 해결 지향적 표현
        solution_words = ["해결", "처리", "완료", "안내", "확인"]
        if any(word in answer for word in solution_words):
            helpfulness += 0.3
        
        # 추가 지원 제안
        support_words = ["고객센터", "문의", "연락", "게시판"]
        if any(word in answer for word in support_words):
            helpfulness += 0.2
        
        return min(1.0, helpfulness)
