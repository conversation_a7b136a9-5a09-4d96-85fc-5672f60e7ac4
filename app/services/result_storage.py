"""
결과 저장 및 비교 시스템 - 모델 비교 결과 영구 저장 및 히스토리 관리
"""
import json
import os
from pathlib import Path
from typing import Dict, List, Any, Optional
from datetime import datetime
import hashlib


class ResultStorageService:
    """
    결과 저장 서비스
    
    기능:
    - 모델 비교 결과 JSON 저장
    - 결과 히스토리 관리
    - 성능 트렌드 분석
    - 결과 검색 및 필터링
    - 결과 간 비교 분석
    """
    
    def __init__(self, storage_path: str = "outputs/results"):
        self.storage_path = Path(storage_path)
        self.storage_path.mkdir(parents=True, exist_ok=True)
        
        # 인덱스 파일 경로
        self.index_file = self.storage_path / "results_index.json"
        
        # 인덱스 로드
        self.results_index = self._load_index()
        
        print("✅ 결과 저장 서비스 초기화 완료")
    
    def save_comparison_result(
        self, 
        comparison_result: Dict[str, Any],
        tags: List[str] = None,
        description: str = ""
    ) -> str:
        """비교 결과 저장"""
        
        # 결과 ID 생성
        result_id = comparison_result.get("metadata", {}).get("comparison_id")
        if not result_id:
            result_id = f"comp_{int(datetime.now().timestamp())}"
        
        # 파일명 생성
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{result_id}_{timestamp}.json"
        filepath = self.storage_path / filename
        
        # 메타데이터 추가
        enhanced_result = {
            **comparison_result,
            "storage_metadata": {
                "result_id": result_id,
                "saved_at": datetime.now().isoformat(),
                "filename": filename,
                "tags": tags or [],
                "description": description,
                "file_hash": ""
            }
        }
        
        # 파일 저장
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(enhanced_result, f, ensure_ascii=False, indent=2, default=str)
            
            # 파일 해시 계산
            file_hash = self._calculate_file_hash(filepath)
            enhanced_result["storage_metadata"]["file_hash"] = file_hash
            
            # 다시 저장 (해시 포함)
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(enhanced_result, f, ensure_ascii=False, indent=2, default=str)
            
            # 인덱스 업데이트
            self._update_index(result_id, enhanced_result["storage_metadata"])
            
            print(f"✅ 비교 결과 저장 완료: {filepath}")
            return result_id
            
        except Exception as e:
            print(f"❌ 결과 저장 실패: {e}")
            return ""
    
    def load_comparison_result(self, result_id: str) -> Optional[Dict[str, Any]]:
        """저장된 비교 결과 로드"""
        
        # 인덱스에서 파일 정보 찾기
        result_info = self.results_index.get(result_id)
        if not result_info:
            print(f"❌ 결과 ID를 찾을 수 없습니다: {result_id}")
            return None
        
        filepath = self.storage_path / result_info["filename"]
        
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                result = json.load(f)
            
            print(f"✅ 결과 로드 완료: {result_id}")
            return result
            
        except Exception as e:
            print(f"❌ 결과 로드 실패: {e}")
            return None
    
    def list_results(
        self, 
        tags: List[str] = None,
        date_from: str = None,
        date_to: str = None,
        limit: int = 50
    ) -> List[Dict[str, Any]]:
        """결과 목록 조회"""
        
        results = []
        
        for result_id, info in self.results_index.items():
            # 태그 필터링
            if tags and not any(tag in info.get("tags", []) for tag in tags):
                continue
            
            # 날짜 필터링
            saved_at = info.get("saved_at", "")
            if date_from and saved_at < date_from:
                continue
            if date_to and saved_at > date_to:
                continue
            
            results.append({
                "result_id": result_id,
                **info
            })
        
        # 최신순 정렬
        results.sort(key=lambda x: x.get("saved_at", ""), reverse=True)
        
        return results[:limit]
    
    def compare_results(self, result_ids: List[str]) -> Dict[str, Any]:
        """여러 결과 간 비교 분석"""
        
        if len(result_ids) < 2:
            return {"error": "비교하려면 최소 2개의 결과가 필요합니다"}
        
        # 결과들 로드
        results = []
        for result_id in result_ids:
            result = self.load_comparison_result(result_id)
            if result:
                results.append(result)
        
        if len(results) < 2:
            return {"error": "유효한 결과가 부족합니다"}
        
        # 비교 분석
        comparison_analysis = {
            "comparison_metadata": {
                "compared_results": result_ids,
                "comparison_date": datetime.now().isoformat(),
                "total_results": len(results)
            },
            "model_performance_trends": self._analyze_performance_trends(results),
            "consistency_analysis": self._analyze_consistency(results),
            "improvement_analysis": self._analyze_improvements(results),
            "recommendations": self._generate_comparison_recommendations(results)
        }
        
        return comparison_analysis
    
    def get_performance_trends(
        self, 
        model_name: str = None,
        days: int = 30
    ) -> Dict[str, Any]:
        """성능 트렌드 분석"""
        
        # 최근 N일간의 결과 조회
        from datetime import timedelta
        date_from = (datetime.now() - timedelta(days=days)).isoformat()
        
        recent_results = self.list_results(date_from=date_from, limit=1000)
        
        if not recent_results:
            return {"error": "분석할 데이터가 없습니다"}
        
        # 각 결과 로드하여 트렌드 분석
        trend_data = []
        for result_info in recent_results:
            result = self.load_comparison_result(result_info["result_id"])
            if result:
                trend_data.append(result)
        
        return self._calculate_trends(trend_data, model_name)
    
    def search_results(self, query: str) -> List[Dict[str, Any]]:
        """결과 검색"""
        
        matching_results = []
        query_lower = query.lower()
        
        for result_id, info in self.results_index.items():
            # 설명, 태그, ID에서 검색
            if (query_lower in info.get("description", "").lower() or
                query_lower in result_id.lower() or
                any(query_lower in tag.lower() for tag in info.get("tags", []))):
                
                matching_results.append({
                    "result_id": result_id,
                    **info
                })
        
        return matching_results
    
    def delete_result(self, result_id: str) -> bool:
        """결과 삭제"""
        
        result_info = self.results_index.get(result_id)
        if not result_info:
            print(f"❌ 결과를 찾을 수 없습니다: {result_id}")
            return False
        
        filepath = self.storage_path / result_info["filename"]
        
        try:
            # 파일 삭제
            if filepath.exists():
                filepath.unlink()
            
            # 인덱스에서 제거
            del self.results_index[result_id]
            self._save_index()
            
            print(f"✅ 결과 삭제 완료: {result_id}")
            return True
            
        except Exception as e:
            print(f"❌ 결과 삭제 실패: {e}")
            return False
    
    def export_results_summary(self, output_path: str = None) -> str:
        """결과 요약 내보내기"""
        
        if not output_path:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_path = f"outputs/results_summary_{timestamp}.json"
        
        summary = {
            "export_metadata": {
                "exported_at": datetime.now().isoformat(),
                "total_results": len(self.results_index)
            },
            "results_index": self.results_index,
            "statistics": self._calculate_overall_statistics()
        }
        
        try:
            output_file = Path(output_path)
            output_file.parent.mkdir(parents=True, exist_ok=True)
            
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(summary, f, ensure_ascii=False, indent=2, default=str)
            
            print(f"✅ 결과 요약 내보내기 완료: {output_file}")
            return str(output_file)
            
        except Exception as e:
            print(f"❌ 결과 요약 내보내기 실패: {e}")
            return ""
    
    def _load_index(self) -> Dict[str, Any]:
        """인덱스 파일 로드"""
        
        if not self.index_file.exists():
            return {}
        
        try:
            with open(self.index_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"⚠️ 인덱스 로드 실패: {e}")
            return {}
    
    def _save_index(self):
        """인덱스 파일 저장"""
        
        try:
            with open(self.index_file, 'w', encoding='utf-8') as f:
                json.dump(self.results_index, f, ensure_ascii=False, indent=2, default=str)
        except Exception as e:
            print(f"⚠️ 인덱스 저장 실패: {e}")
    
    def _update_index(self, result_id: str, metadata: Dict[str, Any]):
        """인덱스 업데이트"""
        
        self.results_index[result_id] = metadata
        self._save_index()
    
    def _calculate_file_hash(self, filepath: Path) -> str:
        """파일 해시 계산"""
        
        try:
            with open(filepath, 'rb') as f:
                content = f.read()
            return hashlib.md5(content).hexdigest()
        except Exception:
            return ""
    
    def _analyze_performance_trends(self, results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """성능 트렌드 분석"""
        
        trends = {}
        
        for result in results:
            timestamp = result.get("metadata", {}).get("timestamp", "")
            performance_metrics = result.get("performance_metrics", {})
            
            for model_name, metrics in performance_metrics.items():
                if model_name not in trends:
                    trends[model_name] = []
                
                trends[model_name].append({
                    "timestamp": timestamp,
                    "avg_score": metrics.get("avg_score", 0),
                    "avg_processing_time": metrics.get("avg_processing_time", 0),
                    "pass_rate": metrics.get("pass_rate", 0)
                })
        
        # 각 모델별 트렌드 정렬
        for model_name in trends:
            trends[model_name].sort(key=lambda x: x["timestamp"])
        
        return trends
    
    def _analyze_consistency(self, results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """일관성 분석"""
        
        consistency_data = {}
        
        for result in results:
            performance_metrics = result.get("performance_metrics", {})
            
            for model_name, metrics in performance_metrics.items():
                if model_name not in consistency_data:
                    consistency_data[model_name] = []
                
                consistency_data[model_name].append(metrics.get("avg_score", 0))
        
        # 각 모델별 일관성 계산 (표준편차)
        consistency_analysis = {}
        for model_name, scores in consistency_data.items():
            if len(scores) > 1:
                import statistics
                mean_score = statistics.mean(scores)
                std_dev = statistics.stdev(scores)
                consistency_score = 1 / (1 + std_dev)  # 표준편차가 낮을수록 일관성 높음
                
                consistency_analysis[model_name] = {
                    "mean_score": mean_score,
                    "std_deviation": std_dev,
                    "consistency_score": consistency_score,
                    "sample_count": len(scores)
                }
        
        return consistency_analysis
    
    def _analyze_improvements(self, results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """개선 분석"""
        
        if len(results) < 2:
            return {}
        
        # 시간순 정렬
        sorted_results = sorted(results, key=lambda x: x.get("metadata", {}).get("timestamp", ""))
        
        first_result = sorted_results[0]
        last_result = sorted_results[-1]
        
        improvements = {}
        
        first_metrics = first_result.get("performance_metrics", {})
        last_metrics = last_result.get("performance_metrics", {})
        
        for model_name in set(first_metrics.keys()) & set(last_metrics.keys()):
            first_score = first_metrics[model_name].get("avg_score", 0)
            last_score = last_metrics[model_name].get("avg_score", 0)
            
            improvement = last_score - first_score
            improvement_percent = (improvement / first_score * 100) if first_score > 0 else 0
            
            improvements[model_name] = {
                "first_score": first_score,
                "last_score": last_score,
                "improvement": improvement,
                "improvement_percent": improvement_percent
            }
        
        return improvements
    
    def _generate_comparison_recommendations(self, results: List[Dict[str, Any]]) -> List[str]:
        """비교 기반 추천 생성"""
        
        recommendations = []
        
        # 성능 트렌드 기반 추천
        trends = self._analyze_performance_trends(results)
        if trends:
            best_trending_model = max(trends.keys(), 
                                    key=lambda k: trends[k][-1]["avg_score"] if trends[k] else 0)
            recommendations.append(f"최근 성능이 가장 좋은 모델: {best_trending_model}")
        
        # 일관성 기반 추천
        consistency = self._analyze_consistency(results)
        if consistency:
            most_consistent_model = max(consistency.keys(), 
                                      key=lambda k: consistency[k]["consistency_score"])
            recommendations.append(f"가장 일관성 있는 모델: {most_consistent_model}")
        
        # 개선 기반 추천
        improvements = self._analyze_improvements(results)
        if improvements:
            most_improved_model = max(improvements.keys(), 
                                    key=lambda k: improvements[k]["improvement"])
            if improvements[most_improved_model]["improvement"] > 0:
                recommendations.append(f"가장 많이 개선된 모델: {most_improved_model}")
        
        return recommendations
    
    def _calculate_trends(self, trend_data: List[Dict[str, Any]], model_name: str = None) -> Dict[str, Any]:
        """트렌드 계산"""
        
        if not trend_data:
            return {"error": "트렌드 데이터가 없습니다"}
        
        # 모델별 성능 데이터 수집
        model_trends = {}
        
        for result in trend_data:
            timestamp = result.get("metadata", {}).get("timestamp", "")
            performance_metrics = result.get("performance_metrics", {})
            
            for m_name, metrics in performance_metrics.items():
                if model_name and m_name != model_name:
                    continue
                
                if m_name not in model_trends:
                    model_trends[m_name] = []
                
                model_trends[m_name].append({
                    "timestamp": timestamp,
                    "avg_score": metrics.get("avg_score", 0),
                    "avg_processing_time": metrics.get("avg_processing_time", 0),
                    "pass_rate": metrics.get("pass_rate", 0)
                })
        
        # 트렌드 분석
        trend_analysis = {}
        for m_name, data_points in model_trends.items():
            if len(data_points) >= 2:
                # 시간순 정렬
                data_points.sort(key=lambda x: x["timestamp"])
                
                # 선형 트렌드 계산 (간단한 기울기)
                first_score = data_points[0]["avg_score"]
                last_score = data_points[-1]["avg_score"]
                trend_slope = (last_score - first_score) / len(data_points)
                
                trend_analysis[m_name] = {
                    "data_points": data_points,
                    "trend_slope": trend_slope,
                    "trend_direction": "improving" if trend_slope > 0 else "declining" if trend_slope < 0 else "stable",
                    "first_score": first_score,
                    "last_score": last_score,
                    "sample_count": len(data_points)
                }
        
        return trend_analysis
    
    def _calculate_overall_statistics(self) -> Dict[str, Any]:
        """전체 통계 계산"""
        
        total_results = len(self.results_index)
        
        # 태그별 통계
        tag_counts = {}
        for info in self.results_index.values():
            for tag in info.get("tags", []):
                tag_counts[tag] = tag_counts.get(tag, 0) + 1
        
        # 월별 통계
        monthly_counts = {}
        for info in self.results_index.values():
            saved_at = info.get("saved_at", "")
            if saved_at:
                month = saved_at[:7]  # YYYY-MM
                monthly_counts[month] = monthly_counts.get(month, 0) + 1
        
        return {
            "total_results": total_results,
            "tag_distribution": tag_counts,
            "monthly_distribution": monthly_counts,
            "storage_path": str(self.storage_path),
            "index_file_size": self.index_file.stat().st_size if self.index_file.exists() else 0
        }
    
    def get_storage_info(self) -> Dict[str, Any]:
        """저장소 정보"""
        
        return {
            "storage_path": str(self.storage_path),
            "total_results": len(self.results_index),
            "index_file": str(self.index_file),
            "storage_size_mb": sum(f.stat().st_size for f in self.storage_path.glob("*.json")) / (1024 * 1024),
            "available_operations": [
                "save_comparison_result",
                "load_comparison_result", 
                "list_results",
                "compare_results",
                "get_performance_trends",
                "search_results",
                "delete_result",
                "export_results_summary"
            ]
        }
