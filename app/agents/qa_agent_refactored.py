"""
리팩토링된 QA 분석 에이전트 - SOLID 원칙 적용
"""
import asyncio
import time
import uuid
from typing import List, Optional, Dict, Any
from datetime import datetime

from app.core.domain import QAPair, AnalysisRequest, BatchResult
from app.core.interfaces import IQAAgent, IAnalysisService, IDatabaseService, IExportService
from app.services.service_factory import ServiceFactory


class RefactoredQAAnalysisAgent(IQAAgent):
    """
    리팩토링된 QA 분석 에이전트
    
    SOLID 원칙 적용:
    - Single Responsibility: QA 분석 조정에만 집중
    - Open/Closed: 인터페이스를 통한 확장 가능
    - Liskov Substitution: 서비스 구현체들은 교체 가능
    - Interface Segregation: 각 기능별로 분리된 인터페이스 사용
    - Dependency Inversion: 구체 클래스가 아닌 인터페이스에 의존
    """
    
    def __init__(
        self,
        analysis_service: IAnalysisService,
        database_service: IDatabaseService,
        export_service: Optional[IExportService] = None
    ):
        # 의존성 주입 (DIP)
        self.analysis_service = analysis_service
        self.database_service = database_service
        self.export_service = export_service
        
        print("✅ 리팩토링된 QA 분석 에이전트 v2.2 초기화 완료")
    
    @classmethod
    def create_with_factory(
        cls, 
        supabase_url: str = None, 
        supabase_key: str = None
    ) -> 'RefactoredQAAnalysisAgent':
        """팩토리를 사용한 에이전트 생성"""
        factory = ServiceFactory(supabase_url, supabase_key)
        
        analysis_service = factory.get_analysis_service()
        database_service = factory.get_database_service()
        
        return cls(
            analysis_service=analysis_service,
            database_service=database_service
        )
    
    async def analyze_single_qa(self, request: AnalysisRequest) -> QAPair:
        """단일 QA 분석 (SRP 적용)"""
        try:
            # 데이터 준비
            qa_data = await self._prepare_qa_data(request)
            
            # 분석 실행 (분석 서비스에 위임)
            result = self.analysis_service.analyze_qa_pair(
                qa_data['id'], qa_data['question'], qa_data['answer']
            )
            
            return result
            
        except Exception as e:
            print(f"❌ 단일 QA 분석 실패: {e}")
            return self._create_error_qa_pair(request, str(e))
    
    async def analyze_batch(self, requests: List[AnalysisRequest]) -> BatchResult:
        """배치 분석 (SRP 적용)"""
        start_time = time.time()
        results = []
        
        print(f"📊 {len(requests)}건의 QA 쌍 분석 시작...")
        
        for i, request in enumerate(requests, 1):
            try:
                result = await self.analyze_single_qa(request)
                results.append(result)
                print(f"  {i}/{len(requests)} 완료")
            except Exception as e:
                print(f"  {i}/{len(requests)} 실패: {e}")
                continue
        
        return self._create_batch_result(results, start_time)
    
    async def analyze_sample_data(self, limit: int = 20) -> BatchResult:
        """샘플 데이터 분석 (SRP 적용)"""
        print(f"📊 {limit}건의 문의 분석 시작...")

        # DB에서 실제 데이터 가져오기 (데이터베이스 서비스에 위임)
        sample_data = await self.database_service.get_sample_inquiries(limit)

        if not sample_data:
            print("⚠️ DB에서 데이터를 가져올 수 없습니다. 테스트 데이터를 사용합니다.")
            sample_data = self._create_test_data(limit)

        # AnalysisRequest 생성
        requests = self._convert_to_analysis_requests(sample_data)
        
        print(f"📋 {len(requests)}건의 요청 생성 완료")
        return await self.analyze_batch(requests)
    
    def export_results(self, batch_result: BatchResult) -> bool:
        """결과 내보내기 (Export Service에 위임)"""
        if self.export_service:
            return self.export_service.export_results(batch_result)
        else:
            # 기본 내보내기 로직
            return self._default_export(batch_result)
    
    def get_agent_info(self) -> Dict[str, Any]:
        """에이전트 정보"""
        return {
            "agent_type": "Refactored QA Analysis Agent v2.2",
            "version": "2.2.0",
            "architecture": "SOLID Principles Applied",
            "db_connected": self.database_service.is_connected(),
            "services": {
                "analysis": type(self.analysis_service).__name__,
                "database": type(self.database_service).__name__,
                "export": type(self.export_service).__name__ if self.export_service else None
            }
        }
    
    async def _prepare_qa_data(self, request: AnalysisRequest) -> Dict[str, str]:
        """QA 데이터 준비"""
        if request.inquiry_id and not request.question_text:
            # DB에서 조회 (데이터베이스 서비스에 위임)
            inquiry_data = await self.database_service.get_inquiry_by_id(request.inquiry_id)
            if inquiry_data:
                subject = inquiry_data.get('subject', '') or ''
                content = inquiry_data.get('content', '') or ''
                question = f"{subject} {content}".strip()
                answer = inquiry_data.get('answer', '') or ''
                qa_id = request.inquiry_id
            else:
                raise Exception(f"문의 ID {request.inquiry_id}를 찾을 수 없습니다")
        else:
            # 직접 입력
            question = request.question_text or ""
            answer = request.answer_text or ""
            qa_id = request.inquiry_id or str(uuid.uuid4())
        
        return {
            'id': qa_id,
            'question': question,
            'answer': answer
        }
    
    @staticmethod
    def _create_batch_result(results: List[QAPair], start_time: float) -> BatchResult:
        """배치 결과 생성"""
        total_count = len(results)
        pass_count = sum(1 for r in results if r.pass_threshold)
        average_score = sum(r.overall_score for r in results) / total_count if total_count > 0 else 0.0
        
        # 등급 분포
        grade_distribution = {}
        for result in results:
            grade = result.grade.value
            grade_distribution[grade] = grade_distribution.get(grade, 0) + 1
        
        processing_time = time.time() - start_time
        
        return BatchResult(
            total_count=total_count,
            pass_count=pass_count,
            average_score=average_score,
            grade_distribution=grade_distribution,
            results=results,
            processing_time_seconds=processing_time
        )
    
    def _create_test_data(self, limit: int) -> List[Dict[str, Any]]:
        """테스트 데이터 생성"""
        return [
            {
                'id': f'test_{i}',
                'question': f'테스트 질문 {i}',
                'answer': f'테스트 답변 {i}'
            }
            for i in range(min(limit, 5))
        ]
    
    def _convert_to_analysis_requests(self, sample_data: List[Dict[str, Any]]) -> List[AnalysisRequest]:
        """샘플 데이터를 AnalysisRequest로 변환"""
        requests = []
        for item in sample_data:
            question_text = item.get('question') or item.get('subject') or item.get('content') or ''
            answer_text = item.get('answer') or ''

            request = AnalysisRequest(
                inquiry_id=item.get('id'),
                question_text=question_text,
                answer_text=answer_text
            )
            requests.append(request)
        
        return requests
    
    def _create_error_qa_pair(self, request: AnalysisRequest, error_msg: str) -> QAPair:
        """오류 발생 시 기본 QAPair 반환"""
        return QAPair(
            id=request.inquiry_id or "error",
            question=request.question_text or "",
            answer=request.answer_text or "",
            weaknesses=[f"분석 실패: {error_msg}"],
            recommendations=["다시 시도해주세요"],
            analysis_timestamp=datetime.now(),
            processing_time_ms=0.0,
            model_info={"error": "analysis_failed"}
        )
    
    @staticmethod
    def _default_export(batch_result: BatchResult) -> bool:
        """기본 내보내기 로직"""
        try:
            # 간단한 JSON 파일로 저장
            import json
            from pathlib import Path
            
            output_dir = Path("outputs/results")
            output_dir.mkdir(parents=True, exist_ok=True)
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"qa_analysis_results_{timestamp}.json"
            filepath = output_dir / filename
            
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(batch_result.to_dict(), f, ensure_ascii=False, indent=2)
            
            print(f"✅ 결과 저장 완료: {filepath}")
            return True
            
        except Exception as e:
            print(f"❌ 결과 저장 실패: {e}")
            return False
