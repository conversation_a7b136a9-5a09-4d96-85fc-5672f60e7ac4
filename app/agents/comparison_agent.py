"""
비교 에이전트 - 모델 성능 비교 및 벤치마크에 특화
"""
import time
import statistics
from typing import Dict, List, Any
from datetime import datetime

from app.core.interfaces import IComparisonAgent, IQAAnalysisAgent, IClassificationAgent, ISentimentAgent
from app.core.domain import AnalysisRequest


class ComparisonAgent(IComparisonAgent):
    """
    모델 비교 에이전트
    
    역할:
    - 여러 모델의 성능 비교
    - 벤치마크 테스트 실행
    - 성능 리포트 생성
    - 모델 추천
    """
    
    def __init__(self):
        self.comparison_history = []
        print("✅ 비교 에이전트 초기화 완료")
    
    async def compare_models(self, requests: List[AnalysisRequest]) -> Dict[str, Any]:
        """모델 성능 비교"""
        if not requests:
            return {"error": "비교할 요청이 없습니다"}
        
        print(f"🔍 {len(requests)}건의 요청으로 모델 성능 비교 시작...")
        
        comparison_results = {
            "metadata": {
                "comparison_id": f"comp_{int(time.time())}",
                "timestamp": datetime.now().isoformat(),
                "total_requests": len(requests),
                "models_compared": []
            },
            "results": {},
            "summary": {},
            "recommendations": []
        }
        
        # 여기서는 예시로 다양한 모델 설정을 시뮬레이션
        model_configs = [
            {"name": "korean_ecommerce_optimized", "description": "한국어 이커머스 최적화"},
            {"name": "high_performance", "description": "고성능 분석"},
            {"name": "lightweight_fast", "description": "경량 고속"},
            {"name": "sentiment_specialized", "description": "감성 분석 특화"}
        ]
        
        comparison_results["metadata"]["models_compared"] = [m["name"] for m in model_configs]
        
        # 각 모델별 성능 측정 (시뮬레이션)
        for model_config in model_configs:
            model_name = model_config["name"]
            print(f"  📊 {model_name} 모델 테스트 중...")
            
            model_results = await self._test_model_performance(model_name, requests)
            comparison_results["results"][model_name] = model_results
        
        # 비교 요약 생성
        comparison_results["summary"] = self._generate_comparison_summary(comparison_results["results"])
        
        # 추천 생성
        comparison_results["recommendations"] = self._generate_recommendations(comparison_results["results"])
        
        # 히스토리에 저장
        self.comparison_history.append(comparison_results)
        
        return comparison_results
    
    async def benchmark_models(self, test_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """모델 벤치마크"""
        print(f"🏁 {len(test_data)}건의 테스트 데이터로 벤치마크 시작...")
        
        benchmark_results = {
            "metadata": {
                "benchmark_id": f"bench_{int(time.time())}",
                "timestamp": datetime.now().isoformat(),
                "test_data_count": len(test_data),
                "test_categories": self._analyze_test_data_categories(test_data)
            },
            "performance_metrics": {},
            "accuracy_metrics": {},
            "efficiency_metrics": {},
            "overall_ranking": [],
            "detailed_results": {}
        }
        
        # 벤치마크 실행 (시뮬레이션)
        models = ["korean_ecommerce_optimized", "high_performance", "lightweight_fast"]
        
        for model_name in models:
            print(f"  🧪 {model_name} 벤치마크 실행 중...")
            
            # 성능 메트릭 측정
            performance = await self._measure_performance_metrics(model_name, test_data)
            benchmark_results["performance_metrics"][model_name] = performance
            
            # 정확도 메트릭 측정
            accuracy = await self._measure_accuracy_metrics(model_name, test_data)
            benchmark_results["accuracy_metrics"][model_name] = accuracy
            
            # 효율성 메트릭 측정
            efficiency = await self._measure_efficiency_metrics(model_name, test_data)
            benchmark_results["efficiency_metrics"][model_name] = efficiency
        
        # 전체 순위 계산
        benchmark_results["overall_ranking"] = self._calculate_overall_ranking(benchmark_results)
        
        return benchmark_results
    
    def generate_comparison_report(self, results: Dict[str, Any]) -> str:
        """비교 리포트 생성"""
        if not results or "results" not in results:
            return "❌ 비교 결과가 없습니다."
        
        report_lines = []
        report_lines.append("=" * 80)
        report_lines.append("📊 모델 성능 비교 리포트")
        report_lines.append("=" * 80)
        
        # 메타데이터
        metadata = results.get("metadata", {})
        report_lines.append(f"🆔 비교 ID: {metadata.get('comparison_id', 'N/A')}")
        report_lines.append(f"📅 실행 시간: {metadata.get('timestamp', 'N/A')}")
        report_lines.append(f"📋 총 요청 수: {metadata.get('total_requests', 0)}건")
        report_lines.append(f"🤖 비교 모델: {', '.join(metadata.get('models_compared', []))}")
        report_lines.append("")
        
        # 모델별 결과
        model_results = results.get("results", {})
        for model_name, model_data in model_results.items():
            report_lines.append(f"🔍 {model_name}")
            report_lines.append("-" * 40)
            report_lines.append(f"  ⏱️ 평균 처리 시간: {model_data.get('avg_processing_time', 0):.2f}ms")
            report_lines.append(f"  📊 평균 점수: {model_data.get('avg_score', 0):.3f}")
            report_lines.append(f"  ✅ 통과율: {model_data.get('pass_rate', 0):.1f}%")
            report_lines.append(f"  🎯 정확도: {model_data.get('accuracy', 0):.3f}")
            report_lines.append("")
        
        # 요약
        summary = results.get("summary", {})
        if summary:
            report_lines.append("📈 비교 요약")
            report_lines.append("-" * 40)
            report_lines.append(f"🏆 최고 성능: {summary.get('best_performance', 'N/A')}")
            report_lines.append(f"⚡ 최고 속도: {summary.get('fastest', 'N/A')}")
            report_lines.append(f"🎯 최고 정확도: {summary.get('most_accurate', 'N/A')}")
            report_lines.append("")
        
        # 추천
        recommendations = results.get("recommendations", [])
        if recommendations:
            report_lines.append("💡 추천사항")
            report_lines.append("-" * 40)
            for i, rec in enumerate(recommendations, 1):
                report_lines.append(f"  {i}. {rec}")
            report_lines.append("")
        
        report_lines.append("=" * 80)
        
        return "\n".join(report_lines)
    
    async def _test_model_performance(self, model_name: str, requests: List[AnalysisRequest]) -> Dict[str, Any]:
        """모델 성능 테스트 (시뮬레이션)"""
        # 실제로는 여기서 각 모델을 실행하고 결과를 측정
        # 지금은 시뮬레이션 데이터 생성
        
        processing_times = []
        scores = []
        pass_count = 0
        
        for request in requests:
            # 모델별 특성을 반영한 시뮬레이션
            if model_name == "korean_ecommerce_optimized":
                processing_time = 150 + (time.time() % 50)  # 150-200ms
                score = 0.75 + (time.time() % 0.2)  # 0.75-0.95
            elif model_name == "high_performance":
                processing_time = 200 + (time.time() % 100)  # 200-300ms
                score = 0.80 + (time.time() % 0.15)  # 0.80-0.95
            elif model_name == "lightweight_fast":
                processing_time = 80 + (time.time() % 40)  # 80-120ms
                score = 0.65 + (time.time() % 0.25)  # 0.65-0.90
            else:  # sentiment_specialized
                processing_time = 120 + (time.time() % 60)  # 120-180ms
                score = 0.70 + (time.time() % 0.20)  # 0.70-0.90
            
            processing_times.append(processing_time)
            scores.append(score)
            if score >= 0.6:
                pass_count += 1
        
        return {
            "avg_processing_time": statistics.mean(processing_times),
            "min_processing_time": min(processing_times),
            "max_processing_time": max(processing_times),
            "avg_score": statistics.mean(scores),
            "min_score": min(scores),
            "max_score": max(scores),
            "pass_rate": (pass_count / len(requests)) * 100,
            "total_requests": len(requests),
            "accuracy": statistics.mean(scores)  # 간단한 정확도 지표
        }
    
    async def _measure_performance_metrics(self, model_name: str, test_data: List[Dict[str, Any]]) -> Dict[str, float]:
        """성능 메트릭 측정"""
        # 시뮬레이션 데이터
        base_metrics = {
            "korean_ecommerce_optimized": {"throughput": 85.0, "latency": 150.0, "memory_usage": 512.0},
            "high_performance": {"throughput": 95.0, "latency": 200.0, "memory_usage": 1024.0},
            "lightweight_fast": {"throughput": 120.0, "latency": 80.0, "memory_usage": 256.0}
        }
        
        return base_metrics.get(model_name, {"throughput": 70.0, "latency": 180.0, "memory_usage": 400.0})
    
    async def _measure_accuracy_metrics(self, model_name: str, test_data: List[Dict[str, Any]]) -> Dict[str, float]:
        """정확도 메트릭 측정"""
        # 시뮬레이션 데이터
        base_metrics = {
            "korean_ecommerce_optimized": {"precision": 0.85, "recall": 0.82, "f1_score": 0.835},
            "high_performance": {"precision": 0.90, "recall": 0.88, "f1_score": 0.89},
            "lightweight_fast": {"precision": 0.78, "recall": 0.75, "f1_score": 0.765}
        }
        
        return base_metrics.get(model_name, {"precision": 0.75, "recall": 0.72, "f1_score": 0.735})
    
    async def _measure_efficiency_metrics(self, model_name: str, test_data: List[Dict[str, Any]]) -> Dict[str, float]:
        """효율성 메트릭 측정"""
        # 시뮬레이션 데이터
        base_metrics = {
            "korean_ecommerce_optimized": {"cpu_usage": 65.0, "gpu_usage": 45.0, "cost_per_request": 0.002},
            "high_performance": {"cpu_usage": 85.0, "gpu_usage": 70.0, "cost_per_request": 0.005},
            "lightweight_fast": {"cpu_usage": 35.0, "gpu_usage": 20.0, "cost_per_request": 0.001}
        }
        
        return base_metrics.get(model_name, {"cpu_usage": 50.0, "gpu_usage": 30.0, "cost_per_request": 0.003})
    
    def _analyze_test_data_categories(self, test_data: List[Dict[str, Any]]) -> Dict[str, int]:
        """테스트 데이터 카테고리 분석"""
        categories = {}
        for item in test_data:
            category = item.get("category", "기타")
            categories[category] = categories.get(category, 0) + 1
        return categories
    
    def _generate_comparison_summary(self, results: Dict[str, Any]) -> Dict[str, str]:
        """비교 요약 생성"""
        if not results:
            return {}
        
        # 최고 성능 모델 찾기
        best_score_model = max(results.keys(), key=lambda k: results[k].get("avg_score", 0))
        fastest_model = min(results.keys(), key=lambda k: results[k].get("avg_processing_time", float('inf')))
        most_accurate_model = max(results.keys(), key=lambda k: results[k].get("accuracy", 0))
        
        return {
            "best_performance": best_score_model,
            "fastest": fastest_model,
            "most_accurate": most_accurate_model
        }
    
    def _generate_recommendations(self, results: Dict[str, Any]) -> List[str]:
        """추천사항 생성"""
        recommendations = []
        
        if not results:
            return ["비교 결과가 없습니다."]
        
        # 성능 기반 추천
        best_model = max(results.keys(), key=lambda k: results[k].get("avg_score", 0))
        recommendations.append(f"전체적인 성능이 가장 우수한 모델: {best_model}")
        
        # 속도 기반 추천
        fastest_model = min(results.keys(), key=lambda k: results[k].get("avg_processing_time", float('inf')))
        recommendations.append(f"처리 속도가 가장 빠른 모델: {fastest_model}")
        
        # 사용 시나리오별 추천
        recommendations.append("실시간 처리가 중요한 경우: lightweight_fast 모델 추천")
        recommendations.append("정확도가 최우선인 경우: high_performance 모델 추천")
        recommendations.append("한국어 이커머스 특화: korean_ecommerce_optimized 모델 추천")
        
        return recommendations
    
    def _calculate_overall_ranking(self, benchmark_results: Dict[str, Any]) -> List[Dict[str, Any]]:
        """전체 순위 계산"""
        models = list(benchmark_results.get("performance_metrics", {}).keys())
        rankings = []
        
        for model in models:
            # 종합 점수 계산 (가중 평균)
            performance = benchmark_results["performance_metrics"].get(model, {})
            accuracy = benchmark_results["accuracy_metrics"].get(model, {})
            efficiency = benchmark_results["efficiency_metrics"].get(model, {})
            
            # 정규화된 점수 계산 (0-1 범위)
            throughput_score = min(1.0, performance.get("throughput", 0) / 150.0)
            latency_score = max(0.0, 1.0 - performance.get("latency", 200) / 300.0)
            f1_score = accuracy.get("f1_score", 0)
            efficiency_score = max(0.0, 1.0 - efficiency.get("cpu_usage", 50) / 100.0)
            
            # 가중 평균 (성능 30%, 정확도 40%, 효율성 30%)
            overall_score = (
                (throughput_score + latency_score) / 2 * 0.3 +
                f1_score * 0.4 +
                efficiency_score * 0.3
            )
            
            rankings.append({
                "model": model,
                "overall_score": overall_score,
                "performance_score": (throughput_score + latency_score) / 2,
                "accuracy_score": f1_score,
                "efficiency_score": efficiency_score
            })
        
        # 점수 순으로 정렬
        rankings.sort(key=lambda x: x["overall_score"], reverse=True)
        
        return rankings
    
    def get_agent_info(self) -> Dict[str, Any]:
        """에이전트 정보"""
        return {
            "agent_type": "Comparison Agent",
            "version": "1.0.0",
            "specialization": "모델 성능 비교 및 벤치마크",
            "capabilities": [
                "모델 성능 비교",
                "벤치마크 테스트",
                "성능 리포트 생성",
                "모델 추천"
            ],
            "comparison_history_count": len(self.comparison_history)
        }
