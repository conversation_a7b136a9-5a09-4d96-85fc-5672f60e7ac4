"""
QA 분석 에이전트 - 메인 에이전트
"""
import asyncio
import time
import json
import uuid
from typing import List, Optional, Dict, Any
from datetime import datetime

from app.core.domain import QAPair, AnalysisRequest, BatchResult
from app.services.analysis import AnalysisService
from app.services.database import DatabaseService


class QAAnalysisAgent:
    """QA 분석 에이전트"""
    
    def __init__(self, supabase_url: str = None, supabase_key: str = None):
        """에이전트 초기화"""
        self.analysis_service = AnalysisService()
        self.db_service = DatabaseService(supabase_url, supabase_key)
        
        print("✅ QA 분석 에이전트 v2.2 초기화 완료")
    
    async def analyze_single_qa(self, request: AnalysisRequest) -> QAPair:
        """단일 QA 분석"""
        try:
            # 데이터 준비
            if request.inquiry_id and not request.question_text:
                # DB에서 조회
                inquiry_data = await self.db_service.get_inquiry_by_id(request.inquiry_id)
                if inquiry_data:
                    subject = inquiry_data.get('subject', '') or ''
                    content = inquiry_data.get('content', '') or ''
                    question = f"{subject} {content}".strip()
                    answer = inquiry_data.get('answer', '') or ''
                    qa_id = request.inquiry_id
                else:
                    raise Exception(f"문의 ID {request.inquiry_id}를 찾을 수 없습니다")
            else:
                # 직접 입력
                question = request.question_text or ""
                answer = request.answer_text or ""
                qa_id = request.inquiry_id or str(uuid.uuid4())
            
            # 분석 실행
            result = self.analysis_service.analyze_qa_pair(qa_id, question, answer)
            return result
            
        except Exception as e:
            print(f"❌ 분석 실패: {e}")
            # 오류 시 기본 QAPair 반환
            return QAPair(
                id=request.inquiry_id or str(uuid.uuid4()),
                question=request.question_text or "",
                answer=request.answer_text or "",
                weaknesses=["분석 실패"],
                recommendations=["다시 시도해주세요"],
                analysis_timestamp=datetime.now(),
                model_info={"error": "analysis_failed"}
            )
    
    async def analyze_batch(self, requests: List[AnalysisRequest]) -> BatchResult:
        """배치 분석"""
        start_time = time.time()
        results = []
        
        print(f"📊 {len(requests)}건의 QA 쌍 분석 시작...")
        
        for i, request in enumerate(requests, 1):
            try:
                result = await self.analyze_single_qa(request)
                results.append(result)
                print(f"  {i}/{len(requests)} 완료")
            except Exception as e:
                print(f"  {i}/{len(requests)} 실패: {e}")
                continue
        
        # 통계 계산
        total_count = len(results)
        pass_count = sum(1 for r in results if r.pass_threshold)
        average_score = sum(r.overall_score for r in results) / total_count if total_count > 0 else 0.0
        
        # 등급 분포
        grade_distribution = {}
        for result in results:
            grade = result.grade.value
            grade_distribution[grade] = grade_distribution.get(grade, 0) + 1
        
        processing_time = time.time() - start_time
        
        return BatchResult(
            total_count=total_count,
            pass_count=pass_count,
            average_score=average_score,
            grade_distribution=grade_distribution,
            results=results,
            processing_time_seconds=processing_time
        )
    
    async def analyze_sample_data(self, limit: int = 20) -> BatchResult:
        """샘플 데이터 분석 - playauto_qna 테이블에서 직접 조회"""
        print(f"📊 {limit}건의 문의 분석 시작...")

        # DB에서 실제 데이터 가져오기
        sample_data = await self.db_service.get_sample_inquiries(limit)

        if not sample_data:
            print("⚠️ DB에서 데이터를 가져올 수 없습니다. 테스트 데이터를 사용합니다.")
            # 테스트용 더미 데이터
            test_data = [
                {
                    'id': f'test_{i}',
                    'question': f'테스트 질문 {i}',
                    'answer': f'테스트 답변 {i}'
                }
                for i in range(min(limit, 5))
            ]
            sample_data = test_data

        # AnalysisRequest 생성
        requests = []
        for item in sample_data:
            # playauto_qna 테이블 구조에 맞게 조정
            question_text = item.get('question') or item.get('subject') or item.get('content') or ''
            answer_text = item.get('answer') or ''

            request = AnalysisRequest(
                inquiry_id=item.get('id'),
                question_text=question_text,
                answer_text=answer_text
            )
            requests.append(request)

        print(f"📋 {len(requests)}건의 요청 생성 완료")
        return await self.analyze_batch(requests)

    async def get_sample_data(self, limit: int = 10) -> List[Dict[str, Any]]:
        """모델 비교용 샘플 데이터 가져오기"""
        try:
            # DB에서 실제 데이터 가져오기
            sample_data = await self.db_service.get_sample_inquiries(limit)

            if sample_data:
                print(f"✅ DB에서 {len(sample_data)}건 데이터 로드 완료")
                return sample_data
            else:
                print("⚠️ DB에서 데이터를 가져올 수 없습니다.")
                return []

        except Exception as e:
            print(f"⚠️ DB 연결 실패: {e}")
            return []

    def export_results(self, batch_result: BatchResult, filename: str = None) -> None:
        """결과 내보내기"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"outputs/results/qa_analysis_{timestamp}.json"
        
        # 디렉토리 생성
        import os
        os.makedirs(os.path.dirname(filename), exist_ok=True)
        
        try:
            # JSON 저장
            data = batch_result.to_dict()
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            
            print(f"📄 결과를 {filename}에 저장했습니다.")
            
        except Exception as e:
            print(f"⚠️ 파일 저장 실패: {e}")
    
    def get_agent_info(self) -> Dict[str, Any]:
        """에이전트 정보"""
        return {
            "agent_type": "QA Analysis Agent v2.2",
            "version": "2.2.0",
            "embedding_model": self.analysis_service.embedding_model.get_model_info(),
            "db_connected": self.db_service.is_connected(),
            "analysis_settings": {
                "pass_threshold": self.analysis_service.pass_threshold,
                "grade_thresholds": self.analysis_service.grade_thresholds,
                "score_weights": self.analysis_service.score_weights
            }
        }
