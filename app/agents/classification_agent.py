"""
분류 에이전트 - 문의 카테고리 분류에 특화
"""
import re
from typing import Dict, List, Any
from app.core.interfaces import IClassificationAgent, IEmbeddingModel


class ClassificationAgent(IClassificationAgent):
    """
    문의 분류 에이전트
    
    역할:
    - 고객 문의를 카테고리별로 분류
    - 문의 유형 식별 (배송, 환불, 상품문의 등)
    - 분류 신뢰도 제공
    """
    
    def __init__(self, embedding_model: IEmbeddingModel):
        self.embedding_model = embedding_model
        
        # 한국어 이커머스 문의 카테고리
        self.categories = {
            "배송문의": {
                "keywords": ["배송", "택배", "발송", "도착", "언제", "배달", "운송장", "추적"],
                "patterns": [r"언제.*오나", r"배송.*확인", r"택배.*추적", r"언제.*도착"]
            },
            "환불문의": {
                "keywords": ["환불", "취소", "반품", "교환", "돌려", "취소하고", "환불받고"],
                "patterns": [r"환불.*가능", r"취소.*하고", r"반품.*하려", r"교환.*가능"]
            },
            "상품문의": {
                "keywords": ["상품", "제품", "사이즈", "색상", "재질", "품질", "사양", "스펙"],
                "patterns": [r"사이즈.*어떻", r"색상.*확인", r"재질.*뭔가", r"품질.*어떤"]
            },
            "결제문의": {
                "keywords": ["결제", "카드", "계좌", "무통장", "입금", "결제수단", "할부"],
                "patterns": [r"결제.*안됨", r"카드.*오류", r"입금.*확인", r"할부.*가능"]
            },
            "회원문의": {
                "keywords": ["회원", "가입", "로그인", "비밀번호", "아이디", "탈퇴", "정보수정"],
                "patterns": [r"로그인.*안됨", r"비밀번호.*찾기", r"회원.*탈퇴", r"정보.*수정"]
            },
            "쿠폰할인": {
                "keywords": ["쿠폰", "할인", "적립금", "포인트", "이벤트", "프로모션"],
                "patterns": [r"쿠폰.*사용", r"할인.*적용", r"적립금.*사용", r"이벤트.*참여"]
            },
            "기타문의": {
                "keywords": ["문의", "질문", "도움", "안내", "확인"],
                "patterns": [r"문의.*드립", r"질문.*있어", r"도움.*필요", r"확인.*부탁"]
            }
        }
        
        print("✅ 분류 에이전트 초기화 완료")
    
    async def classify_inquiry(self, text: str) -> Dict[str, Any]:
        """문의 분류"""
        if not text or not text.strip():
            return {
                "category": "기타문의",
                "confidence": 0.0,
                "keywords_found": [],
                "method": "default"
            }
        
        text_lower = text.lower()
        
        # 1. 키워드 기반 분류
        keyword_scores = self._calculate_keyword_scores(text_lower)
        
        # 2. 패턴 기반 분류
        pattern_scores = self._calculate_pattern_scores(text_lower)
        
        # 3. 임베딩 기반 분류 (고급)
        embedding_scores = await self._calculate_embedding_scores(text)
        
        # 4. 종합 점수 계산
        final_scores = self._combine_scores(keyword_scores, pattern_scores, embedding_scores)
        
        # 5. 최고 점수 카테고리 선택
        best_category = max(final_scores.keys(), key=lambda k: final_scores[k]["score"])
        best_result = final_scores[best_category]
        
        return {
            "category": best_category,
            "confidence": best_result["score"],
            "keywords_found": best_result["keywords"],
            "method": "hybrid",
            "all_scores": {k: v["score"] for k, v in final_scores.items()}
        }
    
    async def classify_batch(self, texts: List[str]) -> List[Dict[str, Any]]:
        """배치 분류"""
        results = []
        for text in texts:
            result = await self.classify_inquiry(text)
            results.append(result)
        return results
    
    def get_categories(self) -> List[str]:
        """사용 가능한 카테고리 목록"""
        return list(self.categories.keys())
    
    def _calculate_keyword_scores(self, text: str) -> Dict[str, Dict[str, Any]]:
        """키워드 기반 점수 계산"""
        scores = {}
        
        for category, config in self.categories.items():
            keywords_found = []
            score = 0.0
            
            for keyword in config["keywords"]:
                if keyword in text:
                    keywords_found.append(keyword)
                    score += 1.0
            
            # 정규화
            if config["keywords"]:
                score = score / len(config["keywords"])
            
            scores[category] = {
                "score": score,
                "keywords": keywords_found
            }
        
        return scores
    
    def _calculate_pattern_scores(self, text: str) -> Dict[str, float]:
        """패턴 기반 점수 계산"""
        scores = {}
        
        for category, config in self.categories.items():
            score = 0.0
            
            for pattern in config["patterns"]:
                if re.search(pattern, text):
                    score += 1.0
            
            # 정규화
            if config["patterns"]:
                score = score / len(config["patterns"])
            
            scores[category] = score
        
        return scores
    
    async def _calculate_embedding_scores(self, text: str) -> Dict[str, float]:
        """임베딩 기반 점수 계산"""
        try:
            # 카테고리별 대표 문장들
            category_examples = {
                "배송문의": "배송 언제 오나요? 택배 추적하고 싶어요.",
                "환불문의": "환불 가능한가요? 취소하고 싶습니다.",
                "상품문의": "상품 사이즈가 어떻게 되나요? 색상 확인 부탁드려요.",
                "결제문의": "결제가 안됩니다. 카드 오류가 나요.",
                "회원문의": "로그인이 안됩니다. 비밀번호를 찾고 싶어요.",
                "쿠폰할인": "쿠폰 사용하고 싶어요. 할인 적용 안되나요?",
                "기타문의": "문의드립니다. 도움이 필요해요."
            }
            
            text_embedding = self.embedding_model.encode(text)
            scores = {}
            
            for category, example in category_examples.items():
                example_embedding = self.embedding_model.encode(example)
                similarity = self._cosine_similarity(text_embedding, example_embedding)
                scores[category] = max(0.0, similarity)
            
            return scores
            
        except Exception as e:
            print(f"⚠️ 임베딩 분류 실패: {e}")
            return {category: 0.0 for category in self.categories.keys()}
    
    def _combine_scores(
        self, 
        keyword_scores: Dict[str, Dict[str, Any]], 
        pattern_scores: Dict[str, float], 
        embedding_scores: Dict[str, float]
    ) -> Dict[str, Dict[str, Any]]:
        """점수 결합"""
        final_scores = {}
        
        for category in self.categories.keys():
            keyword_score = keyword_scores.get(category, {"score": 0.0, "keywords": []})
            pattern_score = pattern_scores.get(category, 0.0)
            embedding_score = embedding_scores.get(category, 0.0)
            
            # 가중 평균 (키워드 40%, 패턴 30%, 임베딩 30%)
            combined_score = (
                keyword_score["score"] * 0.4 +
                pattern_score * 0.3 +
                embedding_score * 0.3
            )
            
            final_scores[category] = {
                "score": combined_score,
                "keywords": keyword_score["keywords"]
            }
        
        return final_scores
    
    def _cosine_similarity(self, vec1: List[float], vec2: List[float]) -> float:
        """코사인 유사도 계산"""
        if not vec1 or not vec2 or len(vec1) != len(vec2):
            return 0.0
        
        dot_product = sum(a * b for a, b in zip(vec1, vec2))
        magnitude1 = sum(a * a for a in vec1) ** 0.5
        magnitude2 = sum(b * b for b in vec2) ** 0.5
        
        if magnitude1 == 0 or magnitude2 == 0:
            return 0.0
        
        return dot_product / (magnitude1 * magnitude2)
    
    def get_agent_info(self) -> Dict[str, Any]:
        """에이전트 정보"""
        return {
            "agent_type": "Classification Agent",
            "version": "1.0.0",
            "specialization": "문의 카테고리 분류",
            "categories": list(self.categories.keys()),
            "methods": ["keyword", "pattern", "embedding"],
            "embedding_model": self.embedding_model.get_model_info()
        }
