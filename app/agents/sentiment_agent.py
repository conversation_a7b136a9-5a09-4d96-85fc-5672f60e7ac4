"""
감정 분석 에이전트 - 고객 감정 및 긴급도 분석에 특화
"""
import re
from typing import Dict, List, Any
from app.core.interfaces import ISentimentAgent


class SentimentAgent(ISentimentAgent):
    """
    감정 분석 에이전트
    
    역할:
    - 고객 문의의 감정 상태 분석 (긍정, 중립, 부정, 분노)
    - 문의 긴급도 분석 (낮음, 보통, 높음, 긴급)
    - 고객 만족도 예측
    """
    
    def __init__(self):
        # 감정 키워드 사전
        self.sentiment_keywords = {
            "긍정": {
                "keywords": ["좋아", "만족", "감사", "훌륭", "완벽", "최고", "추천", "기쁘", "행복"],
                "weight": 1.0
            },
            "중립": {
                "keywords": ["문의", "질문", "확인", "안내", "도움", "정보", "알려", "궁금"],
                "weight": 0.5
            },
            "부정": {
                "keywords": ["불만", "실망", "아쉽", "별로", "나쁘", "문제", "오류", "잘못"],
                "weight": -0.5
            },
            "분노": {
                "keywords": ["화나", "짜증", "열받", "최악", "엉망", "개판", "빡쳐", "미치겠"],
                "weight": -1.0
            }
        }
        
        # 긴급도 키워드 사전
        self.urgency_keywords = {
            "긴급": {
                "keywords": ["긴급", "급해", "빨리", "당장", "즉시", "지금", "오늘", "시급"],
                "patterns": [r"빨리.*해", r"급.*하게", r"당장.*필요", r"오늘.*안에"],
                "weight": 1.0
            },
            "높음": {
                "keywords": ["중요", "필요", "해결", "처리", "확인", "빠른", "신속"],
                "patterns": [r"빠른.*처리", r"신속.*해결", r"중요.*문제"],
                "weight": 0.7
            },
            "보통": {
                "keywords": ["문의", "질문", "안내", "도움", "확인", "알려"],
                "patterns": [r"문의.*드립", r"질문.*있어", r"확인.*부탁"],
                "weight": 0.4
            },
            "낮음": {
                "keywords": ["참고", "여유", "천천히", "괜찮", "나중에"],
                "patterns": [r"나중에.*해도", r"천천히.*해", r"여유.*있을"],
                "weight": 0.1
            }
        }
        
        # 만족도 지표
        self.satisfaction_indicators = {
            "positive": ["감사", "만족", "좋아", "훌륭", "완벽", "최고", "도움"],
            "negative": ["불만", "실망", "화나", "짜증", "최악", "문제", "오류"]
        }
        
        print("✅ 감정 분석 에이전트 초기화 완료")
    
    async def analyze_sentiment(self, text: str) -> Dict[str, Any]:
        """감정 분석"""
        if not text or not text.strip():
            return {
                "sentiment": "중립",
                "confidence": 0.0,
                "score": 0.0,
                "keywords_found": []
            }
        
        text_lower = text.lower()
        sentiment_scores = {}
        keywords_found = {}
        
        # 각 감정별 점수 계산
        for sentiment, config in self.sentiment_keywords.items():
            score = 0.0
            found_keywords = []
            
            for keyword in config["keywords"]:
                count = text_lower.count(keyword)
                if count > 0:
                    score += count * config["weight"]
                    found_keywords.extend([keyword] * count)
            
            sentiment_scores[sentiment] = score
            keywords_found[sentiment] = found_keywords
        
        # 감정 강화 패턴 확인
        intensifiers = ["정말", "너무", "완전", "진짜", "엄청", "매우", "아주"]
        intensifier_multiplier = 1.0
        for intensifier in intensifiers:
            if intensifier in text_lower:
                intensifier_multiplier += 0.2
        
        # 점수에 강화 배수 적용
        for sentiment in sentiment_scores:
            sentiment_scores[sentiment] *= intensifier_multiplier
        
        # 최종 감정 결정
        if not any(sentiment_scores.values()):
            final_sentiment = "중립"
            confidence = 0.5
            final_score = 0.0
        else:
            final_sentiment = max(sentiment_scores.keys(), key=lambda k: abs(sentiment_scores[k]))
            final_score = sentiment_scores[final_sentiment]
            confidence = min(1.0, abs(final_score) / 3.0)  # 정규화
        
        return {
            "sentiment": final_sentiment,
            "confidence": confidence,
            "score": final_score,
            "keywords_found": keywords_found.get(final_sentiment, []),
            "all_scores": sentiment_scores,
            "intensifier_applied": intensifier_multiplier > 1.0
        }
    
    async def analyze_urgency(self, text: str) -> Dict[str, Any]:
        """긴급도 분석"""
        if not text or not text.strip():
            return {
                "urgency": "보통",
                "confidence": 0.0,
                "score": 0.4,
                "indicators": []
            }
        
        text_lower = text.lower()
        urgency_scores = {}
        indicators_found = {}
        
        # 각 긴급도별 점수 계산
        for urgency, config in self.urgency_keywords.items():
            score = 0.0
            found_indicators = []
            
            # 키워드 점수
            for keyword in config["keywords"]:
                count = text_lower.count(keyword)
                if count > 0:
                    score += count * config["weight"]
                    found_indicators.extend([keyword] * count)
            
            # 패턴 점수
            for pattern in config.get("patterns", []):
                matches = len(re.findall(pattern, text_lower))
                if matches > 0:
                    score += matches * config["weight"] * 1.5  # 패턴은 가중치 더 높음
                    found_indicators.append(f"패턴:{pattern}")
            
            urgency_scores[urgency] = score
            indicators_found[urgency] = found_indicators
        
        # 특수 긴급 패턴 확인
        emergency_patterns = [
            r"[!]{2,}",  # 느낌표 2개 이상
            r"[?]{2,}",  # 물음표 2개 이상
            r"제발",
            r"부탁",
            r"도와주세요"
        ]
        
        emergency_boost = 0.0
        for pattern in emergency_patterns:
            if re.search(pattern, text_lower):
                emergency_boost += 0.2
        
        # 긴급도에 부스트 적용
        if emergency_boost > 0:
            urgency_scores["긴급"] += emergency_boost
            urgency_scores["높음"] += emergency_boost * 0.5
        
        # 최종 긴급도 결정
        if not any(urgency_scores.values()):
            final_urgency = "보통"
            confidence = 0.5
            final_score = 0.4
        else:
            final_urgency = max(urgency_scores.keys(), key=lambda k: urgency_scores[k])
            final_score = urgency_scores[final_urgency]
            confidence = min(1.0, final_score)
        
        return {
            "urgency": final_urgency,
            "confidence": confidence,
            "score": final_score,
            "indicators": indicators_found.get(final_urgency, []),
            "all_scores": urgency_scores,
            "emergency_boost_applied": emergency_boost > 0
        }
    
    async def analyze_customer_satisfaction(self, question: str, answer: str) -> Dict[str, Any]:
        """고객 만족도 분석"""
        # 질문의 감정 분석
        question_sentiment = await self.analyze_sentiment(question)
        
        # 답변 품질 지표
        answer_quality_score = 0.0
        quality_indicators = []
        
        if answer and answer.strip():
            answer_lower = answer.lower()
            
            # 긍정적 답변 지표
            for indicator in self.satisfaction_indicators["positive"]:
                if indicator in answer_lower:
                    answer_quality_score += 0.2
                    quality_indicators.append(f"긍정:{indicator}")
            
            # 정중한 표현
            polite_expressions = ["안녕하세요", "감사합니다", "죄송합니다", "도움", "안내"]
            for expr in polite_expressions:
                if expr in answer_lower:
                    answer_quality_score += 0.1
                    quality_indicators.append(f"정중:{expr}")
            
            # 구체적 정보 제공
            specific_info = ["주문번호", "운송장", "연락", "처리", "확인", "안내"]
            for info in specific_info:
                if info in answer_lower:
                    answer_quality_score += 0.15
                    quality_indicators.append(f"구체:{info}")
            
            # 답변 길이 점수 (적절한 길이)
            if 50 <= len(answer) <= 300:
                answer_quality_score += 0.1
                quality_indicators.append("적절한길이")
        
        # 예상 만족도 계산
        base_satisfaction = 0.5  # 기본 만족도
        
        # 질문 감정이 부정적일수록 만족도 달성이 어려움
        sentiment_impact = 0.0
        if question_sentiment["sentiment"] == "긍정":
            sentiment_impact = 0.2
        elif question_sentiment["sentiment"] == "중립":
            sentiment_impact = 0.0
        elif question_sentiment["sentiment"] == "부정":
            sentiment_impact = -0.2
        elif question_sentiment["sentiment"] == "분노":
            sentiment_impact = -0.4
        
        predicted_satisfaction = base_satisfaction + sentiment_impact + answer_quality_score
        predicted_satisfaction = max(0.0, min(1.0, predicted_satisfaction))
        
        # 만족도 등급
        if predicted_satisfaction >= 0.8:
            satisfaction_grade = "매우만족"
        elif predicted_satisfaction >= 0.6:
            satisfaction_grade = "만족"
        elif predicted_satisfaction >= 0.4:
            satisfaction_grade = "보통"
        elif predicted_satisfaction >= 0.2:
            satisfaction_grade = "불만족"
        else:
            satisfaction_grade = "매우불만족"
        
        return {
            "predicted_satisfaction": predicted_satisfaction,
            "satisfaction_grade": satisfaction_grade,
            "question_sentiment": question_sentiment,
            "answer_quality_score": answer_quality_score,
            "quality_indicators": quality_indicators,
            "factors": {
                "base_satisfaction": base_satisfaction,
                "sentiment_impact": sentiment_impact,
                "answer_quality": answer_quality_score
            }
        }
    
    def get_agent_info(self) -> Dict[str, Any]:
        """에이전트 정보"""
        return {
            "agent_type": "Sentiment Analysis Agent",
            "version": "1.0.0",
            "specialization": "감정 및 긴급도 분석",
            "capabilities": [
                "감정 분석 (긍정/중립/부정/분노)",
                "긴급도 분석 (낮음/보통/높음/긴급)",
                "고객 만족도 예측"
            ],
            "sentiment_categories": list(self.sentiment_keywords.keys()),
            "urgency_levels": list(self.urgency_keywords.keys())
        }
