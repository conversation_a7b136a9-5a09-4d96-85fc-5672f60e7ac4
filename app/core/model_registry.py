"""
모델 레지스트리 - 동적 모델 교체 시스템
"""
from typing import Dict, List, Any, Type, Optional
from abc import ABC, abstractmethod
from app.core.interfaces import IEmbeddingModel


class ModelRegistry:
    """
    모델 레지스트리 - 런타임에 모델을 등록하고 교체할 수 있는 시스템
    
    기능:
    - 모델 등록 및 해제
    - 런타임 모델 교체
    - 모델 메타데이터 관리
    - 모델 가용성 확인
    """
    
    def __init__(self):
        self._embedding_models: Dict[str, Dict[str, Any]] = {}
        self._llm_models: Dict[str, Dict[str, Any]] = {}
        self._classification_models: Dict[str, Dict[str, Any]] = {}
        
        # 기본 모델들 등록
        self._register_default_models()
        
        print("✅ 모델 레지스트리 초기화 완료")
    
    def register_embedding_model(
        self, 
        name: str, 
        model_class: Type, 
        config: Dict[str, Any],
        description: str = "",
        tags: List[str] = None
    ) -> bool:
        """임베딩 모델 등록"""
        try:
            model_info = {
                "name": name,
                "model_class": model_class,
                "config": config,
                "description": description,
                "tags": tags or [],
                "type": "embedding",
                "registered_at": self._get_timestamp(),
                "is_available": True
            }
            
            self._embedding_models[name] = model_info
            print(f"✅ 임베딩 모델 등록: {name}")
            return True
            
        except Exception as e:
            print(f"❌ 임베딩 모델 등록 실패 ({name}): {e}")
            return False
    
    def register_llm_model(
        self, 
        name: str, 
        model_class: Type, 
        config: Dict[str, Any],
        description: str = "",
        tags: List[str] = None
    ) -> bool:
        """LLM 모델 등록"""
        try:
            model_info = {
                "name": name,
                "model_class": model_class,
                "config": config,
                "description": description,
                "tags": tags or [],
                "type": "llm",
                "registered_at": self._get_timestamp(),
                "is_available": True
            }
            
            self._llm_models[name] = model_info
            print(f"✅ LLM 모델 등록: {name}")
            return True
            
        except Exception as e:
            print(f"❌ LLM 모델 등록 실패 ({name}): {e}")
            return False
    
    def register_classification_model(
        self, 
        name: str, 
        model_class: Type, 
        config: Dict[str, Any],
        description: str = "",
        tags: List[str] = None
    ) -> bool:
        """분류 모델 등록"""
        try:
            model_info = {
                "name": name,
                "model_class": model_class,
                "config": config,
                "description": description,
                "tags": tags or [],
                "type": "classification",
                "registered_at": self._get_timestamp(),
                "is_available": True
            }
            
            self._classification_models[name] = model_info
            print(f"✅ 분류 모델 등록: {name}")
            return True
            
        except Exception as e:
            print(f"❌ 분류 모델 등록 실패 ({name}): {e}")
            return False
    
    def get_embedding_model(self, name: str) -> Optional[Any]:
        """임베딩 모델 인스턴스 생성"""
        model_info = self._embedding_models.get(name)
        if not model_info:
            print(f"❌ 임베딩 모델을 찾을 수 없습니다: {name}")
            return None
        
        if not model_info["is_available"]:
            print(f"⚠️ 임베딩 모델이 사용 불가능합니다: {name}")
            return None
        
        try:
            model_class = model_info["model_class"]
            config = model_info["config"]
            return model_class(**config)
        except Exception as e:
            print(f"❌ 임베딩 모델 생성 실패 ({name}): {e}")
            return None
    
    def get_llm_model(self, name: str) -> Optional[Any]:
        """LLM 모델 인스턴스 생성"""
        model_info = self._llm_models.get(name)
        if not model_info:
            print(f"❌ LLM 모델을 찾을 수 없습니다: {name}")
            return None
        
        if not model_info["is_available"]:
            print(f"⚠️ LLM 모델이 사용 불가능합니다: {name}")
            return None
        
        try:
            model_class = model_info["model_class"]
            config = model_info["config"]
            return model_class(**config)
        except Exception as e:
            print(f"❌ LLM 모델 생성 실패 ({name}): {e}")
            return None
    
    def get_classification_model(self, name: str) -> Optional[Any]:
        """분류 모델 인스턴스 생성"""
        model_info = self._classification_models.get(name)
        if not model_info:
            print(f"❌ 분류 모델을 찾을 수 없습니다: {name}")
            return None
        
        if not model_info["is_available"]:
            print(f"⚠️ 분류 모델이 사용 불가능합니다: {name}")
            return None
        
        try:
            model_class = model_info["model_class"]
            config = model_info["config"]
            return model_class(**config)
        except Exception as e:
            print(f"❌ 분류 모델 생성 실패 ({name}): {e}")
            return None
    
    def list_available_models(self, model_type: str = None) -> Dict[str, List[str]]:
        """사용 가능한 모델 목록"""
        available = {
            "embedding": [],
            "llm": [],
            "classification": []
        }
        
        if model_type is None or model_type == "embedding":
            available["embedding"] = [
                name for name, info in self._embedding_models.items() 
                if info["is_available"]
            ]
        
        if model_type is None or model_type == "llm":
            available["llm"] = [
                name for name, info in self._llm_models.items() 
                if info["is_available"]
            ]
        
        if model_type is None or model_type == "classification":
            available["classification"] = [
                name for name, info in self._classification_models.items() 
                if info["is_available"]
            ]
        
        return available
    
    def get_model_info(self, name: str) -> Optional[Dict[str, Any]]:
        """모델 정보 조회"""
        # 모든 모델 타입에서 검색
        all_models = {
            **self._embedding_models,
            **self._llm_models,
            **self._classification_models
        }
        
        model_info = all_models.get(name)
        if model_info:
            # 민감한 정보 제외하고 반환
            return {
                "name": model_info["name"],
                "type": model_info["type"],
                "description": model_info["description"],
                "tags": model_info["tags"],
                "registered_at": model_info["registered_at"],
                "is_available": model_info["is_available"]
            }
        
        return None
    
    def set_model_availability(self, name: str, available: bool) -> bool:
        """모델 가용성 설정"""
        # 모든 모델 타입에서 검색
        all_registries = [
            self._embedding_models,
            self._llm_models,
            self._classification_models
        ]
        
        for registry in all_registries:
            if name in registry:
                registry[name]["is_available"] = available
                status = "활성화" if available else "비활성화"
                print(f"✅ 모델 {status}: {name}")
                return True
        
        print(f"❌ 모델을 찾을 수 없습니다: {name}")
        return False
    
    def unregister_model(self, name: str) -> bool:
        """모델 등록 해제"""
        removed = False
        
        if name in self._embedding_models:
            del self._embedding_models[name]
            removed = True
        
        if name in self._llm_models:
            del self._llm_models[name]
            removed = True
        
        if name in self._classification_models:
            del self._classification_models[name]
            removed = True
        
        if removed:
            print(f"✅ 모델 등록 해제: {name}")
        else:
            print(f"❌ 모델을 찾을 수 없습니다: {name}")
        
        return removed
    
    def search_models(self, query: str = "", tags: List[str] = None) -> List[Dict[str, Any]]:
        """모델 검색"""
        results = []
        
        # 모든 모델 수집
        all_models = []
        for models in [self._embedding_models, self._llm_models, self._classification_models]:
            all_models.extend(models.values())
        
        for model_info in all_models:
            # 쿼리 매칭
            if query and query.lower() not in model_info["name"].lower() and \
               query.lower() not in model_info["description"].lower():
                continue
            
            # 태그 매칭
            if tags and not any(tag in model_info["tags"] for tag in tags):
                continue
            
            results.append({
                "name": model_info["name"],
                "type": model_info["type"],
                "description": model_info["description"],
                "tags": model_info["tags"],
                "is_available": model_info["is_available"]
            })
        
        return results
    
    def _register_default_models(self):
        """기본 모델들 등록"""
        from app.models.embedding import EmbeddingModel
        
        # 기본 임베딩 모델들
        embedding_configs = [
            {
                "name": "kr_sbert_default",
                "config": {"model_name": "snunlp/KR-SBERT-V40K-klueNLI-augSTS"},
                "description": "한국어 SBERT 기본 모델 - 문장 임베딩 특화",
                "tags": ["korean", "sbert", "default", "embedding"]
            },
            {
                "name": "klue_roberta_base",
                "config": {"model_name": "klue/roberta-base"},
                "description": "KLUE RoBERTa 기본 모델",
                "tags": ["korean", "roberta", "klue", "balanced"]
            },
            {
                "name": "klue_roberta_large",
                "config": {"model_name": "klue/roberta-large"},
                "description": "KLUE RoBERTa 대형 모델 - 최고 정확도",
                "tags": ["korean", "roberta", "klue", "large", "high_accuracy"]
            },
            {
                "name": "beomi_kcelectra",
                "config": {"model_name": "beomi/KcELECTRA-base"},
                "description": "한국어 SNS·댓글·리뷰 특화 - 정확도 우수, 레이턴시 밸런스",
                "tags": ["korean", "electra", "sns", "review", "balanced"]
            },
            {
                "name": "koelectra_v3",
                "config": {"model_name": "monologg/koelectra-base-v3-discriminator"},
                "description": "경량화된 KoELECTRA - API 배포 최적화, 안정적 일관성",
                "tags": ["korean", "electra", "lightweight", "api", "consistent"]
            },
            {
                "name": "xlm_roberta_base",
                "config": {"model_name": "xlm-roberta-base"},
                "description": "다국어 RoBERTa - 100여개 언어 지원",
                "tags": ["multilingual", "roberta", "global", "versatile"]
            },
            {
                "name": "multilingual_mini",
                "config": {"model_name": "sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2"},
                "description": "다국어 MiniLM 모델 - 고속 처리",
                "tags": ["multilingual", "fast", "lightweight", "embedding"]
            }
        ]
        
        for config in embedding_configs:
            self.register_embedding_model(
                name=config["name"],
                model_class=EmbeddingModel,
                config=config["config"],
                description=config["description"],
                tags=config["tags"]
            )
    
    def _get_timestamp(self) -> str:
        """현재 타임스탬프"""
        from datetime import datetime
        return datetime.now().isoformat()
    
    def get_registry_stats(self) -> Dict[str, Any]:
        """레지스트리 통계"""
        return {
            "total_models": len(self._embedding_models) + len(self._llm_models) + len(self._classification_models),
            "embedding_models": len(self._embedding_models),
            "llm_models": len(self._llm_models),
            "classification_models": len(self._classification_models),
            "available_models": len([
                m for models in [self._embedding_models, self._llm_models, self._classification_models]
                for m in models.values() if m["is_available"]
            ])
        }
