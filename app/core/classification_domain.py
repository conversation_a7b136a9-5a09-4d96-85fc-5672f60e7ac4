"""
분류 전용 도메인 모델 - 고객 질문 분류 및 셀러 답변 평가
"""
from dataclasses import dataclass, field
from typing import Optional, List, Dict, Any
from datetime import datetime
from enum import Enum


class InquiryCategory(Enum):
    """고객 문의 카테고리"""
    SHIPPING = "배송"
    PRODUCT = "상품"
    EXCHANGE = "교환"
    REFUND = "환불"
    PAYMENT = "결제"
    ONE_ON_ONE = "1:1문의"
    COMPLAINT = "불만"
    OTHER = "기타"


class AnswerQuality(Enum):
    """답변 품질 등급"""
    EXCELLENT = "우수"    # 추후 활용 추천
    GOOD = "양호"         # 활용 가능
    AVERAGE = "보통"      # 개선 필요
    POOR = "미흡"         # 재작성 필요


@dataclass
class InquiryClassification:
    """고객 문의 분류 결과"""
    id: str
    inquiry_content: str
    
    # 분류 결과
    primary_category: InquiryCategory
    secondary_categories: List[InquiryCategory] = field(default_factory=list)
    confidence_score: float = 0.0
    
    # 태그
    auto_tags: List[str] = field(default_factory=list)
    keywords: List[str] = field(default_factory=list)
    
    # 메타데이터
    company_code: Optional[str] = None
    mall_name: Optional[str] = None
    inquiry_at: Optional[str] = None
    
    # 분석 정보
    classified_at: Optional[datetime] = None
    processing_time_ms: float = 0.0
    model_info: Dict[str, str] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """딕셔너리 변환"""
        return {
            "id": self.id,
            "inquiry_content": self.inquiry_content,
            "primary_category": self.primary_category.value,
            "secondary_categories": [cat.value for cat in self.secondary_categories],
            "confidence_score": self.confidence_score,
            "auto_tags": self.auto_tags,
            "keywords": self.keywords,
            "company_code": self.company_code,
            "mall_name": self.mall_name,
            "inquiry_at": self.inquiry_at,
            "classified_at": self.classified_at.isoformat() if self.classified_at else None,
            "processing_time_ms": self.processing_time_ms,
            "model_info": self.model_info
        }


@dataclass
class AnswerEvaluation:
    """셀러 답변 평가 결과"""
    id: str
    inquiry_content: str
    answer_content: str
    
    # 답변 품질 평가
    quality_grade: AnswerQuality
    completeness_score: float = 0.0
    accuracy_score: float = 0.0
    helpfulness_score: float = 0.0
    politeness_score: float = 0.0
    overall_score: float = 0.0
    
    # 추천 여부
    recommended_for_reuse: bool = False
    reuse_scenarios: List[str] = field(default_factory=list)
    
    # 개선사항
    strengths: List[str] = field(default_factory=list)
    weaknesses: List[str] = field(default_factory=list)
    improvement_suggestions: List[str] = field(default_factory=list)
    
    # 메타데이터
    company_code: Optional[str] = None
    mall_name: Optional[str] = None
    answered_at: Optional[str] = None
    
    # 분석 정보
    evaluated_at: Optional[datetime] = None
    processing_time_ms: float = 0.0
    model_info: Dict[str, str] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """딕셔너리 변환"""
        return {
            "id": self.id,
            "inquiry_content": self.inquiry_content,
            "answer_content": self.answer_content,
            "quality_grade": self.quality_grade.value,
            "completeness_score": self.completeness_score,
            "accuracy_score": self.accuracy_score,
            "helpfulness_score": self.helpfulness_score,
            "politeness_score": self.politeness_score,
            "overall_score": self.overall_score,
            "recommended_for_reuse": self.recommended_for_reuse,
            "reuse_scenarios": self.reuse_scenarios,
            "strengths": self.strengths,
            "weaknesses": self.weaknesses,
            "improvement_suggestions": self.improvement_suggestions,
            "company_code": self.company_code,
            "mall_name": self.mall_name,
            "answered_at": self.answered_at,
            "evaluated_at": self.evaluated_at.isoformat() if self.evaluated_at else None,
            "processing_time_ms": self.processing_time_ms,
            "model_info": self.model_info
        }


@dataclass
class ClassificationRequest:
    """분류 요청"""
    id: str
    inquiry_content: str
    answer_content: Optional[str] = None
    company_code: Optional[str] = None
    mall_name: Optional[str] = None
    inquiry_at: Optional[str] = None
    answered_at: Optional[str] = None
    
    # 분석 옵션
    classify_inquiry: bool = True
    evaluate_answer: bool = True
    generate_tags: bool = True


@dataclass
class ClassificationBatchResult:
    """분류 배치 결과"""
    total_count: int
    inquiry_classifications: List[InquiryClassification]
    answer_evaluations: List[AnswerEvaluation]
    processing_time_seconds: float
    
    # 통계
    category_distribution: Dict[str, int] = field(default_factory=dict)
    quality_distribution: Dict[str, int] = field(default_factory=dict)
    recommended_answers_count: int = 0
    
    def to_dict(self) -> Dict[str, Any]:
        """딕셔너리 변환"""
        return {
            "metadata": {
                "total_count": self.total_count,
                "processing_time_seconds": self.processing_time_seconds,
                "analysis_timestamp": datetime.now().isoformat(),
                "recommended_answers_count": self.recommended_answers_count
            },
            "statistics": {
                "category_distribution": self.category_distribution,
                "quality_distribution": self.quality_distribution
            },
            "inquiry_classifications": [item.to_dict() for item in self.inquiry_classifications],
            "answer_evaluations": [item.to_dict() for item in self.answer_evaluations]
        }
