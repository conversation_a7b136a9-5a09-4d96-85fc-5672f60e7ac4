"""
핵심 인터페이스 정의 - Interface Segregation Principle 적용
"""
from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional
from app.core.domain import QAPair, AnalysisRequest, BatchResult


class IAnalysisService(ABC):
    """분석 서비스 인터페이스"""
    
    @abstractmethod
    def analyze_qa_pair(self, qa_id: str, question: str, answer: str) -> QAPair:
        """QA 쌍 분석"""
        pass


class IDatabaseService(ABC):
    """데이터베이스 서비스 인터페이스"""
    
    @abstractmethod
    async def connect(self) -> bool:
        """데이터베이스 연결"""
        pass
    
    @abstractmethod
    async def get_inquiry_by_id(self, inquiry_id: str) -> Optional[Dict[str, Any]]:
        """ID로 문의 조회"""
        pass
    
    @abstractmethod
    async def get_sample_inquiries(self, limit: int = 20) -> List[Dict[str, Any]]:
        """샘플 문의 목록 조회"""
        pass
    
    @abstractmethod
    def is_connected(self) -> bool:
        """연결 상태 확인"""
        pass


class IEmbeddingModel(ABC):
    """임베딩 모델 인터페이스"""
    
    @abstractmethod
    def encode(self, text: str) -> List[float]:
        """텍스트를 벡터로 변환"""
        pass
    
    @abstractmethod
    def get_model_info(self) -> Dict[str, Any]:
        """모델 정보 반환"""
        pass


class IConfigManager(ABC):
    """설정 관리자 인터페이스"""
    
    @abstractmethod
    def get_current_profile(self) -> str:
        """현재 프로필 반환"""
        pass
    
    @abstractmethod
    def set_profile(self, profile_name: str) -> bool:
        """프로필 설정"""
        pass
    
    @abstractmethod
    def get_profile_info(self, profile_name: str = None) -> Dict[str, Any]:
        """프로필 정보 반환"""
        pass
    
    @abstractmethod
    def validate_current_config(self) -> bool:
        """현재 설정 검증"""
        pass


class IQAAnalysisAgent(ABC):
    """QA 분석 에이전트 인터페이스 - 품질 분석에 특화"""

    @abstractmethod
    async def analyze_single_qa(self, request: AnalysisRequest) -> QAPair:
        """단일 QA 분석"""
        pass

    @abstractmethod
    async def analyze_batch(self, requests: List[AnalysisRequest]) -> BatchResult:
        """배치 분석"""
        pass

    @abstractmethod
    async def analyze_sample_data(self, limit: int = 20) -> BatchResult:
        """샘플 데이터 분석"""
        pass


class IScoreCalculator(ABC):
    """점수 계산기 인터페이스"""

    @abstractmethod
    def calculate_semantic_similarity(self, question: str, answer: str) -> float:
        """의미적 유사도 계산"""
        pass

    @abstractmethod
    def calculate_topic_relevance(self, question: str, answer: str) -> float:
        """주제 관련성 계산"""
        pass

    @abstractmethod
    def calculate_keyword_overlap(self, question: str, answer: str) -> float:
        """키워드 겹침 계산"""
        pass


class IQualityEvaluator(ABC):
    """품질 평가기 인터페이스"""

    @abstractmethod
    def evaluate_completeness(self, answer: str) -> float:
        """답변 완성도 평가"""
        pass

    @abstractmethod
    def evaluate_accuracy(self, question: str, answer: str) -> float:
        """답변 정확성 평가"""
        pass

    @abstractmethod
    def evaluate_helpfulness(self, answer: str) -> float:
        """답변 도움도 평가"""
        pass


class IFeedbackGenerator(ABC):
    """피드백 생성기 인터페이스"""

    @abstractmethod
    def generate_strengths(self, scores: Dict[str, float]) -> List[str]:
        """강점 생성"""
        pass

    @abstractmethod
    def generate_weaknesses(self, scores: Dict[str, float]) -> List[str]:
        """약점 생성"""
        pass

    @abstractmethod
    def generate_recommendations(self, scores: Dict[str, float]) -> List[str]:
        """개선사항 생성"""
        pass


class ICostCalculator(ABC):
    """비용 계산기 인터페이스"""

    @abstractmethod
    def calculate_cost(self, input_text: str, output_text: str) -> Dict[str, Any]:
        """비용 계산"""
        pass

    @abstractmethod
    def count_tokens(self, text: str) -> int:
        """토큰 수 계산"""
        pass


class IClassificationAgent(ABC):
    """분류 에이전트 인터페이스 - 카테고리 분류에 특화"""

    @abstractmethod
    async def classify_inquiry(self, text: str) -> Dict[str, Any]:
        """문의 분류"""
        pass

    @abstractmethod
    async def classify_batch(self, texts: List[str]) -> List[Dict[str, Any]]:
        """배치 분류"""
        pass

    @abstractmethod
    def get_categories(self) -> List[str]:
        """사용 가능한 카테고리 목록"""
        pass


class IComparisonAgent(ABC):
    """비교 에이전트 인터페이스 - 모델 성능 비교에 특화"""

    @abstractmethod
    async def compare_models(self, requests: List[AnalysisRequest]) -> Dict[str, Any]:
        """모델 성능 비교"""
        pass

    @abstractmethod
    async def benchmark_models(self, test_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """모델 벤치마크"""
        pass

    @abstractmethod
    def generate_comparison_report(self, results: Dict[str, Any]) -> str:
        """비교 리포트 생성"""
        pass


class ISentimentAgent(ABC):
    """감정 분석 에이전트 인터페이스 - 감정 분석에 특화"""

    @abstractmethod
    async def analyze_sentiment(self, text: str) -> Dict[str, Any]:
        """감정 분석"""
        pass

    @abstractmethod
    async def analyze_urgency(self, text: str) -> Dict[str, Any]:
        """긴급도 분석"""
        pass

    @abstractmethod
    async def analyze_customer_satisfaction(self, question: str, answer: str) -> Dict[str, Any]:
        """고객 만족도 분석"""
        pass


class IExportService(ABC):
    """결과 내보내기 서비스 인터페이스"""
    
    @abstractmethod
    def export_results(self, batch_result: BatchResult) -> bool:
        """결과 내보내기"""
        pass
    
    @abstractmethod
    def export_to_csv(self, batch_result: BatchResult, file_path: str) -> bool:
        """CSV로 내보내기"""
        pass
    
    @abstractmethod
    def export_to_json(self, batch_result: BatchResult, file_path: str) -> bool:
        """JSON으로 내보내기"""
        pass


class IScoreCalculator(ABC):
    """점수 계산기 인터페이스"""
    
    @abstractmethod
    def calculate_semantic_similarity(self, question: str, answer: str) -> float:
        """의미적 유사도 계산"""
        pass
    
    @abstractmethod
    def calculate_topic_relevance(self, question: str, answer: str) -> float:
        """주제 관련성 계산"""
        pass
    
    @abstractmethod
    def calculate_keyword_overlap(self, question: str, answer: str) -> float:
        """키워드 겹침 계산"""
        pass


class IQualityEvaluator(ABC):
    """품질 평가기 인터페이스"""
    
    @abstractmethod
    def evaluate_answer_quality(self, question: str, answer: str) -> tuple[float, float, float]:
        """답변 품질 평가 (완성도, 정확성, 도움도)"""
        pass


class IFeedbackGenerator(ABC):
    """피드백 생성기 인터페이스"""
    
    @abstractmethod
    def generate_feedback(self, question: str, answer: str, scores: Dict[str, float]) -> tuple[List[str], List[str], List[str]]:
        """피드백 생성 (강점, 약점, 개선사항)"""
        pass
