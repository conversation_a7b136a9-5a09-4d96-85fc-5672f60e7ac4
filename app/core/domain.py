"""
핵심 도메인 모델 - 간소화된 버전
"""
from dataclasses import dataclass, field
from typing import Optional, List, Dict, Any
from datetime import datetime
from enum import Enum


class Grade(Enum):
    """등급"""
    A = "A"
    B = "B" 
    C = "C"
    D = "D"
    F = "F"


@dataclass
class QAPair:
    """QA 쌍"""
    id: str
    question: str
    answer: str
    
    # 분석 결과
    semantic_similarity: float = 0.0
    topic_relevance: float = 0.0
    keyword_overlap: float = 0.0
    answer_completeness: float = 0.0
    answer_accuracy: float = 0.0
    answer_helpfulness: float = 0.0
    
    overall_score: float = 0.0
    grade: Grade = Grade.F
    pass_threshold: bool = False
    
    # 피드백
    strengths: List[str] = field(default_factory=list)
    weaknesses: List[str] = field(default_factory=list)
    recommendations: List[str] = field(default_factory=list)
    
    # 메타데이터
    analysis_timestamp: Optional[datetime] = None
    processing_time_ms: float = 0.0
    model_info: Dict[str, str] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """딕셔너리 변환"""
        return {
            "id": self.id,
            "question": self.question,
            "answer": self.answer,
            "semantic_similarity": self.semantic_similarity,
            "topic_relevance": self.topic_relevance,
            "keyword_overlap": self.keyword_overlap,
            "answer_completeness": self.answer_completeness,
            "answer_accuracy": self.answer_accuracy,
            "answer_helpfulness": self.answer_helpfulness,
            "overall_score": self.overall_score,
            "grade": self.grade.value,
            "pass_threshold": self.pass_threshold,
            "strengths": self.strengths,
            "weaknesses": self.weaknesses,
            "recommendations": self.recommendations,
            "analysis_timestamp": self.analysis_timestamp.isoformat() if self.analysis_timestamp else None,
            "processing_time_ms": self.processing_time_ms,
            "model_info": self.model_info
        }


@dataclass
class AnalysisRequest:
    """분석 요청"""
    inquiry_id: Optional[str] = None
    question_text: Optional[str] = None
    answer_text: Optional[str] = None
    analysis_type: str = "full"
    include_feedback: bool = True


@dataclass
class BatchResult:
    """배치 분석 결과"""
    total_count: int
    pass_count: int
    average_score: float
    grade_distribution: Dict[str, int]
    results: List[QAPair]
    processing_time_seconds: float
    
    def to_dict(self) -> Dict[str, Any]:
        """딕셔너리 변환"""
        return {
            "metadata": {
                "total_count": self.total_count,
                "pass_count": self.pass_count,
                "pass_rate": (self.pass_count / self.total_count * 100) if self.total_count > 0 else 0,
                "average_score": self.average_score,
                "processing_time_seconds": self.processing_time_seconds,
                "analysis_timestamp": datetime.now().isoformat()
            },
            "statistics": {
                "grade_distribution": self.grade_distribution,
                "performance_categories": {
                    "excellent": sum(1 for r in self.results if r.overall_score >= 0.9),
                    "good": sum(1 for r in self.results if 0.8 <= r.overall_score < 0.9),
                    "fair": sum(1 for r in self.results if 0.7 <= r.overall_score < 0.8),
                    "poor": sum(1 for r in self.results if r.overall_score < 0.7)
                }
            },
            "results": [result.to_dict() for result in self.results]
        }
