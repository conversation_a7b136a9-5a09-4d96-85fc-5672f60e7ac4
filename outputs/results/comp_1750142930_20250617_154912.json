{"metadata": {"comparison_id": "comp_1750142930", "timestamp": "2025-06-17T15:48:50.827332", "models_tested": ["kr_sbert_default", "klue_roberta_base", "klue_roberta_large", "beomi_kcelectra", "koelectra_v3", "xlm_roberta_base", "multilingual_mini"], "total_requests": 3, "test_duration_seconds": 22.04397416114807}, "model_results": {"kr_sbert_default": {"model_name": "kr_sbert_default", "total_requests": 3, "successful_requests": 3, "results": ["QAPair(id='test_1750142930', question='배송 언제 오나요?', answer='3일 내 배송 예정입니다.', semantic_similarity=0.525014794782292, topic_relevance=0.39999999999999997, keyword_overlap=0.3333333333333333, answer_completeness=0.07, answer_accuracy=0.39999999999999997, answer_helpfulness=0.5, overall_score=0.38175369869557296, grade=<Grade.F: 'F'>, pass_threshold=False, strengths=[], weaknesses=['주제 관련성이 부족함', '답변이 너무 간단함', '답변의 정확성이 의심됨'], recommendations=['질문의 핵심 키워드를 답변에 더 많이 포함', '더 상세하고 구체적인 답변 제공', '고객이 다음에 취할 수 있는 구체적 행동 안내', '추가 문의 채널 안내 추가', '더 정중하고 친근한 표현 사용', '질문의 주제에 더 집중한 답변 작성', '답변 길이를 늘려 더 충분한 정보 제공'], analysis_timestamp=datetime.datetime(2025, 6, 17, 15, 48, 54, 635405), processing_time_ms=3807.990074157715, model_info={'service': 'RefactoredAnalysisService', 'version': '2.2.0'})", "QAPair(id='test_1750142934', question='환불 가능한가요?', answer='7일 내 환불 가능합니다.', semantic_similarity=0.6847851351855667, topic_relevance=0.65, keyword_overlap=0.5, answer_completeness=0.07, answer_accuracy=0.65, answer_helpfulness=0.5, overall_score=0.5341962837963917, grade=<Grade.F: 'F'>, pass_threshold=False, strengths=[], weaknesses=['답변이 너무 간단함'], recommendations=['더 상세하고 구체적인 답변 제공', '고객이 다음에 취할 수 있는 구체적 행동 안내', '추가 문의 채널 안내 추가', '더 정중하고 친근한 표현 사용', '답변 길이를 늘려 더 충분한 정보 제공'], analysis_timestamp=datetime.datetime(2025, 6, 17, 15, 48, 54, 651043), processing_time_ms=15.618085861206055, model_info={'service': 'RefactoredAnalysisService', 'version': '2.2.0'})", "QAPair(id='test_1750142934', question='상품 사이즈가 어떻게 되나요?', answer='M, L, XL 사이즈 있습니다.', semantic_similarity=0.4693776393686905, topic_relevance=0.0, keyword_overlap=0.0, answer_completeness=0.09, answer_accuracy=0.0, answer_helpfulness=0.5, overall_score=0.18084440984217262, grade=<Grade.F: 'F'>, pass_threshold=False, strengths=[], weaknesses=['질문과 답변의 의미적 연관성이 낮음', '주제 관련성이 부족함', '질문의 핵심 키워드가 답변에 부족', '답변이 너무 간단함', '답변의 정확성이 의심됨'], recommendations=['질문의 핵심 키워드를 답변에 더 많이 포함', '더 상세하고 구체적인 답변 제공', '고객이 다음에 취할 수 있는 구체적 행동 안내', '추가 문의 채널 안내 추가', '더 정중하고 친근한 표현 사용', '질문의 주제에 더 집중한 답변 작성', '답변 길이를 늘려 더 충분한 정보 제공'], analysis_timestamp=datetime.datetime(2025, 6, 17, 15, 48, 54, 666303), processing_time_ms=15.250921249389648, model_info={'service': 'RefactoredAnalysisService', 'version': '2.2.0'})"], "processing_times": [3808.013916015625, 15.629768371582031, 15.259027481079102], "avg_processing_time": 1279.6342372894287, "min_processing_time": 15.259027481079102, "max_processing_time": 3808.013916015625}, "klue_roberta_base": {"model_name": "klue_roberta_base", "total_requests": 3, "successful_requests": 3, "results": ["QAPair(id='test_1750142934', question='배송 언제 오나요?', answer='3일 내 배송 예정입니다.', semantic_similarity=0.7479740269934663, topic_relevance=0.39999999999999997, keyword_overlap=0.3333333333333333, answer_completeness=0.07, answer_accuracy=0.39999999999999997, answer_helpfulness=0.5, overall_score=0.43749350674836657, grade=<Grade.F: 'F'>, pass_threshold=False, strengths=[], weaknesses=['주제 관련성이 부족함', '답변이 너무 간단함', '답변의 정확성이 의심됨'], recommendations=['더 상세하고 구체적인 답변 제공', '고객이 다음에 취할 수 있는 구체적 행동 안내', '추가 문의 채널 안내 추가', '더 정중하고 친근한 표현 사용', '질문의 주제에 더 집중한 답변 작성', '답변 길이를 늘려 더 충분한 정보 제공'], analysis_timestamp=datetime.datetime(2025, 6, 17, 15, 48, 56, 870110), processing_time_ms=2201.061964035034, model_info={'service': 'RefactoredAnalysisService', 'version': '2.2.0'})", "QAPair(id='test_1750142936', question='환불 가능한가요?', answer='7일 내 환불 가능합니다.', semantic_similarity=0.787684095784327, topic_relevance=0.65, keyword_overlap=0.5, answer_completeness=0.07, answer_accuracy=0.65, answer_helpfulness=0.5, overall_score=0.5599210239460818, grade=<Grade.F: 'F'>, pass_threshold=False, strengths=[], weaknesses=['답변이 너무 간단함'], recommendations=['더 상세하고 구체적인 답변 제공', '고객이 다음에 취할 수 있는 구체적 행동 안내', '추가 문의 채널 안내 추가', '더 정중하고 친근한 표현 사용', '답변 길이를 늘려 더 충분한 정보 제공'], analysis_timestamp=datetime.datetime(2025, 6, 17, 15, 48, 56, 908657), processing_time_ms=38.53416442871094, model_info={'service': 'RefactoredAnalysisService', 'version': '2.2.0'})", "QAPair(id='test_1750142936', question='상품 사이즈가 어떻게 되나요?', answer='M, L, XL 사이즈 있습니다.', semantic_similarity=0.735784531794006, topic_relevance=0.0, keyword_overlap=0.0, answer_completeness=0.09, answer_accuracy=0.0, answer_helpfulness=0.5, overall_score=0.2474461329485015, grade=<Grade.F: 'F'>, pass_threshold=False, strengths=[], weaknesses=['주제 관련성이 부족함', '질문의 핵심 키워드가 답변에 부족', '답변이 너무 간단함', '답변의 정확성이 의심됨'], recommendations=['더 상세하고 구체적인 답변 제공', '고객이 다음에 취할 수 있는 구체적 행동 안내', '추가 문의 채널 안내 추가', '더 정중하고 친근한 표현 사용', '질문의 주제에 더 집중한 답변 작성', '답변 길이를 늘려 더 충분한 정보 제공'], analysis_timestamp=datetime.datetime(2025, 6, 17, 15, 48, 56, 970567), processing_time_ms=61.89727783203125, model_info={'service': 'RefactoredAnalysisService', 'version': '2.2.0'})"], "processing_times": [2201.0743618011475, 38.54799270629883, 61.90800666809082], "avg_processing_time": 767.1767870585123, "min_processing_time": 38.54799270629883, "max_processing_time": 2201.0743618011475}, "klue_roberta_large": {"model_name": "klue_roberta_large", "total_requests": 3, "successful_requests": 3, "results": ["QAPair(id='test_1750142936', question='배송 언제 오나요?', answer='3일 내 배송 예정입니다.', semantic_similarity=0.9006602091114247, topic_relevance=0.39999999999999997, keyword_overlap=0.3333333333333333, answer_completeness=0.07, answer_accuracy=0.39999999999999997, answer_helpfulness=0.5, overall_score=0.47566505227785616, grade=<Grade.F: 'F'>, pass_threshold=False, strengths=['질문과 답변의 의미적 연관성이 높음'], weaknesses=['주제 관련성이 부족함', '답변이 너무 간단함', '답변의 정확성이 의심됨'], recommendations=['더 상세하고 구체적인 답변 제공', '고객이 다음에 취할 수 있는 구체적 행동 안내', '추가 문의 채널 안내 추가', '더 정중하고 친근한 표현 사용', '질문의 주제에 더 집중한 답변 작성', '답변 길이를 늘려 더 충분한 정보 제공'], analysis_timestamp=datetime.datetime(2025, 6, 17, 15, 49, 0, 101156), processing_time_ms=3130.488872528076, model_info={'service': 'RefactoredAnalysisService', 'version': '2.2.0'})", "QAPair(id='test_1750142940', question='환불 가능한가요?', answer='7일 내 환불 가능합니다.', semantic_similarity=0.9151921244435589, topic_relevance=0.65, keyword_overlap=0.5, answer_completeness=0.07, answer_accuracy=0.65, answer_helpfulness=0.5, overall_score=0.5917980311108897, grade=<Grade.F: 'F'>, pass_threshold=False, strengths=['질문과 답변의 의미적 연관성이 높음'], weaknesses=['답변이 너무 간단함'], recommendations=['더 상세하고 구체적인 답변 제공', '고객이 다음에 취할 수 있는 구체적 행동 안내', '추가 문의 채널 안내 추가', '더 정중하고 친근한 표현 사용', '답변 길이를 늘려 더 충분한 정보 제공'], analysis_timestamp=datetime.datetime(2025, 6, 17, 15, 49, 0, 163420), processing_time_ms=62.23416328430176, model_info={'service': 'RefactoredAnalysisService', 'version': '2.2.0'})", "QAPair(id='test_1750142940', question='상품 사이즈가 어떻게 되나요?', answer='M, L, XL 사이즈 있습니다.', semantic_similarity=0.8743414615703123, topic_relevance=0.0, keyword_overlap=0.0, answer_completeness=0.09, answer_accuracy=0.0, answer_helpfulness=0.5, overall_score=0.2820853653925781, grade=<Grade.F: 'F'>, pass_threshold=False, strengths=['질문과 답변의 의미적 연관성이 높음'], weaknesses=['주제 관련성이 부족함', '질문의 핵심 키워드가 답변에 부족', '답변이 너무 간단함', '답변의 정확성이 의심됨'], recommendations=['더 상세하고 구체적인 답변 제공', '고객이 다음에 취할 수 있는 구체적 행동 안내', '추가 문의 채널 안내 추가', '더 정중하고 친근한 표현 사용', '질문의 주제에 더 집중한 답변 작성', '답변 길이를 늘려 더 충분한 정보 제공'], analysis_timestamp=datetime.datetime(2025, 6, 17, 15, 49, 0, 251894), processing_time_ms=88.46092224121094, model_info={'service': 'RefactoredAnalysisService', 'version': '2.2.0'})"], "processing_times": [3130.521059036255, 62.24822998046875, 88.47379684448242], "avg_processing_time": 1093.7476952870686, "min_processing_time": 62.24822998046875, "max_processing_time": 3130.521059036255}, "beomi_kcelectra": {"model_name": "beomi_kcelectra", "total_requests": 3, "successful_requests": 3, "results": ["QAPair(id='test_1750142940', question='배송 언제 오나요?', answer='3일 내 배송 예정입니다.', semantic_similarity=0.8519544950645649, topic_relevance=0.39999999999999997, keyword_overlap=0.3333333333333333, answer_completeness=0.07, answer_accuracy=0.39999999999999997, answer_helpfulness=0.5, overall_score=0.4634886237661412, grade=<Grade.F: 'F'>, pass_threshold=False, strengths=['질문과 답변의 의미적 연관성이 높음'], weaknesses=['주제 관련성이 부족함', '답변이 너무 간단함', '답변의 정확성이 의심됨'], recommendations=['더 상세하고 구체적인 답변 제공', '고객이 다음에 취할 수 있는 구체적 행동 안내', '추가 문의 채널 안내 추가', '더 정중하고 친근한 표현 사용', '질문의 주제에 더 집중한 답변 작성', '답변 길이를 늘려 더 충분한 정보 제공'], analysis_timestamp=datetime.datetime(2025, 6, 17, 15, 49, 2, 315734), processing_time_ms=2063.706874847412, model_info={'service': 'RefactoredAnalysisService', 'version': '2.2.0'})", "QAPair(id='test_1750142942', question='환불 가능한가요?', answer='7일 내 환불 가능합니다.', semantic_similarity=0.8847371958367319, topic_relevance=0.65, keyword_overlap=0.5, answer_completeness=0.07, answer_accuracy=0.65, answer_helpfulness=0.5, overall_score=0.584184298959183, grade=<Grade.F: 'F'>, pass_threshold=False, strengths=['질문과 답변의 의미적 연관성이 높음'], weaknesses=['답변이 너무 간단함'], recommendations=['더 상세하고 구체적인 답변 제공', '고객이 다음에 취할 수 있는 구체적 행동 안내', '추가 문의 채널 안내 추가', '더 정중하고 친근한 표현 사용', '답변 길이를 늘려 더 충분한 정보 제공'], analysis_timestamp=datetime.datetime(2025, 6, 17, 15, 49, 2, 347005), processing_time_ms=31.256914138793945, model_info={'service': 'RefactoredAnalysisService', 'version': '2.2.0'})", "QAPair(id='test_1750142942', question='상품 사이즈가 어떻게 되나요?', answer='M, L, XL 사이즈 있습니다.', semantic_similarity=0.9427569805935736, topic_relevance=0.0, keyword_overlap=0.0, answer_completeness=0.09, answer_accuracy=0.0, answer_helpfulness=0.5, overall_score=0.2991892451483934, grade=<Grade.F: 'F'>, pass_threshold=False, strengths=['질문과 답변의 의미적 연관성이 높음'], weaknesses=['주제 관련성이 부족함', '질문의 핵심 키워드가 답변에 부족', '답변이 너무 간단함', '답변의 정확성이 의심됨'], recommendations=['더 상세하고 구체적인 답변 제공', '고객이 다음에 취할 수 있는 구체적 행동 안내', '추가 문의 채널 안내 추가', '더 정중하고 친근한 표현 사용', '질문의 주제에 더 집중한 답변 작성', '답변 길이를 늘려 더 충분한 정보 제공'], analysis_timestamp=datetime.datetime(2025, 6, 17, 15, 49, 2, 378462), processing_time_ms=31.441926956176758, model_info={'service': 'RefactoredAnalysisService', 'version': '2.2.0'})"], "processing_times": [2063.720941543579, 31.271934509277344, 31.455039978027344], "avg_processing_time": 708.8159720102946, "min_processing_time": 31.271934509277344, "max_processing_time": 2063.720941543579}, "koelectra_v3": {"model_name": "koelectra_v3", "total_requests": 3, "successful_requests": 3, "results": ["QAPair(id='test_1750142942', question='배송 언제 오나요?', answer='3일 내 배송 예정입니다.', semantic_similarity=0.9630021591725768, topic_relevance=0.39999999999999997, keyword_overlap=0.3333333333333333, answer_completeness=0.07, answer_accuracy=0.39999999999999997, answer_helpfulness=0.5, overall_score=0.4912505397931442, grade=<Grade.F: 'F'>, pass_threshold=False, strengths=['질문과 답변의 의미적 연관성이 높음'], weaknesses=['주제 관련성이 부족함', '답변이 너무 간단함', '답변의 정확성이 의심됨'], recommendations=['더 상세하고 구체적인 답변 제공', '고객이 다음에 취할 수 있는 구체적 행동 안내', '추가 문의 채널 안내 추가', '더 정중하고 친근한 표현 사용', '질문의 주제에 더 집중한 답변 작성', '답변 길이를 늘려 더 충분한 정보 제공'], analysis_timestamp=datetime.datetime(2025, 6, 17, 15, 49, 4, 543721), processing_time_ms=2165.165901184082, model_info={'service': 'RefactoredAnalysisService', 'version': '2.2.0'})", "QAPair(id='test_1750142944', question='환불 가능한가요?', answer='7일 내 환불 가능합니다.', semantic_similarity=0.9774316237099313, topic_relevance=0.65, keyword_overlap=0.5, answer_completeness=0.07, answer_accuracy=0.65, answer_helpfulness=0.5, overall_score=0.6073579059274828, grade=<Grade.D: 'D'>, pass_threshold=True, strengths=['질문과 답변의 의미적 연관성이 높음'], weaknesses=['답변이 너무 간단함'], recommendations=['더 상세하고 구체적인 답변 제공', '고객이 다음에 취할 수 있는 구체적 행동 안내', '추가 문의 채널 안내 추가', '더 정중하고 친근한 표현 사용', '답변 길이를 늘려 더 충분한 정보 제공'], analysis_timestamp=datetime.datetime(2025, 6, 17, 15, 49, 4, 562959), processing_time_ms=19.22607421875, model_info={'service': 'RefactoredAnalysisService', 'version': '2.2.0'})", "QAPair(id='test_1750142944', question='상품 사이즈가 어떻게 되나요?', answer='M, L, XL 사이즈 있습니다.', semantic_similarity=0.9617273970449699, topic_relevance=0.0, keyword_overlap=0.0, answer_completeness=0.09, answer_accuracy=0.0, answer_helpfulness=0.5, overall_score=0.3039318492612425, grade=<Grade.F: 'F'>, pass_threshold=False, strengths=['질문과 답변의 의미적 연관성이 높음'], weaknesses=['주제 관련성이 부족함', '질문의 핵심 키워드가 답변에 부족', '답변이 너무 간단함', '답변의 정확성이 의심됨'], recommendations=['더 상세하고 구체적인 답변 제공', '고객이 다음에 취할 수 있는 구체적 행동 안내', '추가 문의 채널 안내 추가', '더 정중하고 친근한 표현 사용', '질문의 주제에 더 집중한 답변 작성', '답변 길이를 늘려 더 충분한 정보 제공'], analysis_timestamp=datetime.datetime(2025, 6, 17, 15, 49, 4, 598048), processing_time_ms=35.07804870605469, model_info={'service': 'RefactoredAnalysisService', 'version': '2.2.0'})"], "processing_times": [2165.178060531616, 19.236087799072266, 35.09187698364258], "avg_processing_time": 739.8353417714437, "min_processing_time": 19.236087799072266, "max_processing_time": 2165.178060531616}, "xlm_roberta_base": {"model_name": "xlm_roberta_base", "total_requests": 3, "successful_requests": 3, "results": ["QAPair(id='test_1750142944', question='배송 언제 오나요?', answer='3일 내 배송 예정입니다.', semantic_similarity=0.9956251673679357, topic_relevance=0.39999999999999997, keyword_overlap=0.3333333333333333, answer_completeness=0.07, answer_accuracy=0.39999999999999997, answer_helpfulness=0.5, overall_score=0.49940629184198393, grade=<Grade.F: 'F'>, pass_threshold=False, strengths=['질문과 답변의 의미적 연관성이 높음'], weaknesses=['주제 관련성이 부족함', '답변이 너무 간단함', '답변의 정확성이 의심됨'], recommendations=['더 상세하고 구체적인 답변 제공', '고객이 다음에 취할 수 있는 구체적 행동 안내', '추가 문의 채널 안내 추가', '더 정중하고 친근한 표현 사용', '질문의 주제에 더 집중한 답변 작성', '답변 길이를 늘려 더 충분한 정보 제공'], analysis_timestamp=datetime.datetime(2025, 6, 17, 15, 49, 9, 136126), processing_time_ms=4537.987232208252, model_info={'service': 'RefactoredAnalysisService', 'version': '2.2.0'})", "QAPair(id='test_1750142949', question='환불 가능한가요?', answer='7일 내 환불 가능합니다.', semantic_similarity=0.9968557610548597, topic_relevance=0.65, keyword_overlap=0.5, answer_completeness=0.07, answer_accuracy=0.65, answer_helpfulness=0.5, overall_score=0.6122139402637149, grade=<Grade.D: 'D'>, pass_threshold=True, strengths=['질문과 답변의 의미적 연관성이 높음'], weaknesses=['답변이 너무 간단함'], recommendations=['더 상세하고 구체적인 답변 제공', '고객이 다음에 취할 수 있는 구체적 행동 안내', '추가 문의 채널 안내 추가', '더 정중하고 친근한 표현 사용', '답변 길이를 늘려 더 충분한 정보 제공'], analysis_timestamp=datetime.datetime(2025, 6, 17, 15, 49, 9, 183027), processing_time_ms=46.88596725463867, model_info={'service': 'RefactoredAnalysisService', 'version': '2.2.0'})", "QAPair(id='test_1750142949', question='상품 사이즈가 어떻게 되나요?', answer='M, L, XL 사이즈 있습니다.', semantic_similarity=0.9923340051704529, topic_relevance=0.0, keyword_overlap=0.0, answer_completeness=0.09, answer_accuracy=0.0, answer_helpfulness=0.5, overall_score=0.31158350129261325, grade=<Grade.F: 'F'>, pass_threshold=False, strengths=['질문과 답변의 의미적 연관성이 높음'], weaknesses=['주제 관련성이 부족함', '질문의 핵심 키워드가 답변에 부족', '답변이 너무 간단함', '답변의 정확성이 의심됨'], recommendations=['더 상세하고 구체적인 답변 제공', '고객이 다음에 취할 수 있는 구체적 행동 안내', '추가 문의 채널 안내 추가', '더 정중하고 친근한 표현 사용', '질문의 주제에 더 집중한 답변 작성', '답변 길이를 늘려 더 충분한 정보 제공'], analysis_timestamp=datetime.datetime(2025, 6, 17, 15, 49, 9, 226470), processing_time_ms=43.427228927612305, model_info={'service': 'RefactoredAnalysisService', 'version': '2.2.0'})"], "processing_times": [4538.001775741577, 46.90098762512207, 43.4420108795166], "avg_processing_time": 1542.7815914154053, "min_processing_time": 43.4420108795166, "max_processing_time": 4538.001775741577}, "multilingual_mini": {"model_name": "multilingual_mini", "total_requests": 3, "successful_requests": 3, "results": ["QAPair(id='test_1750142949', question='배송 언제 오나요?', answer='3일 내 배송 예정입니다.', semantic_similarity=0.6409290180027416, topic_relevance=0.39999999999999997, keyword_overlap=0.3333333333333333, answer_completeness=0.07, answer_accuracy=0.39999999999999997, answer_helpfulness=0.5, overall_score=0.4107322545006854, grade=<Grade.F: 'F'>, pass_threshold=False, strengths=[], weaknesses=['주제 관련성이 부족함', '답변이 너무 간단함', '답변의 정확성이 의심됨'], recommendations=['더 상세하고 구체적인 답변 제공', '고객이 다음에 취할 수 있는 구체적 행동 안내', '추가 문의 채널 안내 추가', '더 정중하고 친근한 표현 사용', '질문의 주제에 더 집중한 답변 작성', '답변 길이를 늘려 더 충분한 정보 제공'], analysis_timestamp=datetime.datetime(2025, 6, 17, 15, 49, 12, 728260), processing_time_ms=3501.6911029815674, model_info={'service': 'RefactoredAnalysisService', 'version': '2.2.0'})", "QAPair(id='test_1750142952', question='환불 가능한가요?', answer='7일 내 환불 가능합니다.', semantic_similarity=0.529439482299983, topic_relevance=0.65, keyword_overlap=0.5, answer_completeness=0.07, answer_accuracy=0.65, answer_helpfulness=0.5, overall_score=0.49535987057499575, grade=<Grade.F: 'F'>, pass_threshold=False, strengths=[], weaknesses=['답변이 너무 간단함'], recommendations=['질문의 핵심 키워드를 답변에 더 많이 포함', '더 상세하고 구체적인 답변 제공', '고객이 다음에 취할 수 있는 구체적 행동 안내', '추가 문의 채널 안내 추가', '더 정중하고 친근한 표현 사용', '답변 길이를 늘려 더 충분한 정보 제공'], analysis_timestamp=datetime.datetime(2025, 6, 17, 15, 49, 12, 801097), processing_time_ms=72.81374931335449, model_info={'service': 'RefactoredAnalysisService', 'version': '2.2.0'})", "QAPair(id='test_1750142952', question='상품 사이즈가 어떻게 되나요?', answer='M, L, XL 사이즈 있습니다.', semantic_similarity=0.6105551311336487, topic_relevance=0.0, keyword_overlap=0.0, answer_completeness=0.09, answer_accuracy=0.0, answer_helpfulness=0.5, overall_score=0.21613878278341217, grade=<Grade.F: 'F'>, pass_threshold=False, strengths=[], weaknesses=['주제 관련성이 부족함', '질문의 핵심 키워드가 답변에 부족', '답변이 너무 간단함', '답변의 정확성이 의심됨'], recommendations=['더 상세하고 구체적인 답변 제공', '고객이 다음에 취할 수 있는 구체적 행동 안내', '추가 문의 채널 안내 추가', '더 정중하고 친근한 표현 사용', '질문의 주제에 더 집중한 답변 작성', '답변 길이를 늘려 더 충분한 정보 제공'], analysis_timestamp=datetime.datetime(2025, 6, 17, 15, 49, 12, 871248), processing_time_ms=70.1301097869873, model_info={'service': 'RefactoredAnalysisService', 'version': '2.2.0'})"], "processing_times": [3501.711845397949, 72.83520698547363, 70.14775276184082], "avg_processing_time": 1214.8982683817546, "min_processing_time": 70.14775276184082, "max_processing_time": 3501.711845397949}}, "performance_metrics": {"kr_sbert_default": {"avg_score": 0.36559813077804576, "pass_rate": 0.0, "avg_processing_time": 1279.6342372894287, "throughput": 0.781473307652536}, "klue_roberta_base": {"avg_score": 0.4149535545476499, "pass_rate": 0.0, "avg_processing_time": 767.1767870585123, "throughput": 1.303480523484257}, "klue_roberta_large": {"avg_score": 0.449849482927108, "pass_rate": 0.0, "avg_processing_time": 1093.7476952870686, "throughput": 0.9142876408416447}, "beomi_kcelectra": {"avg_score": 0.4489540559579059, "pass_rate": 0.0, "avg_processing_time": 708.8159720102946, "throughput": 1.4108034235795641}, "koelectra_v3": {"avg_score": 0.46751343166062315, "pass_rate": 33.33333333333333, "avg_processing_time": 739.8353417714437, "throughput": 1.3516521089755247}, "xlm_roberta_base": {"avg_score": 0.47440124446610404, "pass_rate": 33.33333333333333, "avg_processing_time": 1542.7815914154053, "throughput": 0.6481798885625559}, "multilingual_mini": {"avg_score": 0.3740769692863644, "pass_rate": 0.0, "avg_processing_time": 1214.8982683817546, "throughput": 0.8231141866158067}}, "accuracy_metrics": {"kr_sbert_default": {"semantic_similarity": 0.5597258564455164, "topic_relevance": 0.35000000000000003, "answer_accuracy": 0.35000000000000003, "consistency": 0.9695464129004538}, "klue_roberta_base": {"semantic_similarity": 0.7571475515239331, "topic_relevance": 0.35000000000000003, "answer_accuracy": 0.35000000000000003, "consistency": 0.975808557851234}, "klue_roberta_large": {"semantic_similarity": 0.8967312650417654, "topic_relevance": 0.35000000000000003, "answer_accuracy": 0.35000000000000003, "consistency": 0.9761046492089892}, "beomi_kcelectra": {"semantic_similarity": 0.8931495571649567, "topic_relevance": 0.35000000000000003, "answer_accuracy": 0.35000000000000003, "consistency": 0.9799463913351565}, "koelectra_v3": {"semantic_similarity": 0.967387059975826, "topic_relevance": 0.35000000000000003, "answer_accuracy": 0.35000000000000003, "consistency": 0.9770973933679605}, "xlm_roberta_base": {"semantic_similarity": 0.9949383111977493, "topic_relevance": 0.35000000000000003, "answer_accuracy": 0.35000000000000003, "consistency": 0.977456333698961}, "multilingual_mini": {"semantic_similarity": 0.5936412104787911, "topic_relevance": 0.35000000000000003, "answer_accuracy": 0.35000000000000003, "consistency": 0.9799129492478162}}, "comparison_summary": {"best_overall_score": "xlm_roberta_base", "fastest_processing": "beomi_kcelectra", "highest_throughput": "beomi_kcelectra", "most_consistent": "beomi_kcelectra", "performance_spread": {"score_range": {"min": 0.36559813077804576, "max": 0.47440124446610404, "avg": 0.427906695660543}, "speed_range": {"min": 708.8159720102946, "max": 1542.7815914154053, "avg": 1049.5556990305583}, "throughput_range": {"min": 0.6481798885625559, "max": 1.4108034235795641, "avg": 1.0332844399588412}}}, "recommendations": ["🏆 전체 성능 최고: xlm_roberta_base", "⚡ 처리 속도 최고: be<PERSON>_kcelectra", "🔄 처리량 최고: be<PERSON>_kcelectra", "📊 일관성 최고: be<PERSON>_kcelectra", "", "📋 사용 시나리오별 추천:", "• 실시간 처리: 처리 속도 최고 모델 사용", "• 대용량 배치: 처리량 최고 모델 사용", "• 고품질 분석: 전체 성능 최고 모델 사용", "• 안정적 서비스: 일관성 최고 모델 사용"], "storage_metadata": {"result_id": "comp_1750142930", "saved_at": "2025-06-17T15:49:12.871348", "filename": "comp_1750142930_20250617_154912.json", "tags": ["model_comparison", "auto_generated", "large_scale"], "description": "7개 모델 비교 (3건 요청)", "file_hash": "f4fc718b52deaac67daca44a2bc8a3d8"}}