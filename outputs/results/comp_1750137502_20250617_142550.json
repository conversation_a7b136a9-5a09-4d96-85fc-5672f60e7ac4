{"metadata": {"comparison_id": "comp_1750137502", "timestamp": "2025-06-17T14:18:22.373855", "models_tested": ["kr_sbert_default", "klue_roberta_base", "klue_roberta_large", "beomi_kcelectra", "koelectra_v3", "xlm_roberta_base", "multilingual_mini"], "total_requests": 3, "test_duration_seconds": 447.92774391174316}, "model_results": {"kr_sbert_default": {"model_name": "kr_sbert_default", "total_requests": 3, "successful_requests": 3, "results": ["QAPair(id='test_1750137502', question='배송 언제 오나요?', answer='3일 내 배송 예정입니다.', semantic_similarity=0.525014794782292, topic_relevance=0.39999999999999997, keyword_overlap=0.3333333333333333, answer_completeness=0.07, answer_accuracy=0.39999999999999997, answer_helpfulness=0.5, overall_score=0.38175369869557296, grade=<Grade.F: 'F'>, pass_threshold=False, strengths=[], weaknesses=['주제 관련성이 부족함', '답변이 너무 간단함', '답변의 정확성이 의심됨'], recommendations=['질문의 핵심 키워드를 답변에 더 많이 포함', '더 상세하고 구체적인 답변 제공', '고객이 다음에 취할 수 있는 구체적 행동 안내', '추가 문의 채널 안내 추가', '더 정중하고 친근한 표현 사용', '질문의 주제에 더 집중한 답변 작성', '답변 길이를 늘려 더 충분한 정보 제공'], analysis_timestamp=datetime.datetime(2025, 6, 17, 14, 18, 28, 497953), processing_time_ms=6124.042987823486, model_info={'service': 'RefactoredAnalysisService', 'version': '2.2.0'})", "QAPair(id='test_1750137508', question='환불 가능한가요?', answer='7일 내 환불 가능합니다.', semantic_similarity=0.6847851351855667, topic_relevance=0.65, keyword_overlap=0.5, answer_completeness=0.07, answer_accuracy=0.65, answer_helpfulness=0.5, overall_score=0.5341962837963917, grade=<Grade.F: 'F'>, pass_threshold=False, strengths=[], weaknesses=['답변이 너무 간단함'], recommendations=['더 상세하고 구체적인 답변 제공', '고객이 다음에 취할 수 있는 구체적 행동 안내', '추가 문의 채널 안내 추가', '더 정중하고 친근한 표현 사용', '답변 길이를 늘려 더 충분한 정보 제공'], analysis_timestamp=datetime.datetime(2025, 6, 17, 14, 18, 28, 549295), processing_time_ms=51.322221755981445, model_info={'service': 'RefactoredAnalysisService', 'version': '2.2.0'})", "QAPair(id='test_1750137508', question='상품 사이즈가 어떻게 되나요?', answer='M, L, XL 사이즈 있습니다.', semantic_similarity=0.4693776393686905, topic_relevance=0.0, keyword_overlap=0.0, answer_completeness=0.09, answer_accuracy=0.0, answer_helpfulness=0.5, overall_score=0.18084440984217262, grade=<Grade.F: 'F'>, pass_threshold=False, strengths=[], weaknesses=['질문과 답변의 의미적 연관성이 낮음', '주제 관련성이 부족함', '질문의 핵심 키워드가 답변에 부족', '답변이 너무 간단함', '답변의 정확성이 의심됨'], recommendations=['질문의 핵심 키워드를 답변에 더 많이 포함', '더 상세하고 구체적인 답변 제공', '고객이 다음에 취할 수 있는 구체적 행동 안내', '추가 문의 채널 안내 추가', '더 정중하고 친근한 표현 사용', '질문의 주제에 더 집중한 답변 작성', '답변 길이를 늘려 더 충분한 정보 제공'], analysis_timestamp=datetime.datetime(2025, 6, 17, 14, 18, 28, 598673), processing_time_ms=49.36337471008301, model_info={'service': 'RefactoredAnalysisService', 'version': '2.2.0'})"], "processing_times": [6124.063014984131, 51.338911056518555, 49.382925033569336], "avg_processing_time": 2074.9282836914062, "min_processing_time": 49.382925033569336, "max_processing_time": 6124.063014984131}, "klue_roberta_base": {"model_name": "klue_roberta_base", "total_requests": 3, "successful_requests": 3, "results": ["QAPair(id='test_1750137508', question='배송 언제 오나요?', answer='3일 내 배송 예정입니다.', semantic_similarity=0.7479740269934663, topic_relevance=0.39999999999999997, keyword_overlap=0.3333333333333333, answer_completeness=0.07, answer_accuracy=0.39999999999999997, answer_helpfulness=0.5, overall_score=0.43749350674836657, grade=<Grade.F: 'F'>, pass_threshold=False, strengths=[], weaknesses=['주제 관련성이 부족함', '답변이 너무 간단함', '답변의 정확성이 의심됨'], recommendations=['더 상세하고 구체적인 답변 제공', '고객이 다음에 취할 수 있는 구체적 행동 안내', '추가 문의 채널 안내 추가', '더 정중하고 친근한 표현 사용', '질문의 주제에 더 집중한 답변 작성', '답변 길이를 늘려 더 충분한 정보 제공'], analysis_timestamp=datetime.datetime(2025, 6, 17, 14, 18, 30, 437766), processing_time_ms=1837.9693031311035, model_info={'service': 'RefactoredAnalysisService', 'version': '2.2.0'})", "QAPair(id='test_1750137510', question='환불 가능한가요?', answer='7일 내 환불 가능합니다.', semantic_similarity=0.787684095784327, topic_relevance=0.65, keyword_overlap=0.5, answer_completeness=0.07, answer_accuracy=0.65, answer_helpfulness=0.5, overall_score=0.5599210239460818, grade=<Grade.F: 'F'>, pass_threshold=False, strengths=[], weaknesses=['답변이 너무 간단함'], recommendations=['더 상세하고 구체적인 답변 제공', '고객이 다음에 취할 수 있는 구체적 행동 안내', '추가 문의 채널 안내 추가', '더 정중하고 친근한 표현 사용', '답변 길이를 늘려 더 충분한 정보 제공'], analysis_timestamp=datetime.datetime(2025, 6, 17, 14, 18, 30, 469988), processing_time_ms=32.20796585083008, model_info={'service': 'RefactoredAnalysisService', 'version': '2.2.0'})", "QAPair(id='test_1750137510', question='상품 사이즈가 어떻게 되나요?', answer='M, L, XL 사이즈 있습니다.', semantic_similarity=0.735784531794006, topic_relevance=0.0, keyword_overlap=0.0, answer_completeness=0.09, answer_accuracy=0.0, answer_helpfulness=0.5, overall_score=0.2474461329485015, grade=<Grade.F: 'F'>, pass_threshold=False, strengths=[], weaknesses=['주제 관련성이 부족함', '질문의 핵심 키워드가 답변에 부족', '답변이 너무 간단함', '답변의 정확성이 의심됨'], recommendations=['더 상세하고 구체적인 답변 제공', '고객이 다음에 취할 수 있는 구체적 행동 안내', '추가 문의 채널 안내 추가', '더 정중하고 친근한 표현 사용', '질문의 주제에 더 집중한 답변 작성', '답변 길이를 늘려 더 충분한 정보 제공'], analysis_timestamp=datetime.datetime(2025, 6, 17, 14, 18, 30, 555307), processing_time_ms=85.30688285827637, model_info={'service': 'RefactoredAnalysisService', 'version': '2.2.0'})"], "processing_times": [1837.981939315796, 32.219886779785156, 85.31785011291504], "avg_processing_time": 651.8398920694987, "min_processing_time": 32.219886779785156, "max_processing_time": 1837.981939315796}, "klue_roberta_large": {"model_name": "klue_roberta_large", "total_requests": 3, "successful_requests": 3, "results": ["QAPair(id='test_1750137510', question='배송 언제 오나요?', answer='3일 내 배송 예정입니다.', semantic_similarity=0.9006602091114247, topic_relevance=0.39999999999999997, keyword_overlap=0.3333333333333333, answer_completeness=0.07, answer_accuracy=0.39999999999999997, answer_helpfulness=0.5, overall_score=0.47566505227785616, grade=<Grade.F: 'F'>, pass_threshold=False, strengths=['질문과 답변의 의미적 연관성이 높음'], weaknesses=['주제 관련성이 부족함', '답변이 너무 간단함', '답변의 정확성이 의심됨'], recommendations=['더 상세하고 구체적인 답변 제공', '고객이 다음에 취할 수 있는 구체적 행동 안내', '추가 문의 채널 안내 추가', '더 정중하고 친근한 표현 사용', '질문의 주제에 더 집중한 답변 작성', '답변 길이를 늘려 더 충분한 정보 제공'], analysis_timestamp=datetime.datetime(2025, 6, 17, 14, 21, 28, 80078), processing_time_ms=177524.67393875122, model_info={'service': 'RefactoredAnalysisService', 'version': '2.2.0'})", "QAPair(id='test_1750137688', question='환불 가능한가요?', answer='7일 내 환불 가능합니다.', semantic_similarity=0.9151921244435589, topic_relevance=0.65, keyword_overlap=0.5, answer_completeness=0.07, answer_accuracy=0.65, answer_helpfulness=0.5, overall_score=0.5917980311108897, grade=<Grade.F: 'F'>, pass_threshold=False, strengths=['질문과 답변의 의미적 연관성이 높음'], weaknesses=['답변이 너무 간단함'], recommendations=['더 상세하고 구체적인 답변 제공', '고객이 다음에 취할 수 있는 구체적 행동 안내', '추가 문의 채널 안내 추가', '더 정중하고 친근한 표현 사용', '답변 길이를 늘려 더 충분한 정보 제공'], analysis_timestamp=datetime.datetime(2025, 6, 17, 14, 21, 28, 304406), processing_time_ms=224.3049144744873, model_info={'service': 'RefactoredAnalysisService', 'version': '2.2.0'})", "QAPair(id='test_1750137688', question='상품 사이즈가 어떻게 되나요?', answer='M, L, XL 사이즈 있습니다.', semantic_similarity=0.8743414615703123, topic_relevance=0.0, keyword_overlap=0.0, answer_completeness=0.09, answer_accuracy=0.0, answer_helpfulness=0.5, overall_score=0.2820853653925781, grade=<Grade.F: 'F'>, pass_threshold=False, strengths=['질문과 답변의 의미적 연관성이 높음'], weaknesses=['주제 관련성이 부족함', '질문의 핵심 키워드가 답변에 부족', '답변이 너무 간단함', '답변의 정확성이 의심됨'], recommendations=['더 상세하고 구체적인 답변 제공', '고객이 다음에 취할 수 있는 구체적 행동 안내', '추가 문의 채널 안내 추가', '더 정중하고 친근한 표현 사용', '질문의 주제에 더 집중한 답변 작성', '답변 길이를 늘려 더 충분한 정보 제공'], analysis_timestamp=datetime.datetime(2025, 6, 17, 14, 21, 28, 624174), processing_time_ms=319.75388526916504, model_info={'service': 'RefactoredAnalysisService', 'version': '2.2.0'})"], "processing_times": [177524.69778060913, 224.3189811706543, 319.76795196533203], "avg_processing_time": 59356.261571248375, "min_processing_time": 224.3189811706543, "max_processing_time": 177524.69778060913}, "beomi_kcelectra": {"model_name": "beomi_kcelectra", "total_requests": 3, "successful_requests": 3, "results": ["QAPair(id='test_1750137688', question='배송 언제 오나요?', answer='3일 내 배송 예정입니다.', semantic_similarity=0.8519544950645649, topic_relevance=0.39999999999999997, keyword_overlap=0.3333333333333333, answer_completeness=0.07, answer_accuracy=0.39999999999999997, answer_helpfulness=0.5, overall_score=0.4634886237661412, grade=<Grade.F: 'F'>, pass_threshold=False, strengths=['질문과 답변의 의미적 연관성이 높음'], weaknesses=['주제 관련성이 부족함', '답변이 너무 간단함', '답변의 정확성이 의심됨'], recommendations=['더 상세하고 구체적인 답변 제공', '고객이 다음에 취할 수 있는 구체적 행동 안내', '추가 문의 채널 안내 추가', '더 정중하고 친근한 표현 사용', '질문의 주제에 더 집중한 답변 작성', '답변 길이를 늘려 더 충분한 정보 제공'], analysis_timestamp=datetime.datetime(2025, 6, 17, 14, 22, 38, 201854), processing_time_ms=69577.51035690308, model_info={'service': 'RefactoredAnalysisService', 'version': '2.2.0'})", "QAPair(id='test_1750137758', question='환불 가능한가요?', answer='7일 내 환불 가능합니다.', semantic_similarity=0.8847371958367319, topic_relevance=0.65, keyword_overlap=0.5, answer_completeness=0.07, answer_accuracy=0.65, answer_helpfulness=0.5, overall_score=0.584184298959183, grade=<Grade.F: 'F'>, pass_threshold=False, strengths=['질문과 답변의 의미적 연관성이 높음'], weaknesses=['답변이 너무 간단함'], recommendations=['더 상세하고 구체적인 답변 제공', '고객이 다음에 취할 수 있는 구체적 행동 안내', '추가 문의 채널 안내 추가', '더 정중하고 친근한 표현 사용', '답변 길이를 늘려 더 충분한 정보 제공'], analysis_timestamp=datetime.datetime(2025, 6, 17, 14, 22, 38, 397518), processing_time_ms=195.6501007080078, model_info={'service': 'RefactoredAnalysisService', 'version': '2.2.0'})", "QAPair(id='test_1750137758', question='상품 사이즈가 어떻게 되나요?', answer='M, L, XL 사이즈 있습니다.', semantic_similarity=0.9427569805935736, topic_relevance=0.0, keyword_overlap=0.0, answer_completeness=0.09, answer_accuracy=0.0, answer_helpfulness=0.5, overall_score=0.2991892451483934, grade=<Grade.F: 'F'>, pass_threshold=False, strengths=['질문과 답변의 의미적 연관성이 높음'], weaknesses=['주제 관련성이 부족함', '질문의 핵심 키워드가 답변에 부족', '답변이 너무 간단함', '답변의 정확성이 의심됨'], recommendations=['더 상세하고 구체적인 답변 제공', '고객이 다음에 취할 수 있는 구체적 행동 안내', '추가 문의 채널 안내 추가', '더 정중하고 친근한 표현 사용', '질문의 주제에 더 집중한 답변 작성', '답변 길이를 늘려 더 충분한 정보 제공'], analysis_timestamp=datetime.datetime(2025, 6, 17, 14, 22, 38, 562120), processing_time_ms=164.58964347839355, model_info={'service': 'RefactoredAnalysisService', 'version': '2.2.0'})"], "processing_times": [69577.52704620361, 195.6632137298584, 164.60275650024414], "avg_processing_time": 23312.597672144573, "min_processing_time": 164.60275650024414, "max_processing_time": 69577.52704620361}, "koelectra_v3": {"model_name": "koelectra_v3", "total_requests": 3, "successful_requests": 3, "results": ["QAPair(id='test_1750137758', question='배송 언제 오나요?', answer='3일 내 배송 예정입니다.', semantic_similarity=0.9630021591725768, topic_relevance=0.39999999999999997, keyword_overlap=0.3333333333333333, answer_completeness=0.07, answer_accuracy=0.39999999999999997, answer_helpfulness=0.5, overall_score=0.4912505397931442, grade=<Grade.F: 'F'>, pass_threshold=False, strengths=['질문과 답변의 의미적 연관성이 높음'], weaknesses=['주제 관련성이 부족함', '답변이 너무 간단함', '답변의 정확성이 의심됨'], recommendations=['더 상세하고 구체적인 답변 제공', '고객이 다음에 취할 수 있는 구체적 행동 안내', '추가 문의 채널 안내 추가', '더 정중하고 친근한 표현 사용', '질문의 주제에 더 집중한 답변 작성', '답변 길이를 늘려 더 충분한 정보 제공'], analysis_timestamp=datetime.datetime(2025, 6, 17, 14, 23, 39, 776350), processing_time_ms=61214.130878448486, model_info={'service': 'RefactoredAnalysisService', 'version': '2.2.0'})", "QAPair(id='test_1750137819', question='환불 가능한가요?', answer='7일 내 환불 가능합니다.', semantic_similarity=0.9774316237099313, topic_relevance=0.65, keyword_overlap=0.5, answer_completeness=0.07, answer_accuracy=0.65, answer_helpfulness=0.5, overall_score=0.6073579059274828, grade=<Grade.D: 'D'>, pass_threshold=True, strengths=['질문과 답변의 의미적 연관성이 높음'], weaknesses=['답변이 너무 간단함'], recommendations=['더 상세하고 구체적인 답변 제공', '고객이 다음에 취할 수 있는 구체적 행동 안내', '추가 문의 채널 안내 추가', '더 정중하고 친근한 표현 사용', '답변 길이를 늘려 더 충분한 정보 제공'], analysis_timestamp=datetime.datetime(2025, 6, 17, 14, 23, 39, 802960), processing_time_ms=26.597023010253906, model_info={'service': 'RefactoredAnalysisService', 'version': '2.2.0'})", "QAPair(id='test_1750137819', question='상품 사이즈가 어떻게 되나요?', answer='M, L, XL 사이즈 있습니다.', semantic_similarity=0.9617273970449699, topic_relevance=0.0, keyword_overlap=0.0, answer_completeness=0.09, answer_accuracy=0.0, answer_helpfulness=0.5, overall_score=0.3039318492612425, grade=<Grade.F: 'F'>, pass_threshold=False, strengths=['질문과 답변의 의미적 연관성이 높음'], weaknesses=['주제 관련성이 부족함', '질문의 핵심 키워드가 답변에 부족', '답변이 너무 간단함', '답변의 정확성이 의심됨'], recommendations=['더 상세하고 구체적인 답변 제공', '고객이 다음에 취할 수 있는 구체적 행동 안내', '추가 문의 채널 안내 추가', '더 정중하고 친근한 표현 사용', '질문의 주제에 더 집중한 답변 작성', '답변 길이를 늘려 더 충분한 정보 제공'], analysis_timestamp=datetime.datetime(2025, 6, 17, 14, 23, 39, 985863), processing_time_ms=182.8920841217041, model_info={'service': 'RefactoredAnalysisService', 'version': '2.2.0'})"], "processing_times": [61214.14303779602, 26.607036590576172, 182.9051971435547], "avg_processing_time": 20474.551757176716, "min_processing_time": 26.607036590576172, "max_processing_time": 61214.14303779602}, "xlm_roberta_base": {"model_name": "xlm_roberta_base", "total_requests": 3, "successful_requests": 3, "results": ["QAPair(id='test_1750137819', question='배송 언제 오나요?', answer='3일 내 배송 예정입니다.', semantic_similarity=0.9956251673679357, topic_relevance=0.39999999999999997, keyword_overlap=0.3333333333333333, answer_completeness=0.07, answer_accuracy=0.39999999999999997, answer_helpfulness=0.5, overall_score=0.49940629184198393, grade=<Grade.F: 'F'>, pass_threshold=False, strengths=['질문과 답변의 의미적 연관성이 높음'], weaknesses=['주제 관련성이 부족함', '답변이 너무 간단함', '답변의 정확성이 의심됨'], recommendations=['더 상세하고 구체적인 답변 제공', '고객이 다음에 취할 수 있는 구체적 행동 안내', '추가 문의 채널 안내 추가', '더 정중하고 친근한 표현 사용', '질문의 주제에 더 집중한 답변 작성', '답변 길이를 늘려 더 충분한 정보 제공'], analysis_timestamp=datetime.datetime(2025, 6, 17, 14, 25, 44, 836149), processing_time_ms=124850.20589828491, model_info={'service': 'RefactoredAnalysisService', 'version': '2.2.0'})", "QAPair(id='test_1750137944', question='환불 가능한가요?', answer='7일 내 환불 가능합니다.', semantic_similarity=0.9968557610548597, topic_relevance=0.65, keyword_overlap=0.5, answer_completeness=0.07, answer_accuracy=0.65, answer_helpfulness=0.5, overall_score=0.6122139402637149, grade=<Grade.D: 'D'>, pass_threshold=True, strengths=['질문과 답변의 의미적 연관성이 높음'], weaknesses=['답변이 너무 간단함'], recommendations=['더 상세하고 구체적인 답변 제공', '고객이 다음에 취할 수 있는 구체적 행동 안내', '추가 문의 채널 안내 추가', '더 정중하고 친근한 표현 사용', '답변 길이를 늘려 더 충분한 정보 제공'], analysis_timestamp=datetime.datetime(2025, 6, 17, 14, 25, 44, 889621), processing_time_ms=53.45773696899414, model_info={'service': 'RefactoredAnalysisService', 'version': '2.2.0'})", "QAPair(id='test_1750137944', question='상품 사이즈가 어떻게 되나요?', answer='M, L, XL 사이즈 있습니다.', semantic_similarity=0.9923340051704529, topic_relevance=0.0, keyword_overlap=0.0, answer_completeness=0.09, answer_accuracy=0.0, answer_helpfulness=0.5, overall_score=0.31158350129261325, grade=<Grade.F: 'F'>, pass_threshold=False, strengths=['질문과 답변의 의미적 연관성이 높음'], weaknesses=['주제 관련성이 부족함', '질문의 핵심 키워드가 답변에 부족', '답변이 너무 간단함', '답변의 정확성이 의심됨'], recommendations=['더 상세하고 구체적인 답변 제공', '고객이 다음에 취할 수 있는 구체적 행동 안내', '추가 문의 채널 안내 추가', '더 정중하고 친근한 표현 사용', '질문의 주제에 더 집중한 답변 작성', '답변 길이를 늘려 더 충분한 정보 제공'], analysis_timestamp=datetime.datetime(2025, 6, 17, 14, 25, 44, 942967), processing_time_ms=53.32612991333008, model_info={'service': 'RefactoredAnalysisService', 'version': '2.2.0'})"], "processing_times": [124850.21781921387, 53.47704887390137, 53.33995819091797], "avg_processing_time": 41652.344942092896, "min_processing_time": 53.33995819091797, "max_processing_time": 124850.21781921387}, "multilingual_mini": {"model_name": "multilingual_mini", "total_requests": 3, "successful_requests": 3, "results": ["QAPair(id='test_1750137944', question='배송 언제 오나요?', answer='3일 내 배송 예정입니다.', semantic_similarity=0.6409290180027416, topic_relevance=0.39999999999999997, keyword_overlap=0.3333333333333333, answer_completeness=0.07, answer_accuracy=0.39999999999999997, answer_helpfulness=0.5, overall_score=0.4107322545006854, grade=<Grade.F: 'F'>, pass_threshold=False, strengths=[], weaknesses=['주제 관련성이 부족함', '답변이 너무 간단함', '답변의 정확성이 의심됨'], recommendations=['더 상세하고 구체적인 답변 제공', '고객이 다음에 취할 수 있는 구체적 행동 안내', '추가 문의 채널 안내 추가', '더 정중하고 친근한 표현 사용', '질문의 주제에 더 집중한 답변 작성', '답변 길이를 늘려 더 충분한 정보 제공'], analysis_timestamp=datetime.datetime(2025, 6, 17, 14, 25, 50, 175323), processing_time_ms=5232.251882553101, model_info={'service': 'RefactoredAnalysisService', 'version': '2.2.0'})", "QAPair(id='test_1750137950', question='환불 가능한가요?', answer='7일 내 환불 가능합니다.', semantic_similarity=0.529439482299983, topic_relevance=0.65, keyword_overlap=0.5, answer_completeness=0.07, answer_accuracy=0.65, answer_helpfulness=0.5, overall_score=0.49535987057499575, grade=<Grade.F: 'F'>, pass_threshold=False, strengths=[], weaknesses=['답변이 너무 간단함'], recommendations=['질문의 핵심 키워드를 답변에 더 많이 포함', '더 상세하고 구체적인 답변 제공', '고객이 다음에 취할 수 있는 구체적 행동 안내', '추가 문의 채널 안내 추가', '더 정중하고 친근한 표현 사용', '답변 길이를 늘려 더 충분한 정보 제공'], analysis_timestamp=datetime.datetime(2025, 6, 17, 14, 25, 50, 239247), processing_time_ms=63.90690803527832, model_info={'service': 'RefactoredAnalysisService', 'version': '2.2.0'})", "QAPair(id='test_1750137950', question='상품 사이즈가 어떻게 되나요?', answer='M, L, XL 사이즈 있습니다.', semantic_similarity=0.6105551311336487, topic_relevance=0.0, keyword_overlap=0.0, answer_completeness=0.09, answer_accuracy=0.0, answer_helpfulness=0.5, overall_score=0.21613878278341217, grade=<Grade.F: 'F'>, pass_threshold=False, strengths=[], weaknesses=['주제 관련성이 부족함', '질문의 핵심 키워드가 답변에 부족', '답변이 너무 간단함', '답변의 정확성이 의심됨'], recommendations=['더 상세하고 구체적인 답변 제공', '고객이 다음에 취할 수 있는 구체적 행동 안내', '추가 문의 채널 안내 추가', '더 정중하고 친근한 표현 사용', '질문의 주제에 더 집중한 답변 작성', '답변 길이를 늘려 더 충분한 정보 제공'], analysis_timestamp=datetime.datetime(2025, 6, 17, 14, 25, 50, 301542), processing_time_ms=62.28208541870117, model_info={'service': 'RefactoredAnalysisService', 'version': '2.2.0'})"], "processing_times": [5232.273101806641, 63.920021057128906, 62.296152114868164], "avg_processing_time": 1786.163091659546, "min_processing_time": 62.296152114868164, "max_processing_time": 5232.273101806641}}, "performance_metrics": {"kr_sbert_default": {"avg_score": 0.36559813077804576, "pass_rate": 0.0, "avg_processing_time": 2074.9282836914062, "throughput": 0.48194436784145017}, "klue_roberta_base": {"avg_score": 0.4149535545476499, "pass_rate": 0.0, "avg_processing_time": 651.8398920694987, "throughput": 1.5341190561767286}, "klue_roberta_large": {"avg_score": 0.449849482927108, "pass_rate": 0.0, "avg_processing_time": 59356.261571248375, "throughput": 0.01684742221845708}, "beomi_kcelectra": {"avg_score": 0.4489540559579059, "pass_rate": 0.0, "avg_processing_time": 23312.597672144573, "throughput": 0.04289526264140293}, "koelectra_v3": {"avg_score": 0.46751343166062315, "pass_rate": 33.33333333333333, "avg_processing_time": 20474.551757176716, "throughput": 0.04884111807964153}, "xlm_roberta_base": {"avg_score": 0.47440124446610404, "pass_rate": 33.33333333333333, "avg_processing_time": 41652.344942092896, "throughput": 0.02400825214979489}, "multilingual_mini": {"avg_score": 0.3740769692863644, "pass_rate": 0.0, "avg_processing_time": 1786.163091659546, "throughput": 0.5598592898204429}}, "accuracy_metrics": {"kr_sbert_default": {"semantic_similarity": 0.5597258564455164, "topic_relevance": 0.35000000000000003, "answer_accuracy": 0.35000000000000003, "consistency": 0.9695464129004538}, "klue_roberta_base": {"semantic_similarity": 0.7571475515239331, "topic_relevance": 0.35000000000000003, "answer_accuracy": 0.35000000000000003, "consistency": 0.975808557851234}, "klue_roberta_large": {"semantic_similarity": 0.8967312650417654, "topic_relevance": 0.35000000000000003, "answer_accuracy": 0.35000000000000003, "consistency": 0.9761046492089892}, "beomi_kcelectra": {"semantic_similarity": 0.8931495571649567, "topic_relevance": 0.35000000000000003, "answer_accuracy": 0.35000000000000003, "consistency": 0.9799463913351565}, "koelectra_v3": {"semantic_similarity": 0.967387059975826, "topic_relevance": 0.35000000000000003, "answer_accuracy": 0.35000000000000003, "consistency": 0.9770973933679605}, "xlm_roberta_base": {"semantic_similarity": 0.9949383111977493, "topic_relevance": 0.35000000000000003, "answer_accuracy": 0.35000000000000003, "consistency": 0.977456333698961}, "multilingual_mini": {"semantic_similarity": 0.5936412104787911, "topic_relevance": 0.35000000000000003, "answer_accuracy": 0.35000000000000003, "consistency": 0.9799129492478162}}, "comparison_summary": {"best_overall_score": "xlm_roberta_base", "fastest_processing": "klue_roberta_base", "highest_throughput": "klue_roberta_base", "most_consistent": "beomi_kcelectra", "performance_spread": {"score_range": {"min": 0.36559813077804576, "max": 0.47440124446610404, "avg": 0.427906695660543}, "speed_range": {"min": 651.8398920694987, "max": 59356.261571248375, "avg": 21329.81245858329}, "throughput_range": {"min": 0.01684742221845708, "max": 1.5341190561767286, "avg": 0.38693068127541685}}}, "recommendations": ["🏆 전체 성능 최고: xlm_roberta_base", "⚡ 처리 속도 최고: klue_roberta_base", "🔄 처리량 최고: klue_roberta_base", "📊 일관성 최고: be<PERSON>_kcelectra", "", "📋 사용 시나리오별 추천:", "• 실시간 처리: 처리 속도 최고 모델 사용", "• 대용량 배치: 처리량 최고 모델 사용", "• 고품질 분석: 전체 성능 최고 모델 사용", "• 안정적 서비스: 일관성 최고 모델 사용"], "storage_metadata": {"result_id": "comp_1750137502", "saved_at": "2025-06-17T14:25:50.301655", "filename": "comp_1750137502_20250617_142550.json", "tags": ["model_comparison", "auto_generated", "large_scale"], "description": "7개 모델 비교 (3건 요청)", "file_hash": "6d5c26f90f569374aa9b8db31d126bf9"}}