{"metadata": {"comparison_id": "comp_1750141234", "timestamp": "2025-06-17T15:20:34.929101", "models_tested": ["kr_sbert_default", "klue_roberta_base", "klue_roberta_large", "beomi_kcelectra", "koelectra_v3", "xlm_roberta_base", "multilingual_mini"], "total_requests": 3, "test_duration_seconds": 24.342052936553955}, "model_results": {"kr_sbert_default": {"model_name": "kr_sbert_default", "total_requests": 3, "successful_requests": 3, "results": ["QAPair(id='test_1750141234', question='배송 언제 오나요?', answer='3일 내 배송 예정입니다.', semantic_similarity=0.525014794782292, topic_relevance=0.39999999999999997, keyword_overlap=0.3333333333333333, answer_completeness=0.07, answer_accuracy=0.39999999999999997, answer_helpfulness=0.5, overall_score=0.38175369869557296, grade=<Grade.F: 'F'>, pass_threshold=False, strengths=[], weaknesses=['주제 관련성이 부족함', '답변이 너무 간단함', '답변의 정확성이 의심됨'], recommendations=['질문의 핵심 키워드를 답변에 더 많이 포함', '더 상세하고 구체적인 답변 제공', '고객이 다음에 취할 수 있는 구체적 행동 안내', '추가 문의 채널 안내 추가', '더 정중하고 친근한 표현 사용', '질문의 주제에 더 집중한 답변 작성', '답변 길이를 늘려 더 충분한 정보 제공'], analysis_timestamp=datetime.datetime(2025, 6, 17, 15, 20, 40, 87500), processing_time_ms=5158.344030380249, model_info={'service': 'RefactoredAnalysisService', 'version': '2.2.0'})", "QAPair(id='test_1750141240', question='환불 가능한가요?', answer='7일 내 환불 가능합니다.', semantic_similarity=0.6847851351855667, topic_relevance=0.65, keyword_overlap=0.5, answer_completeness=0.07, answer_accuracy=0.65, answer_helpfulness=0.5, overall_score=0.5341962837963917, grade=<Grade.F: 'F'>, pass_threshold=False, strengths=[], weaknesses=['답변이 너무 간단함'], recommendations=['더 상세하고 구체적인 답변 제공', '고객이 다음에 취할 수 있는 구체적 행동 안내', '추가 문의 채널 안내 추가', '더 정중하고 친근한 표현 사용', '답변 길이를 늘려 더 충분한 정보 제공'], analysis_timestamp=datetime.datetime(2025, 6, 17, 15, 20, 40, 129206), processing_time_ms=41.68105125427246, model_info={'service': 'RefactoredAnalysisService', 'version': '2.2.0'})", "QAPair(id='test_1750141240', question='상품 사이즈가 어떻게 되나요?', answer='M, L, XL 사이즈 있습니다.', semantic_similarity=0.4693776393686905, topic_relevance=0.0, keyword_overlap=0.0, answer_completeness=0.09, answer_accuracy=0.0, answer_helpfulness=0.5, overall_score=0.18084440984217262, grade=<Grade.F: 'F'>, pass_threshold=False, strengths=[], weaknesses=['질문과 답변의 의미적 연관성이 낮음', '주제 관련성이 부족함', '질문의 핵심 키워드가 답변에 부족', '답변이 너무 간단함', '답변의 정확성이 의심됨'], recommendations=['질문의 핵심 키워드를 답변에 더 많이 포함', '더 상세하고 구체적인 답변 제공', '고객이 다음에 취할 수 있는 구체적 행동 안내', '추가 문의 채널 안내 추가', '더 정중하고 친근한 표현 사용', '질문의 주제에 더 집중한 답변 작성', '답변 길이를 늘려 더 충분한 정보 제공'], analysis_timestamp=datetime.datetime(2025, 6, 17, 15, 20, 40, 170716), processing_time_ms=41.49818420410156, model_info={'service': 'RefactoredAnalysisService', 'version': '2.2.0'})"], "processing_times": [5158.361196517944, 41.699886322021484, 41.51105880737305], "avg_processing_time": 1747.1907138824463, "min_processing_time": 41.51105880737305, "max_processing_time": 5158.361196517944}, "klue_roberta_base": {"model_name": "klue_roberta_base", "total_requests": 3, "successful_requests": 3, "results": ["QAPair(id='test_1750141240', question='배송 언제 오나요?', answer='3일 내 배송 예정입니다.', semantic_similarity=0.7479740269934663, topic_relevance=0.39999999999999997, keyword_overlap=0.3333333333333333, answer_completeness=0.07, answer_accuracy=0.39999999999999997, answer_helpfulness=0.5, overall_score=0.43749350674836657, grade=<Grade.F: 'F'>, pass_threshold=False, strengths=[], weaknesses=['주제 관련성이 부족함', '답변이 너무 간단함', '답변의 정확성이 의심됨'], recommendations=['더 상세하고 구체적인 답변 제공', '고객이 다음에 취할 수 있는 구체적 행동 안내', '추가 문의 채널 안내 추가', '더 정중하고 친근한 표현 사용', '질문의 주제에 더 집중한 답변 작성', '답변 길이를 늘려 더 충분한 정보 제공'], analysis_timestamp=datetime.datetime(2025, 6, 17, 15, 20, 42, 803548), processing_time_ms=2630.7849884033203, model_info={'service': 'RefactoredAnalysisService', 'version': '2.2.0'})", "QAPair(id='test_1750141242', question='환불 가능한가요?', answer='7일 내 환불 가능합니다.', semantic_similarity=0.787684095784327, topic_relevance=0.65, keyword_overlap=0.5, answer_completeness=0.07, answer_accuracy=0.65, answer_helpfulness=0.5, overall_score=0.5599210239460818, grade=<Grade.F: 'F'>, pass_threshold=False, strengths=[], weaknesses=['답변이 너무 간단함'], recommendations=['더 상세하고 구체적인 답변 제공', '고객이 다음에 취할 수 있는 구체적 행동 안내', '추가 문의 채널 안내 추가', '더 정중하고 친근한 표현 사용', '답변 길이를 늘려 더 충분한 정보 제공'], analysis_timestamp=datetime.datetime(2025, 6, 17, 15, 20, 42, 835567), processing_time_ms=32.00531005859375, model_info={'service': 'RefactoredAnalysisService', 'version': '2.2.0'})", "QAPair(id='test_1750141242', question='상품 사이즈가 어떻게 되나요?', answer='M, L, XL 사이즈 있습니다.', semantic_similarity=0.735784531794006, topic_relevance=0.0, keyword_overlap=0.0, answer_completeness=0.09, answer_accuracy=0.0, answer_helpfulness=0.5, overall_score=0.2474461329485015, grade=<Grade.F: 'F'>, pass_threshold=False, strengths=[], weaknesses=['주제 관련성이 부족함', '질문의 핵심 키워드가 답변에 부족', '답변이 너무 간단함', '답변의 정확성이 의심됨'], recommendations=['더 상세하고 구체적인 답변 제공', '고객이 다음에 취할 수 있는 구체적 행동 안내', '추가 문의 채널 안내 추가', '더 정중하고 친근한 표현 사용', '질문의 주제에 더 집중한 답변 작성', '답변 길이를 늘려 더 충분한 정보 제공'], analysis_timestamp=datetime.datetime(2025, 6, 17, 15, 20, 43, 55287), processing_time_ms=219.70701217651367, model_info={'service': 'RefactoredAnalysisService', 'version': '2.2.0'})"], "processing_times": [2630.798101425171, 32.01770782470703, 219.72012519836426], "avg_processing_time": 960.8453114827474, "min_processing_time": 32.01770782470703, "max_processing_time": 2630.798101425171}, "klue_roberta_large": {"model_name": "klue_roberta_large", "total_requests": 3, "successful_requests": 3, "results": ["QAPair(id='test_1750141243', question='배송 언제 오나요?', answer='3일 내 배송 예정입니다.', semantic_similarity=0.9006602091114247, topic_relevance=0.39999999999999997, keyword_overlap=0.3333333333333333, answer_completeness=0.07, answer_accuracy=0.39999999999999997, answer_helpfulness=0.5, overall_score=0.47566505227785616, grade=<Grade.F: 'F'>, pass_threshold=False, strengths=['질문과 답변의 의미적 연관성이 높음'], weaknesses=['주제 관련성이 부족함', '답변이 너무 간단함', '답변의 정확성이 의심됨'], recommendations=['더 상세하고 구체적인 답변 제공', '고객이 다음에 취할 수 있는 구체적 행동 안내', '추가 문의 채널 안내 추가', '더 정중하고 친근한 표현 사용', '질문의 주제에 더 집중한 답변 작성', '답변 길이를 늘려 더 충분한 정보 제공'], analysis_timestamp=datetime.datetime(2025, 6, 17, 15, 20, 46, 5895), processing_time_ms=2950.5109786987305, model_info={'service': 'RefactoredAnalysisService', 'version': '2.2.0'})", "QAPair(id='test_1750141246', question='환불 가능한가요?', answer='7일 내 환불 가능합니다.', semantic_similarity=0.9151921244435589, topic_relevance=0.65, keyword_overlap=0.5, answer_completeness=0.07, answer_accuracy=0.65, answer_helpfulness=0.5, overall_score=0.5917980311108897, grade=<Grade.F: 'F'>, pass_threshold=False, strengths=['질문과 답변의 의미적 연관성이 높음'], weaknesses=['답변이 너무 간단함'], recommendations=['더 상세하고 구체적인 답변 제공', '고객이 다음에 취할 수 있는 구체적 행동 안내', '추가 문의 채널 안내 추가', '더 정중하고 친근한 표현 사용', '답변 길이를 늘려 더 충분한 정보 제공'], analysis_timestamp=datetime.datetime(2025, 6, 17, 15, 20, 46, 199822), processing_time_ms=193.90606880187988, model_info={'service': 'RefactoredAnalysisService', 'version': '2.2.0'})", "QAPair(id='test_1750141246', question='상품 사이즈가 어떻게 되나요?', answer='M, L, XL 사이즈 있습니다.', semantic_similarity=0.8743414615703123, topic_relevance=0.0, keyword_overlap=0.0, answer_completeness=0.09, answer_accuracy=0.0, answer_helpfulness=0.5, overall_score=0.2820853653925781, grade=<Grade.F: 'F'>, pass_threshold=False, strengths=['질문과 답변의 의미적 연관성이 높음'], weaknesses=['주제 관련성이 부족함', '질문의 핵심 키워드가 답변에 부족', '답변이 너무 간단함', '답변의 정확성이 의심됨'], recommendations=['더 상세하고 구체적인 답변 제공', '고객이 다음에 취할 수 있는 구체적 행동 안내', '추가 문의 채널 안내 추가', '더 정중하고 친근한 표현 사용', '질문의 주제에 더 집중한 답변 작성', '답변 길이를 늘려 더 충분한 정보 제공'], analysis_timestamp=datetime.datetime(2025, 6, 17, 15, 20, 46, 499158), processing_time_ms=299.32498931884766, model_info={'service': 'RefactoredAnalysisService', 'version': '2.2.0'})"], "processing_times": [2950.5321979522705, 193.91894340515137, 299.33786392211914], "avg_processing_time": 1147.9296684265137, "min_processing_time": 193.91894340515137, "max_processing_time": 2950.5321979522705}, "beomi_kcelectra": {"model_name": "beomi_kcelectra", "total_requests": 3, "successful_requests": 3, "results": ["QAPair(id='test_1750141246', question='배송 언제 오나요?', answer='3일 내 배송 예정입니다.', semantic_similarity=0.8519544950645649, topic_relevance=0.39999999999999997, keyword_overlap=0.3333333333333333, answer_completeness=0.07, answer_accuracy=0.39999999999999997, answer_helpfulness=0.5, overall_score=0.4634886237661412, grade=<Grade.F: 'F'>, pass_threshold=False, strengths=['질문과 답변의 의미적 연관성이 높음'], weaknesses=['주제 관련성이 부족함', '답변이 너무 간단함', '답변의 정확성이 의심됨'], recommendations=['더 상세하고 구체적인 답변 제공', '고객이 다음에 취할 수 있는 구체적 행동 안내', '추가 문의 채널 안내 추가', '더 정중하고 친근한 표현 사용', '질문의 주제에 더 집중한 답변 작성', '답변 길이를 늘려 더 충분한 정보 제공'], analysis_timestamp=datetime.datetime(2025, 6, 17, 15, 20, 48, 901377), processing_time_ms=2402.106761932373, model_info={'service': 'RefactoredAnalysisService', 'version': '2.2.0'})", "QAPair(id='test_1750141248', question='환불 가능한가요?', answer='7일 내 환불 가능합니다.', semantic_similarity=0.8847371958367319, topic_relevance=0.65, keyword_overlap=0.5, answer_completeness=0.07, answer_accuracy=0.65, answer_helpfulness=0.5, overall_score=0.584184298959183, grade=<Grade.F: 'F'>, pass_threshold=False, strengths=['질문과 답변의 의미적 연관성이 높음'], weaknesses=['답변이 너무 간단함'], recommendations=['더 상세하고 구체적인 답변 제공', '고객이 다음에 취할 수 있는 구체적 행동 안내', '추가 문의 채널 안내 추가', '더 정중하고 친근한 표현 사용', '답변 길이를 늘려 더 충분한 정보 제공'], analysis_timestamp=datetime.datetime(2025, 6, 17, 15, 20, 49, 88129), processing_time_ms=186.73205375671387, model_info={'service': 'RefactoredAnalysisService', 'version': '2.2.0'})", "QAPair(id='test_1750141249', question='상품 사이즈가 어떻게 되나요?', answer='M, L, XL 사이즈 있습니다.', semantic_similarity=0.9427569805935736, topic_relevance=0.0, keyword_overlap=0.0, answer_completeness=0.09, answer_accuracy=0.0, answer_helpfulness=0.5, overall_score=0.2991892451483934, grade=<Grade.F: 'F'>, pass_threshold=False, strengths=['질문과 답변의 의미적 연관성이 높음'], weaknesses=['주제 관련성이 부족함', '질문의 핵심 키워드가 답변에 부족', '답변이 너무 간단함', '답변의 정확성이 의심됨'], recommendations=['더 상세하고 구체적인 답변 제공', '고객이 다음에 취할 수 있는 구체적 행동 안내', '추가 문의 채널 안내 추가', '더 정중하고 친근한 표현 사용', '질문의 주제에 더 집중한 답변 작성', '답변 길이를 늘려 더 충분한 정보 제공'], analysis_timestamp=datetime.datetime(2025, 6, 17, 15, 20, 49, 238333), processing_time_ms=150.1922607421875, model_info={'service': 'RefactoredAnalysisService', 'version': '2.2.0'})"], "processing_times": [2402.125120162964, 186.74397468566895, 150.20513534545898], "avg_processing_time": 913.0247433980306, "min_processing_time": 150.20513534545898, "max_processing_time": 2402.125120162964}, "koelectra_v3": {"model_name": "koelectra_v3", "total_requests": 3, "successful_requests": 3, "results": ["QAPair(id='test_1750141249', question='배송 언제 오나요?', answer='3일 내 배송 예정입니다.', semantic_similarity=0.9630021591725768, topic_relevance=0.39999999999999997, keyword_overlap=0.3333333333333333, answer_completeness=0.07, answer_accuracy=0.39999999999999997, answer_helpfulness=0.5, overall_score=0.4912505397931442, grade=<Grade.F: 'F'>, pass_threshold=False, strengths=['질문과 답변의 의미적 연관성이 높음'], weaknesses=['주제 관련성이 부족함', '답변이 너무 간단함', '답변의 정확성이 의심됨'], recommendations=['더 상세하고 구체적인 답변 제공', '고객이 다음에 취할 수 있는 구체적 행동 안내', '추가 문의 채널 안내 추가', '더 정중하고 친근한 표현 사용', '질문의 주제에 더 집중한 답변 작성', '답변 길이를 늘려 더 충분한 정보 제공'], analysis_timestamp=datetime.datetime(2025, 6, 17, 15, 20, 51, 494699), processing_time_ms=2256.274938583374, model_info={'service': 'RefactoredAnalysisService', 'version': '2.2.0'})", "QAPair(id='test_1750141251', question='환불 가능한가요?', answer='7일 내 환불 가능합니다.', semantic_similarity=0.9774316237099313, topic_relevance=0.65, keyword_overlap=0.5, answer_completeness=0.07, answer_accuracy=0.65, answer_helpfulness=0.5, overall_score=0.6073579059274828, grade=<Grade.D: 'D'>, pass_threshold=True, strengths=['질문과 답변의 의미적 연관성이 높음'], weaknesses=['답변이 너무 간단함'], recommendations=['더 상세하고 구체적인 답변 제공', '고객이 다음에 취할 수 있는 구체적 행동 안내', '추가 문의 채널 안내 추가', '더 정중하고 친근한 표현 사용', '답변 길이를 늘려 더 충분한 정보 제공'], analysis_timestamp=datetime.datetime(2025, 6, 17, 15, 20, 51, 513386), processing_time_ms=18.676042556762695, model_info={'service': 'RefactoredAnalysisService', 'version': '2.2.0'})", "QAPair(id='test_1750141251', question='상품 사이즈가 어떻게 되나요?', answer='M, L, XL 사이즈 있습니다.', semantic_similarity=0.9617273970449699, topic_relevance=0.0, keyword_overlap=0.0, answer_completeness=0.09, answer_accuracy=0.0, answer_helpfulness=0.5, overall_score=0.3039318492612425, grade=<Grade.F: 'F'>, pass_threshold=False, strengths=['질문과 답변의 의미적 연관성이 높음'], weaknesses=['주제 관련성이 부족함', '질문의 핵심 키워드가 답변에 부족', '답변이 너무 간단함', '답변의 정확성이 의심됨'], recommendations=['더 상세하고 구체적인 답변 제공', '고객이 다음에 취할 수 있는 구체적 행동 안내', '추가 문의 채널 안내 추가', '더 정중하고 친근한 표현 사용', '질문의 주제에 더 집중한 답변 작성', '답변 길이를 늘려 더 충분한 정보 제공'], analysis_timestamp=datetime.datetime(2025, 6, 17, 15, 20, 51, 671643), processing_time_ms=158.2472324371338, model_info={'service': 'RefactoredAnalysisService', 'version': '2.2.0'})"], "processing_times": [2256.286859512329, 18.685102462768555, 158.25772285461426], "avg_processing_time": 811.0765616099039, "min_processing_time": 18.685102462768555, "max_processing_time": 2256.286859512329}, "xlm_roberta_base": {"model_name": "xlm_roberta_base", "total_requests": 3, "successful_requests": 3, "results": ["QAPair(id='test_1750141251', question='배송 언제 오나요?', answer='3일 내 배송 예정입니다.', semantic_similarity=0.9956251673679357, topic_relevance=0.39999999999999997, keyword_overlap=0.3333333333333333, answer_completeness=0.07, answer_accuracy=0.39999999999999997, answer_helpfulness=0.5, overall_score=0.49940629184198393, grade=<Grade.F: 'F'>, pass_threshold=False, strengths=['질문과 답변의 의미적 연관성이 높음'], weaknesses=['주제 관련성이 부족함', '답변이 너무 간단함', '답변의 정확성이 의심됨'], recommendations=['더 상세하고 구체적인 답변 제공', '고객이 다음에 취할 수 있는 구체적 행동 안내', '추가 문의 채널 안내 추가', '더 정중하고 친근한 표현 사용', '질문의 주제에 더 집중한 답변 작성', '답변 길이를 늘려 더 충분한 정보 제공'], analysis_timestamp=datetime.datetime(2025, 6, 17, 15, 20, 54, 817103), processing_time_ms=3145.3640460968018, model_info={'service': 'RefactoredAnalysisService', 'version': '2.2.0'})", "QAPair(id='test_1750141254', question='환불 가능한가요?', answer='7일 내 환불 가능합니다.', semantic_similarity=0.9968557610548597, topic_relevance=0.65, keyword_overlap=0.5, answer_completeness=0.07, answer_accuracy=0.65, answer_helpfulness=0.5, overall_score=0.6122139402637149, grade=<Grade.D: 'D'>, pass_threshold=True, strengths=['질문과 답변의 의미적 연관성이 높음'], weaknesses=['답변이 너무 간단함'], recommendations=['더 상세하고 구체적인 답변 제공', '고객이 다음에 취할 수 있는 구체적 행동 안내', '추가 문의 채널 안내 추가', '더 정중하고 친근한 표현 사용', '답변 길이를 늘려 더 충분한 정보 제공'], analysis_timestamp=datetime.datetime(2025, 6, 17, 15, 20, 54, 854553), processing_time_ms=37.43791580200195, model_info={'service': 'RefactoredAnalysisService', 'version': '2.2.0'})", "QAPair(id='test_1750141254', question='상품 사이즈가 어떻게 되나요?', answer='M, L, XL 사이즈 있습니다.', semantic_similarity=0.9923340051704529, topic_relevance=0.0, keyword_overlap=0.0, answer_completeness=0.09, answer_accuracy=0.0, answer_helpfulness=0.5, overall_score=0.31158350129261325, grade=<Grade.F: 'F'>, pass_threshold=False, strengths=['질문과 답변의 의미적 연관성이 높음'], weaknesses=['주제 관련성이 부족함', '질문의 핵심 키워드가 답변에 부족', '답변이 너무 간단함', '답변의 정확성이 의심됨'], recommendations=['더 상세하고 구체적인 답변 제공', '고객이 다음에 취할 수 있는 구체적 행동 안내', '추가 문의 채널 안내 추가', '더 정중하고 친근한 표현 사용', '질문의 주제에 더 집중한 답변 작성', '답변 길이를 늘려 더 충분한 정보 제공'], analysis_timestamp=datetime.datetime(2025, 6, 17, 15, 20, 54, 891062), processing_time_ms=36.49592399597168, model_info={'service': 'RefactoredAnalysisService', 'version': '2.2.0'})"], "processing_times": [3145.375967025757, 37.44983673095703, 36.51118278503418], "avg_processing_time": 1073.1123288472493, "min_processing_time": 36.51118278503418, "max_processing_time": 3145.375967025757}, "multilingual_mini": {"model_name": "multilingual_mini", "total_requests": 3, "successful_requests": 3, "results": ["QAPair(id='test_1750141254', question='배송 언제 오나요?', answer='3일 내 배송 예정입니다.', semantic_similarity=0.6409290180027416, topic_relevance=0.39999999999999997, keyword_overlap=0.3333333333333333, answer_completeness=0.07, answer_accuracy=0.39999999999999997, answer_helpfulness=0.5, overall_score=0.4107322545006854, grade=<Grade.F: 'F'>, pass_threshold=False, strengths=[], weaknesses=['주제 관련성이 부족함', '답변이 너무 간단함', '답변의 정확성이 의심됨'], recommendations=['더 상세하고 구체적인 답변 제공', '고객이 다음에 취할 수 있는 구체적 행동 안내', '추가 문의 채널 안내 추가', '더 정중하고 친근한 표현 사용', '질문의 주제에 더 집중한 답변 작성', '답변 길이를 늘려 더 충분한 정보 제공'], analysis_timestamp=datetime.datetime(2025, 6, 17, 15, 20, 58, 666721), processing_time_ms=3775.57110786438, model_info={'service': 'RefactoredAnalysisService', 'version': '2.2.0'})", "QAPair(id='test_1750141258', question='환불 가능한가요?', answer='7일 내 환불 가능합니다.', semantic_similarity=0.529439482299983, topic_relevance=0.65, keyword_overlap=0.5, answer_completeness=0.07, answer_accuracy=0.65, answer_helpfulness=0.5, overall_score=0.49535987057499575, grade=<Grade.F: 'F'>, pass_threshold=False, strengths=[], weaknesses=['답변이 너무 간단함'], recommendations=['질문의 핵심 키워드를 답변에 더 많이 포함', '더 상세하고 구체적인 답변 제공', '고객이 다음에 취할 수 있는 구체적 행동 안내', '추가 문의 채널 안내 추가', '더 정중하고 친근한 표현 사용', '답변 길이를 늘려 더 충분한 정보 제공'], analysis_timestamp=datetime.datetime(2025, 6, 17, 15, 20, 59, 2370), processing_time_ms=335.6280326843262, model_info={'service': 'RefactoredAnalysisService', 'version': '2.2.0'})", "QAPair(id='test_1750141259', question='상품 사이즈가 어떻게 되나요?', answer='M, L, XL 사이즈 있습니다.', semantic_similarity=0.6105551311336487, topic_relevance=0.0, keyword_overlap=0.0, answer_completeness=0.09, answer_accuracy=0.0, answer_helpfulness=0.5, overall_score=0.21613878278341217, grade=<Grade.F: 'F'>, pass_threshold=False, strengths=[], weaknesses=['주제 관련성이 부족함', '질문의 핵심 키워드가 답변에 부족', '답변이 너무 간단함', '답변의 정확성이 의심됨'], recommendations=['더 상세하고 구체적인 답변 제공', '고객이 다음에 취할 수 있는 구체적 행동 안내', '추가 문의 채널 안내 추가', '더 정중하고 친근한 표현 사용', '질문의 주제에 더 집중한 답변 작성', '답변 길이를 늘려 더 충분한 정보 제공'], analysis_timestamp=datetime.datetime(2025, 6, 17, 15, 20, 59, 271104), processing_time_ms=268.718957901001, model_info={'service': 'RefactoredAnalysisService', 'version': '2.2.0'})"], "processing_times": [3775.5911350250244, 335.64281463623047, 268.73302459716797], "avg_processing_time": 1459.9889914194744, "min_processing_time": 268.73302459716797, "max_processing_time": 3775.5911350250244}}, "performance_metrics": {"kr_sbert_default": {"avg_score": 0.36559813077804576, "pass_rate": 0.0, "avg_processing_time": 1747.1907138824463, "throughput": 0.5723473642885224}, "klue_roberta_base": {"avg_score": 0.4149535545476499, "pass_rate": 0.0, "avg_processing_time": 960.8453114827474, "throughput": 1.0407502519389207}, "klue_roberta_large": {"avg_score": 0.449849482927108, "pass_rate": 0.0, "avg_processing_time": 1147.9296684265137, "throughput": 0.8711335088766515}, "beomi_kcelectra": {"avg_score": 0.4489540559579059, "pass_rate": 0.0, "avg_processing_time": 913.0247433980306, "throughput": 1.0952605690381085}, "koelectra_v3": {"avg_score": 0.46751343166062315, "pass_rate": 33.33333333333333, "avg_processing_time": 811.0765616099039, "throughput": 1.2329292292889125}, "xlm_roberta_base": {"avg_score": 0.47440124446610404, "pass_rate": 33.33333333333333, "avg_processing_time": 1073.1123288472493, "throughput": 0.9318688949126254}, "multilingual_mini": {"avg_score": 0.3740769692863644, "pass_rate": 0.0, "avg_processing_time": 1459.9889914194744, "throughput": 0.6849366713565079}}, "accuracy_metrics": {"kr_sbert_default": {"semantic_similarity": 0.5597258564455164, "topic_relevance": 0.35000000000000003, "answer_accuracy": 0.35000000000000003, "consistency": 0.9695464129004538}, "klue_roberta_base": {"semantic_similarity": 0.7571475515239331, "topic_relevance": 0.35000000000000003, "answer_accuracy": 0.35000000000000003, "consistency": 0.975808557851234}, "klue_roberta_large": {"semantic_similarity": 0.8967312650417654, "topic_relevance": 0.35000000000000003, "answer_accuracy": 0.35000000000000003, "consistency": 0.9761046492089892}, "beomi_kcelectra": {"semantic_similarity": 0.8931495571649567, "topic_relevance": 0.35000000000000003, "answer_accuracy": 0.35000000000000003, "consistency": 0.9799463913351565}, "koelectra_v3": {"semantic_similarity": 0.967387059975826, "topic_relevance": 0.35000000000000003, "answer_accuracy": 0.35000000000000003, "consistency": 0.9770973933679605}, "xlm_roberta_base": {"semantic_similarity": 0.9949383111977493, "topic_relevance": 0.35000000000000003, "answer_accuracy": 0.35000000000000003, "consistency": 0.977456333698961}, "multilingual_mini": {"semantic_similarity": 0.5936412104787911, "topic_relevance": 0.35000000000000003, "answer_accuracy": 0.35000000000000003, "consistency": 0.9799129492478162}}, "comparison_summary": {"best_overall_score": "xlm_roberta_base", "fastest_processing": "koelectra_v3", "highest_throughput": "koelectra_v3", "most_consistent": "beomi_kcelectra", "performance_spread": {"score_range": {"min": 0.36559813077804576, "max": 0.47440124446610404, "avg": 0.427906695660543}, "speed_range": {"min": 811.0765616099039, "max": 1747.1907138824463, "avg": 1159.0240455809094}, "throughput_range": {"min": 0.5723473642885224, "max": 1.2329292292889125, "avg": 0.9184609271000356}}}, "recommendations": ["🏆 전체 성능 최고: xlm_roberta_base", "⚡ 처리 속도 최고: koelectra_v3", "🔄 처리량 최고: koelectra_v3", "📊 일관성 최고: be<PERSON>_kcelectra", "", "📋 사용 시나리오별 추천:", "• 실시간 처리: 처리 속도 최고 모델 사용", "• 대용량 배치: 처리량 최고 모델 사용", "• 고품질 분석: 전체 성능 최고 모델 사용", "• 안정적 서비스: 일관성 최고 모델 사용"], "storage_metadata": {"result_id": "comp_1750141234", "saved_at": "2025-06-17T15:20:59.271217", "filename": "comp_1750141234_20250617_152059.json", "tags": ["model_comparison", "auto_generated", "large_scale"], "description": "7개 모델 비교 (3건 요청)", "file_hash": "e88271100af61d93191eea71928f2fb5"}}