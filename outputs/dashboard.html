
<!DOCTYPE html>
<html lang="ko">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>모델 성능 비교 대시보드</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #eee;
        }
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .metric-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }
        .chart-container {
            margin: 30px 0;
            height: 400px;
        }
        .recommendations {
            background: #e7f3ff;
            padding: 20px;
            border-radius: 8px;
            margin-top: 30px;
        }
        .model-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        .model-table th, .model-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        .model-table th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        .best-model {
            background-color: #d4edda;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 모델 성능 비교 대시보드</h1>
            <p>비교 ID: comp_1750136305 | 
               실행 시간: 2025-06-17T13:58:25.797915</p>
            <p>테스트 모델: kr_sbert_default, klue_roberta, multilingual_mini | 
               총 요청: 3건</p>
        </div>
        
        <div class="metrics-grid">
            <div class="metric-card">
                <h3>🏆 최고 성능</h3>
                <p>klue_roberta</p>
            </div>
            <div class="metric-card">
                <h3>⚡ 최고 속도</h3>
                <p>kr_sbert_default</p>
            </div>
            <div class="metric-card">
                <h3>🔄 최고 처리량</h3>
                <p>kr_sbert_default</p>
            </div>
            <div class="metric-card">
                <h3>📊 최고 일관성</h3>
                <p>multilingual_mini</p>
            </div>
        </div>
        
        <div class="chart-container">
            <canvas id="performanceChart"></canvas>
        </div>
        
        <div class="chart-container">
            <canvas id="speedChart"></canvas>
        </div>
        
        <h2>📊 상세 메트릭</h2>
        <table class="model-table">
            <thead>
                <tr>
                    <th>모델</th>
                    <th>평균 점수</th>
                    <th>통과율 (%)</th>
                    <th>처리 시간 (ms)</th>
                    <th>처리량 (req/s)</th>
                    <th>일관성</th>
                </tr>
            </thead>
            <tbody>
                
                <tr class="">
                    <td>kr_sbert_default</td>
                    <td>0.366</td>
                    <td>0.0</td>
                    <td>1960.59</td>
                    <td>0.51</td>
                    <td>0.970</td>
                </tr>
            
                <tr class="best-model">
                    <td>klue_roberta</td>
                    <td>0.415</td>
                    <td>0.0</td>
                    <td>17969.27</td>
                    <td>0.06</td>
                    <td>0.976</td>
                </tr>
            
                <tr class="">
                    <td>multilingual_mini</td>
                    <td>0.374</td>
                    <td>0.0</td>
                    <td>21264.62</td>
                    <td>0.05</td>
                    <td>0.980</td>
                </tr>
            
            </tbody>
        </table>
        
        <div class="recommendations">
            <h2>💡 추천사항</h2>
            <ul>
                <li>🏆 전체 성능 최고: klue_roberta</li><li>⚡ 처리 속도 최고: kr_sbert_default</li><li>🔄 처리량 최고: kr_sbert_default</li><li>📊 일관성 최고: multilingual_mini</li><li>📋 사용 시나리오별 추천:</li><li>• 실시간 처리: 처리 속도 최고 모델 사용</li><li>• 대용량 배치: 처리량 최고 모델 사용</li><li>• 고품질 분석: 전체 성능 최고 모델 사용</li><li>• 안정적 서비스: 일관성 최고 모델 사용</li>
            </ul>
        </div>
    </div>
    
    <script>
        // 성능 차트
        const performanceCtx = document.getElementById('performanceChart').getContext('2d');
        new Chart(performanceCtx, {
            type: 'bar',
            data: {"labels": ["kr_sbert_default", "klue_roberta", "multilingual_mini"], "datasets": [{"label": "\ud3c9\uade0 \uc810\uc218", "data": [0.36559813077804576, 0.4149535545476499, 0.3740769692863644], "backgroundColor": "rgba(54, 162, 235, 0.8)"}]},
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: '모델별 성능 비교'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 1
                    }
                }
            }
        });
        
        // 속도 차트
        const speedCtx = document.getElementById('speedChart').getContext('2d');
        new Chart(speedCtx, {
            type: 'bar',
            data: {"labels": ["kr_sbert_default", "klue_roberta", "multilingual_mini"], "datasets": [{"label": "\ucc98\ub9ac \uc2dc\uac04 (ms)", "data": [1960.5940977732341, 17969.266335169475, 21264.622688293457], "backgroundColor": "rgba(255, 99, 132, 0.8)"}]},
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: '모델별 처리 속도 비교'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    </script>
</body>
</html>
        