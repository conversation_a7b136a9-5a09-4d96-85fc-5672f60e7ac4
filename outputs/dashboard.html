
<!DOCTYPE html>
<html lang="ko">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>모델 성능 비교 대시보드</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #eee;
        }
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .metric-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }
        .chart-container {
            margin: 30px 0;
            height: 400px;
        }
        .recommendations {
            background: #e7f3ff;
            padding: 20px;
            border-radius: 8px;
            margin-top: 30px;
        }
        .model-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        .model-table th, .model-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        .model-table th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        .best-model {
            background-color: #d4edda;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 모델 성능 비교 대시보드</h1>
            <p>비교 ID: comp_1750142930 | 
               실행 시간: 2025-06-17T15:48:50.827332</p>
            <p>테스트 모델: kr_sbert_default, klue_roberta_base, klue_roberta_large, beomi_kcelectra, koelectra_v3, xlm_roberta_base, multilingual_mini | 
               총 요청: 3건</p>
        </div>
        
        <div class="metrics-grid">
            <div class="metric-card">
                <h3>🏆 최고 성능</h3>
                <p>xlm_roberta_base</p>
            </div>
            <div class="metric-card">
                <h3>⚡ 최고 속도</h3>
                <p>beomi_kcelectra</p>
            </div>
            <div class="metric-card">
                <h3>🔄 최고 처리량</h3>
                <p>beomi_kcelectra</p>
            </div>
            <div class="metric-card">
                <h3>📊 최고 일관성</h3>
                <p>beomi_kcelectra</p>
            </div>
        </div>
        
        <div class="chart-container">
            <canvas id="performanceChart"></canvas>
        </div>
        
        <div class="chart-container">
            <canvas id="speedChart"></canvas>
        </div>
        
        <h2>📊 상세 메트릭</h2>
        <table class="model-table">
            <thead>
                <tr>
                    <th>모델</th>
                    <th>평균 점수</th>
                    <th>통과율 (%)</th>
                    <th>처리 시간 (ms)</th>
                    <th>처리량 (req/s)</th>
                    <th>일관성</th>
                </tr>
            </thead>
            <tbody>
                
                <tr class="">
                    <td>kr_sbert_default</td>
                    <td>0.366</td>
                    <td>0.0</td>
                    <td>1279.63</td>
                    <td>0.78</td>
                    <td>0.970</td>
                </tr>
            
                <tr class="">
                    <td>klue_roberta_base</td>
                    <td>0.415</td>
                    <td>0.0</td>
                    <td>767.18</td>
                    <td>1.30</td>
                    <td>0.976</td>
                </tr>
            
                <tr class="">
                    <td>klue_roberta_large</td>
                    <td>0.450</td>
                    <td>0.0</td>
                    <td>1093.75</td>
                    <td>0.91</td>
                    <td>0.976</td>
                </tr>
            
                <tr class="">
                    <td>beomi_kcelectra</td>
                    <td>0.449</td>
                    <td>0.0</td>
                    <td>708.82</td>
                    <td>1.41</td>
                    <td>0.980</td>
                </tr>
            
                <tr class="">
                    <td>koelectra_v3</td>
                    <td>0.468</td>
                    <td>33.3</td>
                    <td>739.84</td>
                    <td>1.35</td>
                    <td>0.977</td>
                </tr>
            
                <tr class="best-model">
                    <td>xlm_roberta_base</td>
                    <td>0.474</td>
                    <td>33.3</td>
                    <td>1542.78</td>
                    <td>0.65</td>
                    <td>0.977</td>
                </tr>
            
                <tr class="">
                    <td>multilingual_mini</td>
                    <td>0.374</td>
                    <td>0.0</td>
                    <td>1214.90</td>
                    <td>0.82</td>
                    <td>0.980</td>
                </tr>
            
            </tbody>
        </table>
        
        <div class="recommendations">
            <h2>💡 추천사항</h2>
            <ul>
                <li>🏆 전체 성능 최고: xlm_roberta_base</li><li>⚡ 처리 속도 최고: beomi_kcelectra</li><li>🔄 처리량 최고: beomi_kcelectra</li><li>📊 일관성 최고: beomi_kcelectra</li><li>📋 사용 시나리오별 추천:</li><li>• 실시간 처리: 처리 속도 최고 모델 사용</li><li>• 대용량 배치: 처리량 최고 모델 사용</li><li>• 고품질 분석: 전체 성능 최고 모델 사용</li><li>• 안정적 서비스: 일관성 최고 모델 사용</li>
            </ul>
        </div>
    </div>
    
    <script>
        // 성능 차트
        const performanceCtx = document.getElementById('performanceChart').getContext('2d');
        new Chart(performanceCtx, {
            type: 'bar',
            data: {"labels": ["kr_sbert_default", "klue_roberta_base", "klue_roberta_large", "beomi_kcelectra", "koelectra_v3", "xlm_roberta_base", "multilingual_mini"], "datasets": [{"label": "\ud3c9\uade0 \uc810\uc218", "data": [0.36559813077804576, 0.4149535545476499, 0.449849482927108, 0.4489540559579059, 0.46751343166062315, 0.47440124446610404, 0.3740769692863644], "backgroundColor": "rgba(54, 162, 235, 0.8)"}]},
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: '모델별 성능 비교'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 1
                    }
                }
            }
        });
        
        // 속도 차트
        const speedCtx = document.getElementById('speedChart').getContext('2d');
        new Chart(speedCtx, {
            type: 'bar',
            data: {"labels": ["kr_sbert_default", "klue_roberta_base", "klue_roberta_large", "beomi_kcelectra", "koelectra_v3", "xlm_roberta_base", "multilingual_mini"], "datasets": [{"label": "\ucc98\ub9ac \uc2dc\uac04 (ms)", "data": [1279.6342372894287, 767.1767870585123, 1093.7476952870686, 708.8159720102946, 739.8353417714437, 1542.7815914154053, 1214.8982683817546], "backgroundColor": "rgba(255, 99, 132, 0.8)"}]},
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: '모델별 처리 속도 비교'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    </script>
</body>
</html>
        