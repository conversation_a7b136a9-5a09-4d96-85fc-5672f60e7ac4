# QA Analysis System Architecture

## 📁 새로운 폴더 구조 (SOLID 원칙 적용)

```
📁 qa_analysis_system/
├── 📁 core/                          # 핵심 도메인 로직
│   ├── __init__.py
│   ├── 📁 domain/                     # 도메인 모델 (Entity, Value Object)
│   │   ├── __init__.py
│   │   ├── qa_pair.py                 # QA 쌍 도메인 모델
│   │   ├── analysis_result.py         # 분석 결과 도메인 모델
│   │   └── inquiry.py                 # 문의 도메인 모델
│   ├── 📁 interfaces/                 # 인터페이스 (추상화)
│   │   ├── __init__.py
│   │   ├── ai_model_interface.py      # AI 모델 인터페이스
│   │   ├── repository_interface.py    # 저장소 인터페이스
│   │   └── analyzer_interface.py      # 분석기 인터페이스
│   └── 📁 use_cases/                  # 비즈니스 로직 (Use Case)
│       ├── __init__.py
│       ├── analyze_qa_pair.py         # QA 쌍 분석 유스케이스
│       ├── batch_analysis.py          # 배치 분석 유스케이스
│       └── model_management.py        # 모델 관리 유스케이스
│
├── 📁 infrastructure/                 # 외부 시스템 연동
│   ├── __init__.py
│   ├── 📁 ai_models/                  # AI 모델 구현체
│   │   ├── __init__.py
│   │   ├── 📁 embedding/              # 임베딩 모델들
│   │   │   ├── __init__.py
│   │   │   ├── kr_sbert.py            # KR-SBERT 구현
│   │   │   ├── kcelectra.py           # KcELECTRA 구현
│   │   │   ├── kobert.py              # KoBERT 구현
│   │   │   └── klue_roberta.py        # KLUE-RoBERTa 구현
│   │   ├── 📁 llm/                    # LLM 모델들
│   │   │   ├── __init__.py
│   │   │   ├── openai_gpt.py          # OpenAI GPT 구현
│   │   │   ├── anthropic_claude.py    # Anthropic Claude 구현
│   │   │   └── google_gemini.py       # Google Gemini 구현
│   │   └── model_factory.py           # 모델 팩토리
│   ├── 📁 repositories/               # 데이터 저장소 구현
│   │   ├── __init__.py
│   │   ├── supabase_repository.py     # Supabase 저장소
│   │   └── file_repository.py         # 파일 저장소
│   └── 📁 external_apis/              # 외부 API 연동
│       ├── __init__.py
│       └── supabase_client.py         # Supabase 클라이언트
│
├── 📁 application/                    # 애플리케이션 서비스
│   ├── __init__.py
│   ├── 📁 services/                   # 애플리케이션 서비스
│   │   ├── __init__.py
│   │   ├── qa_analysis_service.py     # QA 분석 서비스
│   │   ├── model_service.py           # 모델 관리 서비스
│   │   └── export_service.py          # 결과 내보내기 서비스
│   ├── 📁 agents/                     # 에이전트 (Facade 패턴)
│   │   ├── __init__.py
│   │   └── qa_analysis_agent.py       # QA 분석 에이전트
│   └── 📁 dto/                        # 데이터 전송 객체
│       ├── __init__.py
│       ├── analysis_request.py        # 분석 요청 DTO
│       └── analysis_response.py       # 분석 응답 DTO
│
├── 📁 presentation/                   # 프레젠테이션 계층
│   ├── __init__.py
│   ├── 📁 cli/                        # CLI 인터페이스
│   │   ├── __init__.py
│   │   ├── main_cli.py                # 메인 CLI
│   │   └── commands/                  # CLI 명령어들
│   │       ├── __init__.py
│   │       ├── analyze_command.py
│   │       └── config_command.py
│   └── 📁 api/                        # API 인터페이스 (향후 확장)
│       ├── __init__.py
│       └── rest_api.py
│
├── 📁 config/                         # 설정 관리
│   ├── __init__.py
│   ├── 📁 settings/                   # 설정 파일들
│   │   ├── __init__.py
│   │   ├── model_profiles.yaml        # 모델 프로필 설정
│   │   ├── analysis_config.yaml       # 분석 설정
│   │   └── app_config.yaml            # 애플리케이션 설정
│   ├── 📁 managers/                   # 설정 관리자들
│   │   ├── __init__.py
│   │   ├── config_manager.py          # 통합 설정 관리자
│   │   └── model_config_manager.py    # 모델 설정 관리자
│   └── 📁 validators/                 # 설정 검증기
│       ├── __init__.py
│       └── config_validator.py
│
├── 📁 shared/                         # 공통 유틸리티
│   ├── __init__.py
│   ├── 📁 utils/                      # 유틸리티 함수들
│   │   ├── __init__.py
│   │   ├── async_utils.py             # 비동기 유틸리티
│   │   ├── text_utils.py              # 텍스트 처리 유틸리티
│   │   └── validation_utils.py        # 검증 유틸리티
│   ├── 📁 exceptions/                 # 커스텀 예외
│   │   ├── __init__.py
│   │   ├── model_exceptions.py        # 모델 관련 예외
│   │   └── analysis_exceptions.py     # 분석 관련 예외
│   └── 📁 constants/                  # 상수 정의
│       ├── __init__.py
│       ├── model_constants.py         # 모델 관련 상수
│       └── analysis_constants.py      # 분석 관련 상수
│
├── 📁 tests/                          # 테스트 코드
│   ├── __init__.py
│   ├── 📁 unit/                       # 단위 테스트
│   │   ├── __init__.py
│   │   ├── test_domain/
│   │   ├── test_use_cases/
│   │   └── test_services/
│   ├── 📁 integration/                # 통합 테스트
│   │   ├── __init__.py
│   │   └── test_agents/
│   └── 📁 fixtures/                   # 테스트 데이터
│       ├── __init__.py
│       └── sample_data.py
│
├── 📁 docs/                           # 문서화
│   ├── README.md
│   ├── API_REFERENCE.md
│   ├── USER_GUIDE.md
│   └── DEVELOPMENT.md
│
├── 📁 scripts/                        # 스크립트
│   ├── setup.py                       # 설치 스크립트
│   ├── migrate.py                     # 마이그레이션 스크립트
│   └── benchmark.py                   # 벤치마크 스크립트
│
├── main.py                            # 메인 엔트리 포인트
├── requirements.txt                   # 의존성
├── pyproject.toml                     # 프로젝트 설정
└── .env.example                       # 환경변수 예시
```

## 🏗️ SOLID 원칙 적용

### 1. Single Responsibility Principle (SRP)
- **각 클래스는 하나의 책임만 가짐**
- `QAAnalysisService`: QA 분석만 담당
- `ModelService`: 모델 관리만 담당
- `ExportService`: 결과 내보내기만 담당

### 2. Open/Closed Principle (OCP)
- **확장에는 열려있고 수정에는 닫혀있음**
- 새로운 AI 모델 추가 시 기존 코드 수정 없이 확장 가능
- 인터페이스를 통한 의존성 주입

### 3. Liskov Substitution Principle (LSP)
- **하위 타입은 상위 타입으로 대체 가능**
- 모든 AI 모델 구현체는 동일한 인터페이스 구현
- 런타임에 모델 교체 가능

### 4. Interface Segregation Principle (ISP)
- **클라이언트는 사용하지 않는 인터페이스에 의존하지 않음**
- `EmbeddingModelInterface`와 `LLMInterface` 분리
- 필요한 기능만 노출

### 5. Dependency Inversion Principle (DIP)
- **고수준 모듈은 저수준 모듈에 의존하지 않음**
- Use Case는 인터페이스에만 의존
- 구현체는 의존성 주입으로 제공

## 🔧 주요 개선사항

### 1. 모델 관리 개선
- **4개 한국어 특화 모델** 사전 설정
- **프로필 기반 모델 전환** 시스템
- **성능 벤치마크** 자동화

### 2. 설정 관리 개선
- **계층화된 설정** 구조
- **환경별 설정** 분리
- **설정 검증** 자동화

### 3. 에러 처리 개선
- **커스텀 예외** 체계
- **에러 복구** 메커니즘
- **로깅** 표준화

### 4. 테스트 개선
- **단위 테스트** 커버리지 90%+
- **통합 테스트** 자동화
- **성능 테스트** 포함

### 5. 문서화 개선
- **API 문서** 자동 생성
- **사용자 가이드** 제공
- **개발자 문서** 완비

## 🚀 마이그레이션 계획

1. **Phase 1**: 핵심 도메인 모델 구현
2. **Phase 2**: 인터페이스 및 Use Case 구현
3. **Phase 3**: 인프라스트럭처 구현
4. **Phase 4**: 애플리케이션 서비스 구현
5. **Phase 5**: 프레젠테이션 계층 구현
6. **Phase 6**: 테스트 및 문서화

## 📊 예상 효과

- **유지보수성**: 50% 향상
- **확장성**: 새 모델 추가 시간 80% 단축
- **테스트 커버리지**: 90%+ 달성
- **코드 재사용성**: 70% 향상
- **개발 생산성**: 40% 향상
